<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record model="security.role" id="role_manager">
            <field name="name">管理人员</field>
        </record>
        <record model="security.role" id="role_project_manager">
            <field name="name">项目执行人员</field>
        </record>
        <record model="security.role" id="role_database_manager">
            <field name="name">数据库管理人员</field>
        </record>
        <!-- <record id="view_carbon_roles_form" model="ir.ui.view">
            <field name="name">carbon.roles.form</field>
            <field name="model">security.role</field>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <group>
                            <field name="name"/>
                            <field name="user_ids"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>
        <record id="view_carbon_roles_tree" model="ir.ui.view">
            <field name="name">carbon.roles.tree</field>
            <field name="model">security.role</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                </tree>
            </field>
        </record>
        <record id="action_carbon_roles" model="ir.actions.act_window">
            <field name="name">角色</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">security.role</field>
            <field name="view_mode">tree,form</field>
            <field name="view_ids" eval="[(5, 0, 0),
                    (0, 0, {'view_mode': 'tree', 'view_id': ref('view_carbon_roles_tree')}),
                    (0, 0, {'view_mode': 'form', 'view_id': ref('view_carbon_roles_form')})]"/>
        </record> -->
    </data>
</odoo>

