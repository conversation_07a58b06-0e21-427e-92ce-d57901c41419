# -*- coding: utf-8 -*-
from odoo import fields, api, models
from odoo.exceptions import UserError, ValidationError

class CarbonProjectResult(models.Model):
    """
    计算结果
    """
    _name = 'carbon.project.result'
    _description = "计算结果"
    # _rec_name = 'name'

    project_id = fields.Many2one('carbon.project', '所属项目', ondelete='cascade')
    scheme_id = fields.Many2one('carbon.project.scheme', '所属方案', required=True, ondelete='cascade')
    stage_id = fields.Many2one('carbon.stage', string='阶段', required=True)
    res_all = fields.Char(string='总碳排放')
    res_area = fields.Char(string='单位面积碳排放强度')
    res_year = fields.Char(string='平均每年碳排放强度')
    res_area_year = fields.Char(string='单位面积年均碳排放强度')
    category_result = fields.Text(string='类别计算结果')








