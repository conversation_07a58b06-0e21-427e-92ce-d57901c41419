# -*- coding: utf-8 -*-
from odoo import fields, api, models
from odoo.exceptions import UserError, ValidationError

class MaterialLifeCycleInventory(models.Model):
    """
    材料生命周期清单
    """
    _name = 'material.life.cycle.inventory'
    _description = "材料生命周期清单"
    _rec_name = 'name'

  

    name = fields.Char(string='材料名称', required=True)
    code = fields.Char(string='编码', compute='com_code')
    carbon_factor = fields.Char(string='碳排放因子', required=True)
    unit_id = fields.Many2one('carbon.unit', '单位', ondelete='restrict', required=True)
    type_id = fields.Many2one('life.cycle.inventory.type', '所属类别', ondelete='cascade')
    inventory_id = fields.Many2one('life.cycle.inventory', '所属清单', ondelete='cascade' )
    composition_id = fields.Many2one('structural.layer.composition', '所属成分', required=True)


    def com_code(self):
        for record in self:
            if record.composition_id.code:
                record.code = f'{record.composition_id.code}-{record.id}'
            else:
                record.code = ''
    
