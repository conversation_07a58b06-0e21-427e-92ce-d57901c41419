# -*- coding: utf-8 -*-
from odoo import fields, api, models
from odoo.exceptions import UserError, ValidationError

class CarbonLifeCycleInventory(models.Model):
    """
    碳汇生命周期清单
    """
    _name = 'carbon.life.cycle.inventory'
    _description = "碳汇生命周期清单"
    _rec_name = 'name'

  

    name = fields.Char(string='名称', required=True)
    code = fields.Char(string='编码', compute='com_code')
    carbon_factor = fields.Char(string='碳排放因子', required=True)
    remark = fields.Char(string='备注')
    unit_id = fields.Many2one('carbon.unit', '单位', ondelete='restrict', required=True)
    material_id = fields.Many2one('material.life.cycle.inventory', '回收材料')
    type_id = fields.Many2one('life.cycle.inventory.type', '所属类别', ondelete='cascade')
    inventory_id = fields.Many2one('life.cycle.inventory', '所属清单', ondelete='cascade')


    def com_code(self):
        for record in self:
            record.code = f'F-TH-{record.id}'
    
