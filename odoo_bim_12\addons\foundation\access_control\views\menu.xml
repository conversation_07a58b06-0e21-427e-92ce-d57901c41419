<?xml version="1.0" encoding="utf-8"?>
<odoo>
      <data noupdate="1">
         <record model="ir.module.category" id="module_access_control">
            <field name="name">权限控制</field>
            <field name="description">权限管理</field>
        </record>
        <record model="res.groups" id="group_access_control_customer">
            <field name="name">客户</field>
            <field name="category_id" ref="module_access_control"/>
        </record>
        <record model="res.groups" id="group_access_control_manager">
            <field name="name">管理员</field>
            <field name="category_id" ref="module_access_control"/>
            <field name="implied_ids" eval="[(4, ref('access_control.group_access_control_customer'))]"/>
            <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
        </record>
    </data>
    <data>
        <template id="assets_backend" name="access_control_assets" inherit_id="web.assets_backend">
            <xpath expr="." position="inside">
                <script type="text/javascript" src="/access_control/static/src/js/operate_access.js"/>
                <script type="text/javascript" src="/access_control/static/src/js/operate_navigation_access.js"/>
            </xpath>
        </template>
        <record model="ir.actions.client" id="operate_access">
            <field name="name">配置路由权限</field>
            <field name="tag">operate_access</field>
        </record>
         <record model="ir.actions.client" id="operate_navigation_access">
            <field name="name">配置导航权限</field>
            <field name="tag">operate_navigation_access</field>
        </record>


        <menuitem id="menu_root_access_control" name="权限控制" web_icon="access_control,static/description/icon.png" groups="group_access_control_manager"/>

        <menuitem id="menu_operate_access" parent="menu_root_access_control" name="操作面板" sequence="0"/>
                  <menuitem id="menu_operate_access_routes" parent="menu_operate_access" name="配置路由权限" sequence="0"
                              action="operate_access"/>
                  <menuitem id="menu_operate_navigation_access_routes" parent="menu_operate_access" name="配置导航权限" sequence="1"
                              action="operate_navigation_access"/>
        <menuitem id="menu_root_access" name="权限" parent="menu_root_access_control" sequence="1"/>
            <menuitem id="menu_role_access_route" parent="menu_root_access" name="路由权限" sequence="1"
                      action="action_role_access_route"/>
            <menuitem id="menu_role_access_route_button" parent="menu_root_access" name="按钮权限" sequence="2"
                      action="action_role_access_route_button"/>
            <menuitem id="menu_role_access_navigation" parent="menu_root_access" name="导航权限" sequence="3"
                      action="action_role_access_navigation"/>
        <menuitem id="menu_access_route_config" parent="menu_root_access_control" name="路由配置" sequence="4"/>
                  <menuitem id="menu_access_route" parent="menu_access_route_config" name="路由" sequence="0" action="action_access_route"/> 
                  <menuitem id="menu_access_route_button" parent="menu_access_route_config" name="按钮" sequence="1" action="action_access_route_button"/>
                  <menuitem id="menu_access_navigation" parent="menu_access_route_config" name="导航" sequence="2" action="action_access_navigation"/>
    </data>
</odoo>