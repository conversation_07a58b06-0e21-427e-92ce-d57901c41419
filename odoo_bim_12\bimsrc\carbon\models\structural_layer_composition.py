# -*- coding: utf-8 -*-
from odoo import fields, api, models
from odoo.exceptions import UserError, ValidationError

class StructuralLayerComposition(models.Model):
    """
    结构层成分
    """
    _name = 'structural.layer.composition'
    _description = "结构层成分"
    _rec_name = 'name'
    _order = 'sequence'

  
    sequence = fields.Integer(string='序号')
    name = fields.Char(string='名称', required=True)
    code = fields.Char(string='编码规则', required=True)
    unit = fields.Char(string='单位')
    type = fields.Selection(
            selection=[
                ('checkbox', '多选'),
                ('radio', '单选'),
                ('fill', '填充')
            ], required=True, string='类型',
            default='checkbox')
    columns = fields.Text('多选表格列配置')
    max_length = fields.Integer('多选最大条目数')
    layer_ids = fields.Many2many('structural.layer', 'structural_layer_composition_layer','composition_id','layer_id', string="结构层", copy=False)


    def name_get(self):
        result = []
        for c in self:
            result.append((c.id, (f"{c.name}({'、'.join(c.layer_ids.mapped('name'))})")))
        return result
    
