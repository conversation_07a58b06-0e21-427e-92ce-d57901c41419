<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record model="ir.actions.act_window" id="action_role_access_route_button">
            <field name="name">按钮权限</field>
            <field name="res_model">role.access.route.button</field>
            <field name="view_mode">tree,form</field>
            <field name="target">current</field>
            <field name="help" type="html">
                <p class="oe_view_nocontent_create">创建第一条数据
                </p>
            </field>
        </record>
        <record id="role_access_route_button_form" model="ir.ui.view">
            <field name="name">role.access.route.button.form</field>
            <field name="model">role.access.route.button</field>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <group>
                            <field name="domId"/>
                            <field name="name"/>
                            <field name="button_id"/>
                            <field name="state"/>
                            <field name="role_route_id"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>
        <record id="role_access_route_button_tree" model="ir.ui.view">
            <field name="name">role.access.route.button.tree</field>
            <field name="model">role.access.route.button</field>
            <field name="arch" type="xml">
                <tree>
                     <field name="domId"/>
                     <field name="name"/>
                     <field name="button_id"/>
                     <field name="state"/>
                     <field name="role_route_id"/>
                </tree>
            </field>
        </record>
    </data>
</odoo>

