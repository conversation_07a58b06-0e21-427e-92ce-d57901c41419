<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <report
        id="action_carbon_project_report_rough_compare"
        model="carbon.project"
        string="方案比选报告"
        report_type="qweb-html"
        name="carbon.project_report_rough_compare"
    />
    <template id="project_report_rough_compare">
        <style>
        </style>
        <t t-call="web.html_container">
            <div class="page" style="padding:100px">
                <h1 class="title" style="text-align: center; font-weight: 700;">道路铺面碳排放计算分析报告</h1>
                <h2 class="title" style="text-align: center; font-weight: 700;">（方案比选报告）</h2>
                <div>
                    <div>项目名称：<t t-esc="project.name"></t></div>
                    <div>项目地点：<t t-esc="project.city_id.name"></t></div>
                    <div>编制人员：   （签名）</div>
                    <div>校对人员：   （签名）</div>
                    <div>审核人员：   （签名）</div>
                    <div>报告时间：<t t-esc="current_time"></t></div>
                </div>
                <br/>

                <h2 class="">一、项目基本信息</h2>
                <div>项目名称：<t t-esc="project.name"></t></div>
                <div>项目地点：<t t-esc="project.city_id.name"></t></div>
                <div>铺装面积：<t t-esc="project.area"></t>m²</div>

                <h2 class="">二、铺装方案</h2>
                <t t-foreach="schemes_data"  t-as="sc">
                    <h3 class="">2.<t t-esc="sc_index + 1" />  方案 <t t-esc="sc_index + 1" /> ：<t t-esc="sc.get('name')" /></h3>
                    <div>铺装结构：</div>
                      <table border="2px solid" width="100%">
                                <tbody border="1px solid">
                                    <t t-foreach="sc.get('constructs')"  t-as="cons">
                                        <tr border="1px solid">
                                            <!-- <td class="text-center" style="border:1px solid;" colspan='1' t-esc="cons.get('k')"/> -->
                                            <td class="text-center" style="border:1px solid;" colspan='1' t-esc="cons"/>
                                        </tr>
                                    </t>
                                </tbody>
                            </table>
                    <div>使用年限：<t t-esc="sc.get('life')"/>年</div>
                </t>
                
                <h2 class="">三、编制依据</h2>
                <ul>
                    <li>《环境管理-生命周期评价-原则与框架》（ISO 14040:2006，GB/T 24040:2008）</li>
                    <li>《环境管理-生命周期评价-要求与指南》（ISO 14044:2006，GB/T 24044:2008）</li>
                    <li>《IPCC 2006年国家温室气体清单指南 2019修订版》</li>
                    <li>《综合能耗计算通则》（GB/T 2589-2020）</li>
                    <li>《混凝土搅拌运输车》（GB/T 26408-2020）</li>
                    <li>《公路工程预算定额》（JTG/T 3832-2018）</li>
                    <li>《公路工程机械台班费用定额》（JTG/T 3833-2018）</li>
                    <li>《营运货车燃料消耗量限值及测量方法》（JT/T 719-2016）</li>
                    <li>《2019年度减排项目中国区域电网基准线排放因子》（中华人民共和国生态环境部2020-12-29）</li>
                </ul>

                <h2 class="">四、碳排放计算模型</h2>
                <h3 class="">4.1 计算范围</h3>
                <div>
                &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;通过追溯能源、原材料从生产到消亡的全过程，本软件建立了道路铺面的全生命周期温室气体排放计算模型。模型包括4个主要计算单元，分别是原材料单元、施工单元、拆除单元以及碳汇单元。原材料单元考虑模型输入材料的生产和加工过程的碳排放；施工单元针对沥青混合料在拌和厂拌合的过程，混合料的运输，以及铺路现场进行的摊铺和碾压等作业过程中的排放；拆除单元考虑路面寿命结束后的铣刨铲除以及废料外运；碳汇单元考虑采用回收材料的碳减排效益。
                </div>
                <h3 class="">4.2 计算方法</h3>
                <div>
                &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;不同温室气体的辐射效率和在大气中存在的时间有差异，其对全球变暖的影响有所不同。通过IPCC规定的以100年为温室效应分析年限的全球变暖潜值GWP100a，搭建不同材料与能源的碳排放因子库，将道路铺面生命周期中各环节涉及的各种能源与材料消耗量乘以对应的排放因子，计算出不同阶段相关活动的二氧化碳当量（CO₂e），以描述对全球变暖的影响。
                </div>
                <h3 class="">4.3 计算基础数据与假设</h3>
                <div>
                &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;模型计算所需的生命周期各环节基础数据及其假设如表1所示。
                </div>
                  <div style="text-align: center; font-weight: 700;">表1 计算基础数据与假设</div>
                  <table border="2px solid" width="100%" height="482px">
                                <thead>
                                    <tr border="1px solid" height="48.2px">
                                        <th class="text-center" style="border:1px solid;" width="10%">计算单元</th>
                                        <th class="text-center" style="border:1px solid;" width="20%">排放环节</th>
                                        <th class="text-center" style="border:1px solid;" width="30%">计算说明</th>
                                        <th class="text-center" style="border:1px solid;">假设</th>
                                    </tr>
                                </thead>
                                <tbody border="1px solid">
                                    <tr border="1px solid">
                                        <td class="text-center" style="border:1px solid;" colspan='1' t-esc="'原材料'"/>
                                        <td class="text-center" style="border:1px solid;" colspan='1' t-esc="'输入材料的生产、加工和分配过程'"/>
                                        <td class="text-center" style="border:1px solid;" colspan='1' t-esc="'根据调查建立各原材料生命周期清单，采用碳排放因子方法分别计算出沥青、矿料等主要原材料的生产碳排放，汇总得到原材料单元总碳排放。'"/>
                                        <td class="text-center" style="border:1px solid;" colspan='1' t-esc="''"/>
                                    </tr>
                                    <tr border="1px solid">
                                        <td class="text-center" style="border:1px solid;" colspan='1' t-esc="'建设'"/>
                                        <td class="text-center" style="border:1px solid;" colspan='1' t-esc="'混合料的输运过程以及建造机械活动'"/>
                                        <td class="text-center" style="border:1px solid;" colspan='1' t-esc="'应包括混合料拌合、运输以及铺面的摊铺与碾压等工序，软件参考规范《公路工程预算定额》（JTG/T 3833-2018）的机械能耗及定额台班数据建立建设机械碳排放因子库，根据输入施工机械信息计算得到建设单元碳排放。'"/>
                                        <td class="text-center" style="border:1px solid;" colspan='1' t-esc="'AC、SMA、EA沥青混凝土，水泥混凝土和基层/底基层材料选择15t以内自卸汽车运输；GA沥青混凝土采用专用车辆Cooker车进行运输，满载情况下（柴）油耗为14.02L/h，行驶速度设为50km/h。假定干燥前的集料含水率为4%，在离开干燥滚筒之后的含水率为0.2%，当温度升高到125℃时，水分从集料中全部蒸发；沥青和集料常温温度均设为25℃；SMA沥青混凝土和AC沥青混凝土加热温度控制在160℃，集料加热温度应控制在200℃，采用生产能力为320t/h以内的沥青混合料拌和设备、12.5m以内沥青混合料摊铺机以及5t以内振动压路机（双钢轮）、16～20t轮胎式压路机和10000L以内洒水汽车；EA沥青混凝土拌合设备同SMA沥青混凝土，此外还需采用专用混合设备将环氧沥青双组分按比例加热、添加以及混合，A组分加热温度控制为60℃，B组分加热温度控制为150℃，集料加热温度应控制在200℃，其余参数与SMA沥青混凝土相同；GA沥青混凝土拌合设备同SMA沥青混凝土，集料加热为290~320℃左右后称量，按照配合比加入适量矿粉进行干拌，干拌时间为15s，随后沥青喷入后湿拌60s，沥青加热温度为165℃，其余参数与改性SMA混合料相同；水泥混凝土施工采用3.0～9.0m滑模式水泥混凝土摊铺机、混凝土电动刻纹机、混凝土电动切缝机、250L以内强制式混凝土搅拌机以及10000L以内洒水汽车；基层/底基层施工采用120kW以内自行式平地机、12～15t光轮压路机、235kW自行式稳定土拌和机以及10000L以内洒水汽车。'"/>
                                    </tr>
                                    <tr border="1px solid">
                                        <td class="text-center" style="border:1px solid;" colspan='1' t-esc="'拆除'"/>
                                        <td class="text-center" style="border:1px solid;" colspan='1' t-esc="'拆除机械活动'"/>
                                        <td class="text-center" style="border:1px solid;" colspan='1' t-esc="'拆除单元仅计算分析拆除铺面时所用到的施工机械和废料外运到回收站所消耗的能耗，以及相应的碳排放量，计算方法同建设阶段。'"/>
                                        <td class="text-center" style="border:1px solid;" colspan='1' t-esc="'规范《JTG-T 3832-2018》中对铣刨机台班数进行了说明，本软件采用定额法对铣刨过程中的能耗和排放进行计算，铣刨后还需对钢桥面进行抛丸除锈处理后才能重新铺装，此部分根据工程量进行台班数换算。'"/>
                                    </tr>
                                    <tr border="1px solid">
                                        <td class="text-center" style="border:1px solid;" colspan='1' t-esc="'碳汇'"/>
                                        <td class="text-center" style="border:1px solid;" colspan='1' t-esc="'采用回收材料的碳减排效益'"/>
                                        <td class="text-center" style="border:1px solid;" colspan='1' t-esc="'碳汇单元考虑回收材料利用所避免的填埋垃圾场环节的碳排放，数据参考Ecoinvent数据库。'"/>
                                        <td class="text-center" style="border:1px solid;" colspan='1' t-esc="'假设该废弃材料仅可回收到路面工程建设，不与其他回收方式的碳效益进行对比。'"/>
                                    </tr>
                                </tbody>
                            </table>
            <svg class="watermark" xmlns="http://www.w3.org/2000/svg" width="200" height="200">
                <text x="0" y="15" font-size="20" fill="rgba(204,204,204,0.5)" style="transform: rotate(-29deg);">道路铺面碳排放计算管理平台</text>
            </svg>
                <h2 class="">五、碳排放计算结果</h2>
                <t t-foreach="schemes_data"  t-as="sc">
                    <h3 class="">5.<t t-esc="sc_index + 1" />  方案 <t t-esc="sc_index + 1" /> ：<t t-esc="sc.get('name')" /></h3>
                    <t t-foreach="sc.get('result_data')"  t-as="rec">
                        <h4 class="">5.<t t-esc="sc_index + 1" />.<t t-esc="rec_index + 1" /> <t t-esc="rec.get('name')" />单元碳排放分析</h4>
                        <t t-if="rec.get('name')=='原材料'">
                            <div>
                                &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;根据用户输入关键结构层参数，结合同类型道路铺面实际工程量清单，估算得原材料单元碳排放数据。按使用年限<t t-esc="sc.get('life')"></t>年计算，原材料单元的碳排放总量为<t t-esc="rec.get('res_all')"></t> tCO₂e，年均碳排放强度为<t t-esc="rec.get('res_year')"></t> tCO₂e/a，单位面积碳排放强度为<t t-esc="rec.get('res_area')"></t> kgCO₂e/m²，单位面积年均碳排放强度为<t t-esc="rec.get('res_area_year')"></t> kgCO₂e/(m²·a)。各类别材料碳排放数据如表<t t-esc="rec.get('table_idx')"></t>所示：
                            </div>
                        </t>
                        <!-- <t t-if="rec.get('name')=='运输'">
                            <div>
                                &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;根据用户输入关键结构层参数，结合同类型道路铺面实际工程量清单，估算得运输单元碳排放数据。按设计使用年限<t t-esc="sc.get('life')"></t>年计算，运输单元的碳排放总量为<t t-esc="rec.get('res_all')"></t> tCO₂e，年均碳排放强度为<t t-esc="rec.get('res_year')"></t> tCO₂e/a，单位面积碳排放强度为<t t-esc="rec.get('res_area')"></t> kgCO₂e/m²，单位面积年均碳排放强度为<t t-esc="rec.get('res_area_year')"></t> kgCO₂e/(m²·a)。
                            </div>
                        </t> -->
                        <t t-if="rec.get('name')=='施工'">
                            <div>
                                &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;按设计使用年限<t t-esc="sc.get('life')"></t>年计算，施工单元的碳排放总量为<t t-esc="rec.get('res_all')"></t> tCO₂e，年均碳排放强度为<t t-esc="rec.get('res_year')"></t> tCO₂e/a，单位面积碳排放强度为<t t-esc="rec.get('res_area')"></t> kgCO₂e/m²，单位面积年均碳排放强度为<t t-esc="rec.get('res_area_year')"></t> kgCO₂e/(m²·a)。各施工环节碳排放数据如表<t t-esc="rec.get('table_idx')"></t>所示：
                            </div>
                        </t>
                        <t t-if="rec.get('name')=='拆除'">
                            <div>
                                &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;根据用户输入关键结构层参数，结合同类型道路铺面实际工程量清单，估算得拆除单元碳排放数据。按设计使用年限<t t-esc="sc.get('life')"></t>年计算，拆除单元的碳排放总量为<t t-esc="rec.get('res_all')"></t> tCO₂e，年均碳排放强度为<t t-esc="rec.get('res_year')"></t> tCO₂e/a，单位面积碳排放强度为<t t-esc="rec.get('res_area')"></t> kgCO₂e/m²，单位面积年均碳排放强度为<t t-esc="rec.get('res_area_year')"></t> kgCO₂e/(m²·a)。各拆除环节碳排放数据如表<t t-esc="rec.get('table_idx')"></t>所示：
                            </div>
                        </t>
                        <t t-if="rec.get('name')=='碳汇'">
                            <div>
                                &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;根据用户输入关键结构层参数，结合同类型道路铺面实际工程量清单，估算得碳汇单元碳排放数据。按设计使用年限<t t-esc="sc.get('life')"></t>年计算，拆除单元的碳排放总量为<t t-esc="rec.get('res_all')"></t> tCO₂e，年均碳排放强度为<t t-esc="rec.get('res_year')"></t> tCO₂e/a，单位面积碳排放强度为<t t-esc="rec.get('res_area')"></t> kgCO₂e/m²，单位面积年均碳排放强度为<t t-esc="rec.get('res_area_year')"></t> kgCO₂e/(m²·a)。各碳汇环节碳排放数据如表<t t-esc="rec.get('table_idx')"></t>所示：
                            </div>
                        </t>
                          <div style="text-align: center; font-weight: 700;">表<t t-esc="rec.get('table_idx')" /> <t t-esc="rec.get('name')" />单元碳排放数据</div>
                          <table border="2px solid" width="100%">
                                <thead>
                                    <tr border="1px solid" height="48.2px">
                                        <th class="text-center" style="border:1px solid;" width="20%">类别</th>
                                        <th class="text-center" style="border:1px solid;" width="20%">总碳排放 tCO₂e</th>
                                        <th class="text-center" style="border:1px solid;" width="20%">年均碳排放强度 tCO₂e/a</th>
                                        <th class="text-center" style="border:1px solid;" width="20%">单位面积碳排放强度  kgCO₂e/m²</th>
                                        <th class="text-center" style="border:1px solid;" width="20%">单位面积年均碳排放强度 kgCO₂e/(m²·a)</th>
                                    </tr>
                                </thead>
                                <tbody border="1px solid">
                                    <t t-foreach="rec.get('category_data')"  t-as="category">
                                        <tr border="1px solid">
                                            <td class="text-center" style="border:1px solid;" colspan='1' t-esc="category.get('category')"/>
                                            <td class="text-center" style="border:1px solid;" colspan='1' t-esc="category.get('res_all')"/>
                                            <td class="text-center" style="border:1px solid;" colspan='1' t-esc="category.get('res_year')"/>
                                            <td class="text-center" style="border:1px solid;" colspan='1' t-esc="category.get('res_area')"/>
                                            <td class="text-center" style="border:1px solid;" colspan='1' t-esc="category.get('res_area_year')"/>
                                        </tr>
                                    </t>
                                    <tr border="1px solid">
                                        <td class="text-center" style="border:1px solid;" colspan='1' t-esc="'合计'"/>
                                        <td class="text-center" style="border:1px solid;" colspan='1' t-esc="rec.get('res_all')"/>
                                        <td class="text-center" style="border:1px solid;" colspan='1' t-esc="rec.get('res_year')"/>
                                        <td class="text-center" style="border:1px solid;" colspan='1' t-esc="rec.get('res_area')"/>
                                        <td class="text-center" style="border:1px solid;" colspan='1' t-esc="rec.get('res_area_year')"/>
                                    </tr>
                                </tbody>
                            </table>
                    </t>
                </t>
                <h3>5.<t t-esc="len(schemes_data) + 1" /> 铺面全生命周期碳排放量构成及分析</h3>
                <t t-foreach="images"  t-as="image">
                    <img t-att-src="image" />
                </t>
            </div>
        </t>
    </template>
</odoo>

