# REST服务注册

## 1. 服务注册中心Registry

`base_rest`模块安装完成后，它会维护一个所有REST服务的Registry。该服务中心维护了路由路径和服务集合之间的映射：

- key: `RestController`注册的`root_path`
- value: `RestServiceComponent`注册的collection名称

```python
class RestServicesRegistry(dict):
    """ Holds a registry of REST services where key is the root of the path on
    which the methods of your ` RestController`` are registred and value is the
    name of the collection on which your ``RestServiceComponent`` implementing
    the business logic of your service is registered."""
```

## 2. 服务注册中心Registry初始化

### 2.1 创建

Odoo在加载完所有模块后，会调用每个模型的`_register_hook`钩子函数。而`base_rest`模块正是利用这个时机去进行Registry的创建和初始化。

```python
class RestServiceRegistation(models.AbstractModel):
    ...

    @api.model_cr
    def _register_hook(self):
        ...
        services_registry = self._init_global_registry()
        ...
    
    ...
```

### 2.2 构建

```python
class RestServiceRegistation(models.AbstractModel):
    ...

    @api.model_cr
    def _register_hook(self):
        ...
        self.build_registry(services_registry)
        ...
    
    ...
```

构建服务注册中心：

- 搜索所有状态为`("installed", "to upgrade")`的模块
- 对于每个模块调用`load_services()`
  - 从`_rest_controllers_per_module`中读取对应模块的控制器信息列表
  - 对于每个控制器，将`root_path`作为key，控制器信息作为值存储在注册中心

上述算法中，每个控制器的信息如下所示：

```python
{
    "root_path": root_path,
    "collection_name": collection_name,
    "controller_class": cls,
}
```

关于`_rest_controllers_per_module`如何初始化，参见`RestControllerType`(与metaclass相关)。

### 2.3 路由

```python
class RestServiceRegistation(models.AbstractModel):
    ...

    @api.model_cr
    def _register_hook(self):
        ...
        self._build_controllers_routes(services_registry)

    ...

    def _build_controllers_routes(self, services_registry):
        for controller_def in services_registry.values():
            for service in self._get_services(controller_def["collection_name"]):
                RestApiMethodTransformer(service, controller_def).fix()
                self._build_controller(service, controller_def)
    
    ...

    def _build_controller(self, service, controller_def):
        base_controller_cls = controller_def["controller_class"]
        # build our new controller class
        ctrl_cls = RestApiServiceControllerGenerator(
            service, base_controller_cls
        ).generate()

        # generate an addon name used to register our new controller for
        # the current database
        addon_name = "{}_{}_{}".format(
            self.env.cr.dbname,
            service._collection.replace(".", "_"),
            service._usage.replace(".", "_"),
        )
        # put our new controller into the new addon module
        ctrl_cls.__module__ = "odoo.addons.{}".format(addon_name)

        # instruct the registry that our fake addon is part of the loaded
        # modules
        self.env.registry._init_modules.add(addon_name)

        # register our conroller into the list of available controllers
        name_class = ("{}.{}".format(ctrl_cls.__module__, ctrl_cls.__name__), ctrl_cls)
        http.controllers_per_module[addon_name].append(name_class)
    
    ...
```

`RestApiMethodTransformer`主要是针对在Service实现中，并没有使用`@restapi.method`装饰器的方法进行处理，为它们统一增加`@restapi.method`。

通过`RestApiServiceControllerGenerator`动态创建：

- 假的Odoo模块：模块名称为`<db name>_<collection name>_<service usage>`，并将该动态创建的模块加入到Odoo的模块注册中心中`self.env.registry`
- Odoo的控制器类`http.Controller`，并将该控制器类加入到上述动态创建的模块中
- 将控制器注册到`http`模块中，也即`http.controllers_per_module`

完成这步动作之后，也就完成了从`base_rest`控制器以及服务生成`http.controller`的所有过程，后续的路由处理依旧遵循Odoo核心代码的流程处理。
