from odoo.addons.base_rest import restapi
from odoo.addons.component.core import Component
from odoo.http import request
from odoo import http
import json
import time
import requests
import string
import datetime
import random
import uuid
import hashlib
import os
import hmac
import logging
import base64
import xml.etree.ElementTree as ET
_logger = logging.getLogger(__name__)
import xml.etree.ElementTree as ET


def xml_to_dict(xml_string):
    """
    将XML字符串转换为Python字典

    参数:
        xml_string (str): 要转换的XML格式字符串

    返回:
        dict: 转换后的字典
    """

    def _element_to_dict(element):
        """
        递归将XML元素转换为字典
        """
        # 如果元素没有子元素，直接返回其文本内容
        if not list(element):
            return element.text

        # 处理有子元素的情况
        result = {}
        for child in element:
            # 如果子元素已经在结果中存在，则转换为列表
            if child.tag in result:
                if isinstance(result[child.tag], list):
                    result[child.tag].append(_element_to_dict(child))
                else:
                    result[child.tag] = [result[child.tag], _element_to_dict(child)]
            else:
                result[child.tag] = _element_to_dict(child)

        # 添加属性（如果有）
        if element.attrib:
            if not result:  # 如果没有子元素只有属性
                result = element.attrib
            else:  # 既有子元素又有属性
                result['@attributes'] = element.attrib

        return result

    try:
        # 解析XML字符串
        root = ET.fromstring(xml_string)
        return {root.tag: _element_to_dict(root)}
    except ET.ParseError as e:
        raise ValueError(f"无效的XML格式: {e}")



def get_one_page_data(page, page_size , data):
    """

    :param self: 
    :param page_size: 每页数量
    :param page: 第几页
    :return: 第几页的数据
    """
    totalPages = int(len(data) / page_size) if len(data) % page_size == 0 else int(len(data) / page_size) + 1
    if page == totalPages:
        res = data[(page - 1) * page_size:]
    else:
        res = data[(page - 1) * page_size:page * page_size]
    return res,totalPages


def res_success(parent, data):
    """
    RESTAPI正确返回值
    """
    res = parent(partial=True, data=data)
    res.code = 0
    res.message = 'success'
    return res


class CarbonProjectServices(Component):
    _inherit = 'base.rest.service'
    _name = 'sms'
    _usage = 'sms'
    _collection = 'sms.services'
    _description = ""


    @restapi.method(
    [
        (['/sendsms'], 'POST')
    ],
    input_param=restapi.Datamodel("sendsms.param"),
    output_param=restapi.Datamodel("sendsms.response"),auth='public')
    def sendsms(self, param):
        """
        发送验证码
        """
        VerifyCode = self.env['verify.code'].sudo()

        phone = param.phone
        captcha_param = param.captchaVerifyParam
        ACCESS_KEY_ID = self.env["ir.config_parameter"].sudo().get_param("ACCESS_KEY_ID")
        ACCESS_KEY_SECRET = self.env["ir.config_parameter"].sudo().get_param("ACCESS_KEY_SECRET")
        sms_config = self.env["ir.config_parameter"].sudo().get_param("sms.config")
        # ACCESS_KEY_ID = "LTAI5tRRLpH5kaNouoA8r9po"
        # ACCESS_KEY_SECRET = "******************************"

        params = {
        "Action": "VerifyIntelligentCaptcha",
        "Version": "2023-03-05", # 接口版本，验证码2.0使用 2023-03-05
        "AccessKeyId": ACCESS_KEY_ID,
        "SignatureMethod": "HMAC-SHA1",
        "SignatureVersion": "1.0",
        "SignatureNonce": str(uuid.uuid4()), # 唯一随机数
        "Timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime()),
        "CaptchaVerifyParam": captcha_param, # 前端传来的验证参数
        "SceneId": "1hfw1r2d"
        }
        sorted_items = sorted(params.items(), key=lambda x: x[0])
        query_string = "&".join(f"{k}={requests.utils.quote(str(v), safe='')}" for
        k, v in sorted_items)
        # 构造待签名字符串
        string_to_sign = "GET&%2F&" + requests.utils.quote(query_string, safe='')
        # 计算HMAC-SHA1签名
        key = (ACCESS_KEY_SECRET + "&").encode('utf-8')
        signature = base64.b64encode(hmac.new(key, string_to_sign.encode('utf-8'),
        hashlib.sha1).digest()).decode()
        params["Signature"] = signature
        # try:
        api_url = "https://captcha.cn-shanghai.aliyuncs.com/"
        response = requests.get(api_url, params=params)
        print(response.content.decode())

        xml_data = response.content.decode()
        result = xml_to_dict(xml_data)
        print(result)
        if result.get('VerifyIntelligentCaptchaResponse').get('Result').get('VerifyResult') == 'true':
            #验证通过
            code = ''.join(random.choices('**********', k=6))
            sms_config = sms_config.replace('%',code).replace('$',phone)
            sms_config = json.loads(sms_config)
            # print(sms_config)
            active_platform = sms_config.get('active_platform')
            platform = sms_config.get(active_platform)
            url = platform.get('url')
            params = platform.get('params')
            # params = {
            #     'userid': '18721',
            #     'action': 'send',
            #     'account': '江苏点米谋士企业',
            #     'password': '123456',
            #     'mobile': phone,
            #     'content': f'【关爱通】欢迎使用，您的验证码：{code}，5分钟有效。',
            #     'sendTime': '',
            #     'extno': ''
            # }
            # url = 'http://47.106.229.82:8888/sms.aspx'
            resp = requests.post(url, data=params)

            VerifyCode.create({
                'phone': phone,
                'code': code,
                'token': str(int(time.time())) + ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(16)),
                'generation_time':str(int(time.time())),
                'is_used':False,
            })

            res = {
                'success': True,
                'message': '验证码已发送'
            }
        else:
            res = {
                'success': True,
                'message': '安全验证未通过'
            }

        Parent = self.env.datamodels["sendsms.response"]
        return res_success(Parent, res)


    @restapi.method(
    [
        (['/register'], 'POST')
    ],
    input_param=restapi.Datamodel("register.param"),
    output_param=restapi.Datamodel("register.response"),auth='public')
    def register(self, param):
        """
        注册
        """
        VerifyCode = self.env['verify.code'].sudo()
        ResUsers = self.env['res.users'].sudo()
        IrModelData = self.env['ir.model.data'].sudo()

        username = param.username
        phone = param.phone
        password = param.password
        verifycode = param.verifycode

        #验证验证码
        check_res = VerifyCode.check_verify_code(verifycode, phone)
        if check_res == 'ok':
            user = ResUsers.search(['|',('phone','=',phone),('name','=',username)])
            if user:
                res = {
                    'result': '用户已注册'
                }
            else:
                user = ResUsers.create({
                    'name':username,
                    'login':username,
                    'phone':phone,
                    'password_text':password,
                })
                user.write({
                    'password': password,
                    'security_role_ids': [IrModelData.xmlid_to_res_id('carbon.role_manager'), IrModelData.xmlid_to_res_id('carbon.role_project_manager'), IrModelData.xmlid_to_res_id('carbon.role_database_manager')]
                })
                user.bind_user_default_data()
                res = {
                    'result': '注册成功'
                }
        else:
            res = {
                'result': check_res
            }

        Parent = self.env.datamodels["register.response"]
        return res_success(Parent, res)
    
    @restapi.method(
    [
        (['/prelogin'], 'POST')
    ],
    input_param=restapi.Datamodel("prelogin.param"),
    output_param=restapi.Datamodel("prelogin.response"),auth='public')
    def prelogin(self, param):
        """
        预登录（检验该手机用户是否注册）
        """
        ResUsers = self.env['res.users'].sudo()

        phone = param.phone

        user = ResUsers.search([('phone','=',phone)])
        if user:
            res = {
                'success': True
            }
        else:
            res = {
                'success': False,
                'message': '手机未注册，请先注册'
            }
        Parent = self.env.datamodels["prelogin.response"]
        return res_success(Parent, res)

    @restapi.method(
    [
        (['/login'], 'POST')
    ],
    input_param=restapi.Datamodel("login.param"),
    output_param=restapi.Datamodel("login.response"),auth='public')
    def login(self, param):
        """
        登录（密码登录和短信登录）
        """
        ResUsers = self.env['res.users'].sudo()
        VerifyCode = self.env['verify.code'].sudo()
        LoginRecord = self.env['login.record'].sudo()

        passwordType = param.passwordType
        nameOrPhone = param.nameOrPhone
        phone = param.phone
        verifycode = param.verifycode

        def tologin(user, password, is_password):
            request.session.authenticate(request.db, user.login, password)
            request.httprequest.session.rotate = False
            this_sid = request.httprequest.session.sid

            # 获取客户端IP地址
            ip_address = request.httprequest.environ.get('REMOTE_ADDR')
            
            # 或者获取经过代理后的真实IP（如果有X-Forwarded-For头）
            if 'HTTP_X_FORWARDED_FOR' in request.httprequest.environ:
                ip_address = request.httprequest.environ['HTTP_X_FORWARDED_FOR'].split(',')[0].strip()

            LoginRecord.create({
                'user_id': user.id,
                'sid': this_sid,
                'login_ip': ip_address,
                'type': 'password' if is_password else 'verifycode',
                'login_time': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            })

            #判断最大会话数量
            max_session = self.env["ir.config_parameter"].sudo().get_param("max.session")
            if max_session:
                max_session = int(max_session)
            else:
                max_session = 1
            #获取用户session
            records = LoginRecord.search([('user_id','=',user.id)])
            session_list = []
            for r in records:
                _logger.info(r.login_time)
                if r.sid and r.sid != this_sid:
                    session_list.append(r.sid)
            session_list = list(set(session_list))
            _logger.info(session_list)

            #筛选未过期的session,判断是否在data中的sessions目录，如：werkzeug_a341f6c9273894327d0dbb0c588ef6e957f580bb.sess
            # 获取 session_store
            active_session_list = []
            session_store = http.root.session_store
            _logger.info('self.session_store')
            _logger.info(session_store.path)
            # httprequest.session = self.session_store.get(sid)
            # last_week = time.time() - 60 * 60 * 24 * 7
            for fname in os.listdir(session_store.path):
                path = os.path.join(session_store.path, fname)
                _logger.info(path)
                sid = path.split('werkzeug_')[1].split('.')[0]
                if sid in session_list:
                    active_session_list.append(sid)
            _logger.info(active_session_list)

        

            #需删除sid的数量
            del_len = len(active_session_list)  + 1 -  max_session
            for i in range(0,del_len):
                sid = active_session_list.pop()
                path = f'{session_store.path}/werkzeug_{sid}.sess'
                os.unlink(path)

            return this_sid

        if passwordType:
            user = ResUsers.search(['|',('name','=',nameOrPhone),('phone','=',nameOrPhone)],limit=1)
            password = param.password
            if user:
                try:
                    sid = tologin(user,password,passwordType)
                    res = {
                        'success': True,
                        'session': sid
                    }
                except:
                    res = {
                        'success': False,
                        'message': '密码错误'
                    }
            else:
                res = {
                    'success': False,
                    'message': '用户未注册'
                }
        else:
            user = ResUsers.search([('phone','=',phone)],limit=1)
            password = user.password_text
            if user:
                #验证验证码
                check_res = VerifyCode.check_verify_code(verifycode, phone)
                if check_res == 'ok':
                    try:
                        sid = tologin(user,password,passwordType)
                        res = {
                            'success': True,
                            'session': sid
                        }
                    except:
                        res = {
                            'success': False,
                            'message': '发生错误'
                        }
                else:
                    res = {
                        'success': False,
                        'message': check_res
                    }
            else:
                res = {
                    'success': False,
                    'message': '手机未注册'
                }

        Parent = self.env.datamodels["login.response"]
        return res_success(Parent, res)

    @restapi.method(
    [
        (['/password'], 'POST')
    ],
    input_param=restapi.Datamodel("password.param"),
    output_param=restapi.Datamodel("password.response"),auth='user')
    def password(self, param):
        """
        修改密码
        """
        user = request.env.user
        old_password = param.old_password
        new_password = param.new_password

        try:
            user.change_password(old_password, new_password)
            user.password_text = new_password
            res = {
                'result': 'Changed Success'
            }
        except:
            res = {
                'result': 'Old Password Verification Error'
            }
            
        Parent = self.env.datamodels["password.response"]
        return res_success(Parent, res)

   