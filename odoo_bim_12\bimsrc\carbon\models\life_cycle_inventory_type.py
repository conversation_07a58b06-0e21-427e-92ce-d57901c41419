# -*- coding: utf-8 -*-
from odoo import fields, api, models
from odoo.exceptions import UserError, ValidationError

class LifeCycleInventoryType(models.Model):
    """
    生命周期清单类别
    """
    _name = 'life.cycle.inventory.type'
    _description = "生命周期清单类别"
    _rec_name = 'name'

  
    name = fields.Char(string='名称', required=True)
    category = fields.Selection(selection=[
        ('material', '材料'),
        ('ys_mechanical', '运输机械'),
        ('sg_mechanical', '施工机械'),
        ('cc_mechanical', '拆除机械'),
        ('maintenance', '养护'),
        ('carbon', '碳汇')
    ], string='单元类型', required=True, default='material')
    inventory_id = fields.Many2one('life.cycle.inventory', '所属清单', ondelete='cascade')
    unit_ids = fields.Many2many('carbon.unit', 'inventory_type_unit','type_id','unit_id', string="单位", required=True)
    
