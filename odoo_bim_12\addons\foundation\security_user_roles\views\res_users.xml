<?xml version="1.0"?>
<odoo>

    <record id="view_users_simple_form" model="ir.ui.view">
        <field name="name">res.users.simplified.form.roles</field>
        <field name="model">res.users</field>
        <field name="inherit_id" ref="base.view_users_simple_form"/>
        <field name="arch" type="xml">
            <field name="company_id" position="after">
                <label for="security_role_ids"
                       class="oe_edit_only"
                       attrs="{'invisible': [('id', '>', 0)]}"
                       string="角色"
                />
                <field name="security_role_ids"
                       widget="many2many_tags"
                       options="{'color_field': 'color'}"
                       attrs="{'invisible': [('id', '>', 0)]}"
                   
                />
            </field>
        </field>
    </record>

    <record id="view_users_form" model="ir.ui.view">
        <field name="name">res.users.form.roles</field>
        <field name="model">res.users</field>
        <field name="inherit_id" ref="base.view_users_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[hasclass('oe_title')]/group" position="inside">
                <field name="security_role_ids"
                       widget="many2many_tags"
                       options="{'color_field': 'color'}"
                />
            </xpath>
            <button name="action_show_groups" position="before">
                <button name="action_create_role"
                        type="object"
                        class="oe_stat_button"
                        icon="fa-group"
                >
                    创建角色
                </button>
            </button>
        </field>
    </record>

    <record id="view_users_tree" model="ir.ui.view">
        <field name="name">res.users.tree.roles</field>
        <field name="model">res.users</field>
        <field name="inherit_id" ref="base.view_users_tree"/>
        <field name="arch" type="xml">
            <field name="login" position="after">
                <field name="security_role_ids"
                       widget="many2many_tags"
                       options="{'color_field': 'color'}"
                />
            </field>
        </field>
    </record>

    <record id="view_res_users_kanban" model="ir.ui.view">
        <field name="name">res.users.kanban</field>
        <field name="model">res.users</field>
        <field name="inherit_id" ref="base.view_res_users_kanban"/>
        <field name="arch" type="xml">
            <xpath expr="//div[hasclass('oe_kanban_details')]/ul" position="inside">
                <li>
                    <field name="security_role_ids"
                           widget="many2many_tags"
                           options="{'color_field': 'color'}"
                    />
                </li>
            </xpath>
        </field>
    </record>

    <record id="view_users_search" model="ir.ui.view">
        <field name="name">res.users.search.roles</field>
        <field name="model">res.users</field>
        <field name="inherit_id" ref="base.view_users_search"/>
        <field name="arch" type="xml">
            <field name="company_ids" position="after">
                <field name="security_role_ids"/>
            </field>
        </field>
    </record>

    <record id="action_res_users_only_form" model="ir.actions.act_window">
        <field name="name">Users</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">res.users</field>
        <field name="view_mode">form</field>
    </record>

</odoo>
