# -*- coding: utf-8 -*-
from odoo import fields, api, models
from odoo.exceptions import UserError, ValidationError

class CarbonUnit(models.Model):
    """
    单位
    """
    _name = 'carbon.unit'
    _description = "单位"
    _rec_name = 'name'
    
    _sql_constraints = [('name_unique', 'unique(name)', '单位名称不可重名!!!')]

  
    name = fields.Char(string='单位', required=True)
    type = fields.Selection(
        selection=[
            ('material', '材料'),
            ('mechanical', '机械'),
            ('maintenance', '养护')
        ],
        default='material', required=False)
   
    
