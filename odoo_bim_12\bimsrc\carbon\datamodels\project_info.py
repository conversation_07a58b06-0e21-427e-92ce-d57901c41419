# from marshmallow import fields

from odoo.addons.datamodel.core import Datamodel
from odoo.addons.datamodel import fields


# class CarbonProjectUsersRegisterParam(Datamodel):
#     _name = "carbon.project.users.register.param"

#     username = fields.String(required=True,description='用户名')
#     password = fields.String(required=True,description='密码')

# class CarbonProjectUsersRegisterResponse(Datamodel):
#     _name = "carbon.project.users.register.response"

#     code = fields.Integer(required=True)
#     message = fields.String(required=True, allow_none=False)
#     data = fields.NestedModel('carbon.project.users.register.data', many=False)


# class CarbonProjectUsersRegisterData(Datamodel):
#     _name = "carbon.project.users.register.data"

#     result = fields.String(required=True, description='结果')

#===============================================================

# class CarbonProjectUsersLoginResponse(Datamodel):
#     _name = "carbon.project.users.login.response"

#     code = fields.Integer(required=True)
#     message = fields.String(required=True, allow_none=False)
#     data = fields.NestedModel('carbon.project.users.login.data', many=False)


# class CarbonProjectUsersLoginData(Datamodel):
#     _name = "carbon.project.users.login.data"

#     result = fields.String(required=True, description='结果')
#     session = fields.String(description='Session ID')

#===============================================================
# class CarbonProjectUsersPasswordParam(Datamodel):
#     _name = "carbon.project.users.password.param"

#     old_password = fields.String(required=True,description='旧密码')
#     new_password = fields.String(required=True,description='新密码')

# class CarbonProjectUsersPasswordResponse(Datamodel):
#     _name = "carbon.project.users.password.response"

#     code = fields.Integer(required=True)
#     message = fields.String(required=True, allow_none=False)
#     data = fields.NestedModel('carbon.project.users.password.data', many=False)


# class CarbonProjectUsersPasswordData(Datamodel):
#     _name = "carbon.project.users.password.data"

#     result = fields.String(required=True, description='结果')

#===============================================================

class CarbonProjectUsersInfoParam(Datamodel):
    _name = "carbon.project.users.info.param"

    vals = fields.Dict(description='字段值')

class CarbonProjectUsersInfoResponse(Datamodel):
    _name = "carbon.project.users.info.response"

    code = fields.Integer(required=True)
    message = fields.String(required=True, allow_none=False)
    data = fields.NestedModel('carbon.project.users.info.data', many=False)


class CarbonProjectUsersInfoData(Datamodel):
    _name = "carbon.project.users.info.data"

    success = fields.Boolean(required=True, description='是否成功')
    message = fields.String()
    data = fields.NestedModel('carbon.project.users.info.data.data', many=False)

class CarbonProjectUsersInfoDataData(Datamodel):
    _name = "carbon.project.users.info.data.data"

    id = fields.Integer(required=True, description='用户ID')
    is_master = fields.Boolean(required=True, description='是否是主账号')
    name = fields.String(required=True, description='用户名')
    vip_level = fields.String(required=True, description='会员等级')
    expire_date = fields.String(required=True, description='到期日期')
    phone = fields.String(required=True, description='手机')
    email = fields.String(required=True, description='邮箱')

#===============================================================

class CarbonProjectUsersRolesResponse(Datamodel):
    _name = "carbon.project.users.roles.response"

    code = fields.Integer(required=True)
    message = fields.String(required=True, allow_none=False)
    data = fields.NestedModel('carbon.project.users.roles.data', many=True)


class CarbonProjectUsersRolesData(Datamodel):
    _name = "carbon.project.users.roles.data"

    role_id = fields.Integer(required=True, description='角色ID')
    role_name = fields.String(required=True, description='角色名称')


#===============================================================

class CarbonProjectUsersProjectsParam(Datamodel):
    _name = "carbon.project.users.projects.param"

    keyword = fields.String(description='搜索关键词')
    vals = fields.Dict(description='参数')
    curPage = fields.Integer(description='当前页')
    pageSize = fields.Integer(description='每页数量')


class CarbonProjectUsersProjectsResponse(Datamodel):
    _name = "carbon.project.users.projects.response"

    code = fields.Integer(required=True)
    message = fields.String(required=True, allow_none=False)
    data = fields.NestedModel('carbon.project.users.projects.data', many=False)


class CarbonProjectUsersProjectsData(Datamodel):
    _name = "carbon.project.users.projects.data"

    success = fields.Boolean(required=True, description='是否成功')
    curPage = fields.Integer(description='当前页')
    pageSize = fields.Integer(description='每页数量')
    total = fields.Integer(description='总数量')
    data = fields.NestedModel('carbon.project.users.projects.data.data', many=True)

class CarbonProjectUsersProjectsDataData(Datamodel):
    _name = "carbon.project.users.projects.data.data"

    sequence = fields.Integer(required=True, description='序号')
    id = fields.Integer(required=True, description='项目ID')
    city_id = fields.List(fields.Integer, required=True, description='城市ID')
    checked_stages = fields.List(fields.String, required=True, description='阶段名称列表')
    fine_checked_stages = fields.List(fields.String, required=True, description='阶段名称列表')
    name = fields.String(required=True, description='项目名称')
    location = fields.String(description='项目地点', required=True)
    is_completed = fields.Boolean(description='是否完成计算', required=True)
    calc_stage = fields.String(description='计算阶段')
    mode = fields.String(description='计算模式', required=True)
    life = fields.String(required=True, description='使用年限')
    area = fields.String(required=True, description='铺装面积')
    type = fields.String(required=True, description='铺装类型')
    has_fine_scheme = fields.Boolean(description='是否创建核算方案')
    has_rough_scheme = fields.Boolean(description='是否创建粗略方案')
    fine_report_id = fields.String(description='核算报告ID')
    can_fine_report = fields.Boolean(description='查看核算报告')
    can_compare_report = fields.Boolean(description='查看比选报告')
    # can_compare_fine_report = fields.Boolean(description='查看核算对比报告')
    schemes = fields.NestedModel('carbon.project.users.projects.schemes.data', many=True)

class CarbonProjectUsersProjectsSchemesData(Datamodel):
    _name = "carbon.project.users.projects.schemes.data"

    sequence = fields.String(required=True, description='序号')
    id = fields.Integer(required=True, description='方案ID')
    name = fields.String(required=True, description='方案名称')
    res_all = fields.String(required=True, description='总碳排放')
    res_area = fields.String(required=True, description='单位面积碳排放强度')
    res_year = fields.String(required=True, description='平均每年碳排放强度')
    res_area_year = fields.String(required=True, description='单位面积年均碳排放强度')
    select = fields.Boolean(required=True, description='选定')
    is_completed = fields.Boolean(required=True, description='计算完成')

#===============================================================

class CarbonProjectUsersProjectsIdResultParam(Datamodel):
    _name = "carbon.project.users.projects.id.result.param"

    stage_id = fields.Integer(description='阶段ID')
    scheme_id = fields.Integer(description='方案ID')
    # inventory_id = fields.Integer(description='清单ID')
    # number = fields.Integer(description='数量')


class CarbonProjectUsersProjectsIdResultResponse(Datamodel):
    _name = "carbon.project.users.projects.id.result.response"

    code = fields.Integer(required=True)
    message = fields.String(required=True, allow_none=False)
    data = fields.NestedModel('carbon.project.users.projects.id.result.data', many=False)


class CarbonProjectUsersProjectsIdResultData(Datamodel):
    _name = "carbon.project.users.projects.id.result.data"

    success = fields.Boolean(required=True, description='是否成功')
    project_info = fields.NestedModel('carbon.project.users.projects.id.result.project.info.data', many=False)
    res_all = fields.NestedModel('carbon.project.users.projects.id.result.data.data', many=False)
    res_area = fields.NestedModel('carbon.project.users.projects.id.result.data.data', many=False)
    res_year = fields.NestedModel('carbon.project.users.projects.id.result.data.data', many=False)
    res_area_year = fields.NestedModel('carbon.project.users.projects.id.result.data.data', many=False)
    stage_result = fields.Dict()
    stage_category = fields.Dict()
    

class CarbonProjectUsersProjectsIdResultProjectInfoData(Datamodel):
    _name = "carbon.project.users.projects.id.result.project.info.data"

    name = fields.String(string='项目名称', required=True)
    location = fields.String(string='项目地点', required=True)
    life = fields.String(string='使用年限', required=True)
    area = fields.String(string='铺装面积', required=True)
    mode = fields.String(string='模式', required=True)
    scheme_mode = fields.String(string='模式', required=True)

class CarbonProjectUsersProjectsIdResultDataData(Datamodel):
    _name = "carbon.project.users.projects.id.result.data.data"

    label = fields.String()
    unit = fields.String()
    value = fields.String(string='总数')
    z_value = fields.String()
    f_value = fields.String()
    bh_value = fields.String(string='拌合')
    tp_value = fields.String(string='摊铺')
    ny_value = fields.String(string='碾压')
    stage_result = fields.NestedModel('carbon.project.users.projects.id.result.data.data.data', many=True)

class CarbonProjectUsersProjectsIdResultDataDataData(Datamodel):
    _name = "carbon.project.users.projects.id.result.data.data.data"

    stage_id = fields.Integer(description='阶段ID')
    stage_name = fields.String(description='阶段名称')
    label = fields.String()
    unit = fields.String()
    value = fields.String()
    z_value = fields.String()
    f_value = fields.String()


#===============================================================

class CarbonProjectUsersProjectsIdResponse(Datamodel):
    _name = "carbon.project.users.projects.id.response"

    code = fields.Integer(required=True)
    message = fields.String(required=True, allow_none=False)
    data = fields.NestedModel('carbon.project.users.projects.id.data', many=False)

class CarbonProjectUsersProjectsIdData(Datamodel):
    _name = "carbon.project.users.projects.id.data"

    inventory_id = fields.Integer(required=True, description='清单ID')
    schemes = fields.List(fields.Dict, required=True, description='方案')
    stages = fields.List(fields.Dict, required=True, description='阶段')
    fine_stages = fields.List(fields.Dict, required=True, description='阶段')
    data = fields.Dict(required=True, allow_none=True, description='填报数据')
    project_info = fields.Dict(required=True, allow_none=True, description='项目信息')
    inventory_data = fields.Dict(required=True, allow_none=True, description='清单数据')


#===============================================================
class CarbonProjectUsersInventoriesParam(Datamodel):
    _name = "carbon.project.users.inventories.param"

    del_all = fields.Boolean(description='删除所有')
    is_active = fields.Boolean(description='生效')
    inventory_id = fields.Integer(description='清单ID')
    inventory_name = fields.String(description='清单名称')
    remark = fields.String(description='备注')
    keyword = fields.String(description='搜索关键词')
    curPage = fields.Integer(description='当前页')
    pageSize = fields.Integer(description='每页数量')

class CarbonProjectUsersInventoriesResponse(Datamodel):
    _name = "carbon.project.users.inventories.response"

    code = fields.Integer(required=True)
    message = fields.String(required=True, allow_none=False)
    data = fields.NestedModel('carbon.project.users.inventories.data', many=False)

class CarbonProjectUsersInventoriesData(Datamodel):
    _name = "carbon.project.users.inventories.data"

    success = fields.Boolean(required=True, description='是否成功')
    message = fields.String()
    curPage = fields.Integer(description='当前页')
    pageSize = fields.Integer(description='每页数量')
    total = fields.Integer(description='总数量')
    data = fields.NestedModel('carbon.project.users.inventories.data.data', many=True)

class CarbonProjectUsersInventoriesDataData(Datamodel):
    _name = "carbon.project.users.inventories.data.data"

    sequence = fields.Integer(required=True, description='序号')
    is_active = fields.Boolean(required=True, description='生效')
    inventory_id = fields.Integer(required=True, description='清单ID')
    inventory_name = fields.String(required=True, description='清单名称')
    remark = fields.String(required=True, description='备注')

#===============================================================
class CarbonProjectUsersProjectsIdSchemesParam(Datamodel):
    _name = "carbon.project.users.projects.id.schemes.param"

    id = fields.Integer(description='ID')
    select = fields.Boolean(description='选择')


class CarbonProjectUsersProjectsIdSchemesResponse(Datamodel):
    _name = "carbon.project.users.projects.id.schemes.response"

    code = fields.Integer(required=True)
    message = fields.String(required=True, allow_none=False)
    data = fields.NestedModel('carbon.project.users.projects.id.schemes.data', many=False)

class CarbonProjectUsersProjectsIdSchemesData(Datamodel):
    _name = "carbon.project.users.projects.id.schemes.data"

    success = fields.Boolean(required=True, description='是否成功')
    # data = fields.NestedModel('carbon.project.users.projects.id.schemes.data.data', many=True)

# class CarbonProjectUsersProjectsIdSchemesDataData(Datamodel):
#     _name = "carbon.project.users.projects.id.schemes.data.data"

#     sequence = fields.Integer(required=True, description='序号')
#     is_active = fields.Boolean(required=True, description='生效')
#     inventory_id = fields.Integer(required=True, description='清单ID')
#     inventory_name = fields.String(required=True, description='清单名称')
#     remark = fields.String(required=True, description='备注')

#===============================================================
class CarbonProjectUsersInventoriesDetailsParam(Datamodel):
    _name = "carbon.project.users.inventories.details.param"

    parent_id = fields.Integer(description='父清单ID')
    del_all = fields.Boolean(description='删除所有')
    type = fields.String(required=True, description='清单类型')
    vals = fields.Dict(description='字段值')

class CarbonProjectUsersInventoriesDetailsResponse(Datamodel):
    _name = "carbon.project.users.inventories.details.response"

    code = fields.Integer(required=True)
    message = fields.String(required=True, allow_none=False)
    data = fields.NestedModel('carbon.project.users.inventories.details.data', many=False)

class CarbonProjectUsersInventoriesDetailsData(Datamodel):
    _name = "carbon.project.users.inventories.details.data"

    success = fields.Boolean(required=True, description='是否成功')
    data = fields.NestedModel('carbon.project.users.inventories.details.data.data', many=False)

class CarbonProjectUsersInventoriesDetailsDataData(Datamodel):
    _name = "carbon.project.users.inventories.details.data.data"

    inventory_id = fields.Integer(required=True, description='清单ID')
    inventory_name = fields.String(required=True, description='清单名称')
    details = fields.List(fields.Dict, required=True, description='清单名称')


#===============================================================
class CarbonProjectUnitsParam(Datamodel):
    _name = "carbon.project.units.param"

    type = fields.String(description='单位类型')

class CarbonProjectUnitsResponse(Datamodel):
    _name = "carbon.project.units.response"

    code = fields.Integer(required=True)
    message = fields.String(required=True, allow_none=False)
    data = fields.NestedModel('carbon.project.units.response.data', many=True)

class CarbonProjectUnitsResponseData(Datamodel):
    _name = "carbon.project.units.response.data"

    name = fields.String(required=True, description='类别名称')
    id = fields.Integer(required=True, description='类别ID')
    units = fields.NestedModel('carbon.project.units.response.data.data', many=True)

class CarbonProjectUnitsResponseDataData(Datamodel):
    _name = "carbon.project.units.response.data.data"

    id = fields.Integer(required=True, description='单位ID')
    name = fields.String(required=True, description='单位名称')

#===============================================================

class CarbonProjectStagesResponse(Datamodel):
    _name = "carbon.project.stages.response"

    code = fields.Integer(required=True)
    message = fields.String(required=True, allow_none=False)
    data = fields.NestedModel('carbon.project.stages.response.data', many=True)

class CarbonProjectStagesResponseData(Datamodel):
    _name = "carbon.project.stages.response.data"

    id = fields.Integer(required=True, description='阶段ID')
    name = fields.String(required=True, description='阶段名称')
    
#===============================================================

class CarbonProjectStagesIdInventoriesResponse(Datamodel):
    _name = "carbon.project.stages.id.inventories.response"

    code = fields.Integer(required=True)
    message = fields.String(required=True, allow_none=False)
    data = fields.NestedModel('carbon.project.stages.id.inventories.response.data', many=True)

class CarbonProjectStagesIdInventoriesResponseData(Datamodel):
    _name = "carbon.project.stages.id.inventories.response.data"

    id = fields.Integer(required=True, description='清单ID')
    name = fields.String(required=True, description='清单名称')
    unit = fields.String(required=True, description='单位')
    carbon_factor = fields.String(required=True, description='因子系数')
    carbon_unit = fields.String(required=True, description='因子单位')


#===============================================================

class CarbonProjectCitysResponse(Datamodel):
    _name = "carbon.project.citys.response"

    code = fields.Integer(required=True)
    message = fields.String(required=True, allow_none=False)
    data = fields.NestedModel('carbon.project.citys.response.data', many=True)

class CarbonProjectCitysResponseData(Datamodel):
    _name = "carbon.project.citys.response.data"

    value = fields.Integer(required=True, description='省份ID')
    label = fields.String(required=True, description='省份名称')
    children = fields.NestedModel('carbon.project.citys.response.data.data', many=True)

class CarbonProjectCitysResponseDataData(Datamodel):
    _name = "carbon.project.citys.response.data.data"

    value = fields.Integer(required=True, description='城市ID')
    label = fields.String(required=True, description='城市名称')

#===============================================================

class CarbonProjectCompositionsResponse(Datamodel):
    _name = "carbon.project.compositions.response"

    code = fields.Integer(required=True)
    message = fields.String(required=True, allow_none=False)
    data = fields.NestedModel('carbon.project.compositions.response.data', many=True)

class CarbonProjectCompositionsResponseData(Datamodel):
    _name = "carbon.project.compositions.response.data"

    id = fields.Integer(required=True, description='结构层成分ID')
    name = fields.String(required=True, description='结构层成分名称')

#===============================================================

class CarbonProjectLayersResponse(Datamodel):
    _name = "carbon.project.layers.response"

    code = fields.Integer(required=True)
    message = fields.String(required=True, allow_none=False)
    data = fields.NestedModel('carbon.project.layers.response.data', many=True)

class CarbonProjectLayersResponseData(Datamodel):
    _name = "carbon.project.layers.response.data"

    id = fields.Integer(required=True, description='结构层ID')
    name = fields.String(required=True, description='结构层名称')

#===============================================================

class CarbonProjectLayersIdCompositionsParam(Datamodel):
    _name = "carbon.project.layers.id.compositions.param"

    inventory_id = fields.Integer(required=True, description='清单ID')

class CarbonProjectLayersIdCompositionsResponse(Datamodel):
    _name = "carbon.project.layers.id.compositions.response"

    code = fields.Integer(required=True)
    message = fields.String(required=True, allow_none=False)
    data = fields.NestedModel('carbon.project.layers.id.compositions.response.data', many=True)

class CarbonProjectLayersIdCompositionsResponseData(Datamodel):
    _name = "carbon.project.layers.id.compositions.response.data"

    id = fields.Integer(required=True, description='结构层成分ID')
    max_length = fields.Integer(description='多选最大条目数')
    name = fields.String(required=True, description='结构层成分名称')
    type = fields.String(required=True, description='类型')
    unit = fields.String(description='单位')
    columns = fields.List(fields.Dict, description='多选表格列配置')
    options = fields.List(fields.Dict, description='单选选项配置')

#===============================================================
class CarbonProjectGeojsonParam(Datamodel):
    _name = "carbon.project.geojson.param"

    type = fields.String(required=True, description='类型')
    adcode = fields.String(required=True, description='adcode')

class CarbonProjectGeojsonResponse(Datamodel):
    _name = "carbon.project.geojson.response"

    code = fields.Integer(required=True)
    message = fields.String(required=True, allow_none=False)
    data = fields.NestedModel('carbon.project.geojson.response.data', many=False)

class CarbonProjectGeojsonResponseData(Datamodel):
    _name = "carbon.project.geojson.response.data"

    geojson = fields.String(required=True, description='geojson')

#===============================================================
class CarbonProjectUsersChildsParam(Datamodel):
    _name = "carbon.project.users.childs.param"

    vals = fields.Dict(description='字段值')


class CarbonProjectUsersChildsResponse(Datamodel):
    _name = "carbon.project.users.childs.response"

    code = fields.Integer(required=True)
    message = fields.String(required=True, allow_none=False)
    data = fields.NestedModel('carbon.project.users.childs.response.data', many=False)

class CarbonProjectUsersChildsResponseData(Datamodel):
    _name = "carbon.project.users.childs.response.data"

    success = fields.Boolean(required=True, description='是否成功')
    result = fields.String(description='结果')
    has_database_manager = fields.Boolean(description='数据库管理员是否分配')
    database_manager = fields.String(description='数据库管理员用户名')
    is_master = fields.Boolean(description='是否是主账号')
    master_name = fields.String(description='主账号名称')
    database_manager_id = fields.Integer(description='数据库管理员ID')
    org_data = fields.Dict(description='架构图数据')
    data = fields.NestedModel('carbon.project.users.childs.response.data.data', many=True)

class CarbonProjectUsersChildsResponseDataData(Datamodel):
    _name = "carbon.project.users.childs.response.data.data"

    id = fields.Integer(required=True, description='子账号ID')
    name = fields.String(required=True, description='子账号名称')
    password = fields.String(description='子账号密码')
    roles = fields.NestedModel('carbon.project.users.childs.response.data.data.data', many=True)

class CarbonProjectUsersChildsResponseDataDataData(Datamodel):
    _name = "carbon.project.users.childs.response.data.data.data"

    id = fields.Integer(required=True, description='角色ID')
    name = fields.String(required=True, description='角色名称')


#===============================================================
class CarbonProjectUsersProjectsRankingParam(Datamodel):
    _name = "carbon.project.users.projects.ranking.param"

    type = fields.String(required=True, description='类型')
    user_id = fields.Integer(description='用户ID')

class CarbonProjectUsersProjectsRankingResponse(Datamodel):
    _name = "carbon.project.users.projects.ranking.response"

    code = fields.Integer(required=True)
    message = fields.String(required=True, allow_none=False)
    data = fields.NestedModel('carbon.project.users.projects.ranking.response.data', many=False)

class CarbonProjectUsersProjectsRankingResponseData(Datamodel):
    _name = "carbon.project.users.projects.ranking.response.data"

    x_data = fields.List(fields.String, required=True, description='横轴数据')
    y_data = fields.List(fields.String, required=True, description='纵轴数据')

#===============================================================
class CarbonProjectUsersProjectsOverviewParam(Datamodel):
    _name = "carbon.project.users.projects.overview.param"

    user_id = fields.Integer(description='用户ID')

class CarbonProjectUsersProjectsOverviewResponse(Datamodel):
    _name = "carbon.project.users.projects.overview.response"

    code = fields.Integer(required=True)
    message = fields.String(required=True, allow_none=False)
    data = fields.NestedModel('carbon.project.users.projects.overview.response.data', many=False)

class CarbonProjectUsersProjectsOverviewResponseData(Datamodel):
    _name = "carbon.project.users.projects.overview.response.data"

    total = fields.Integer(required=True, description='项目总数量')
    completed_number = fields.Integer(required=True, description='已完成项目数量')
    city_projects = fields.NestedModel('carbon.project.users.projects.overview.response.data.city', many=True , description='城市')
    completed_projects = fields.NestedModel('carbon.project.users.projects.overview.response.data.completed', many=True , description='已完成项目列表')

class CarbonProjectUsersProjectsOverviewResponseDataCity(Datamodel):
    _name = "carbon.project.users.projects.overview.response.data.city"

    id = fields.Integer(required=True, description='城市ID')
    name = fields.String(required=True, description='城市名称')
    adcode = fields.String(required=True, description='adcode')
    geojson = fields.String(required=True, description='geojson')
    projects = fields.NestedModel('carbon.project.users.projects.overview.response.data.completed', many=True , description='项目列表')

class CarbonProjectUsersProjectsOverviewResponseDataCompleted(Datamodel):
    _name = "carbon.project.users.projects.overview.response.data.completed"

    id = fields.Integer(required=True, description='项目ID')
    active_scheme_id = fields.Integer(description='方案ID')
    name = fields.String(required=True, description='项目名称')
    username = fields.String(required=True, description='所属账号')
    location = fields.String(required=True, description='项目地点')
    life = fields.String(required=True, description='使用年限')
    area = fields.String(required=True, description='铺装面积')
    geojson = fields.String(required=True, description='geojson')
    show_detail_btn = fields.Boolean(required=True, description='行详情按钮是否显示')

#===============================================================
class CarbonProjectVerifycodeParam(Datamodel):
    _name = "carbon.project.verifycode.param"

    vals = fields.Dict(description='字段值')

class CarbonProjectVerifycodeResponse(Datamodel):
    _name = "carbon.project.verifycode.response"

    code = fields.Integer(required=True)
    message = fields.String(required=True, allow_none=False)
    data = fields.NestedModel('carbon.project.verifycode.data', many=False)

class CarbonProjectVerifycodeData(Datamodel):
    _name = "carbon.project.verifycode.data"

    success = fields.Boolean(required=True, description='是否成功')
