<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_carbon_users_form" model="ir.ui.view">
            <field name="name">carbon.users.form</field>
            <field name="model">res.users</field>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <group>
                            <field name="name"/>
                            <field name="vip_level"/>
                            <field name="phone"/>
                            <field name="email"/>
                            <field name="expire_date"/>
                            <field name="parent_id"/>
                            <field name="security_role_ids" widget="many2many_tags"/>
                            <field name="child_ids"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>
        <record id="view_carbon_users_tree" model="ir.ui.view">
            <field name="name">carbon.users.tree</field>
            <field name="model">res.users</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                    <field name="vip_level"/>
                    <field name="phone"/>
                    <field name="email"/>
                    <field name="expire_date"/>
                    <field name="parent_id"/>
                    <!-- <field name="child_ids"/> -->
                </tree>
            </field>
        </record>
        <record id="action_carbon_users" model="ir.actions.act_window">
            <field name="name">用户</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">res.users</field>
            <field name="view_mode">tree,form</field>
            <field name="view_ids" eval="[(5, 0, 0),
                    (0, 0, {'view_mode': 'tree', 'view_id': ref('view_carbon_users_tree')}),
                    (0, 0, {'view_mode': 'form', 'view_id': ref('view_carbon_users_form')})]"/>
        </record>
    </data>
</odoo>

