.o_kanban_view.o_kanban_dashboard.o_account_kanban {
    .o_kanban_record {

        @include media-breakpoint-up(sm) {
            .oe_kanban_action_button {
                display: block;
                margin-bottom: 5px;
            }
        }

        .o_kanban_card_settings {
            padding-top: $o-horizontal-padding/2;
            padding-bottom: $o-horizontal-padding/2;

            border-top: 1px solid;
            border-color: $o-brand-lightsecondary;
        }
        .o_dashboard_star {
            font-size: 12px;

            &.fa-star-o {
                color: $o-main-color-muted;
                &:hover {
                    color: gold;
                }
            }
            &.fa-star {
                color: gold;
            }
        }

        .o_dashboard_graph {
            margin-bottom: -$o-horizontal-padding/2;
        }
    }

    &.o_kanban_ungrouped {
        .o_kanban_record {
            width: 450px;
        }
    }

    .o_sample_data_label {
        @include o-position-absolute($bottom: 25%, $right: 0);
        z-index: 1;
    }
    .o_kanban_group {
        &:not(.o_column_folded) {
            width: 450px + 2*$o-kanban-group-padding;

            @include media-breakpoint-down(sm) {
                width: 100%;
            }
        }
    }
}

// Style for the widget "dashboard_graph"
.o_dashboard_graph {
    position: relative;
    margin: 16px -16px;

    canvas {
        height: 75px;
    }

}

.o_sample_data .o_dashboard_graph.o_graph_linechart > svg g.nv-linesWrap g.nv-group.nv-series-0 {
    fill: gray !important;
    opacity: 0.1;
}
