<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record model="ir.actions.act_window" id="action_access_navigation">
            <field name="name">导航</field>
            <field name="res_model">access.navigation</field>
            <field name="view_mode">tree,form</field>
            <field name="target">current</field>
            <field name="help" type="html">
                <p class="oe_view_nocontent_create">创建第一条数据
                </p>
            </field>
        </record>
        <record id="access_navigation_form" model="ir.ui.view">
            <field name="name">access.navigation.form</field>
            <field name="model">access.navigation</field>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <group>
                            <field name="name"/>
                            <field name="path"/>
                            <field name="route_id"/>
                            <field name="sequence" required="1"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>
        <record id="access_navigation_tree" model="ir.ui.view">
            <field name="name">access.navigation.tree</field>
            <field name="model">access.navigation</field>
            <field name="arch" type="xml">
                <tree>
                     <field name="sequence" widget="handle"/>
                     <field name="name"/>
                     <field name="path"/>
                     <field name="route_id"/>
                     <field name="is_active" widget="boolean_toggle"/>
                </tree>
            </field>
        </record>
    </data>
</odoo>

