# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account
#
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-11-16 08:08+0000\n"
"PO-Revision-Date: 2017-11-16 08:08+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Spanish (Bolivia) (https://www.transifex.com/odoo/teams/41243/"
"es_BO/)\n"
"Language: es_BO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__create_date
#: model:ir.model.fields,field_description:account.field_account_account_tag__create_date
#: model:ir.model.fields,field_description:account.field_account_account_template__create_date
#: model:ir.model.fields,field_description:account.field_account_account_type__create_date
#: model:ir.model.fields,field_description:account.field_account_accrual_accounting_wizard__create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement__create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance__create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__create_date
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__create_date
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__create_date
#: model:ir.model.fields,field_description:account.field_account_chart_template__create_date
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__create_date
#: model:ir.model.fields,field_description:account.field_account_common_report__create_date
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__create_date
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__create_date
#: model:ir.model.fields,field_description:account.field_account_group__create_date
#: model:ir.model.fields,field_description:account.field_account_incoterms__create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_send__create_date
#: model:ir.model.fields,field_description:account.field_account_journal__create_date
#: model:ir.model.fields,field_description:account.field_account_journal_group__create_date
#: model:ir.model.fields,field_description:account.field_account_move__create_date
#: model:ir.model.fields,field_description:account.field_account_move_line__create_date
#: model:ir.model.fields,field_description:account.field_account_move_reversal__create_date
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__create_date
#: model:ir.model.fields,field_description:account.field_account_payment__create_date
#: model:ir.model.fields,field_description:account.field_account_payment_method__create_date
#: model:ir.model.fields,field_description:account.field_account_payment_register__create_date
#: model:ir.model.fields,field_description:account.field_account_payment_term__create_date
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__create_date
#: model:ir.model.fields,field_description:account.field_account_print_journal__create_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__create_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__create_date
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__create_date
#: model:ir.model.fields,field_description:account.field_account_tax__create_date
#: model:ir.model.fields,field_description:account.field_account_tax_group__create_date
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line__create_date
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line_template__create_date
#: model:ir.model.fields,field_description:account.field_account_tax_report_line__create_date
#: model:ir.model.fields,field_description:account.field_account_tax_template__create_date
#: model:ir.model.fields,field_description:account.field_account_unreconcile__create_date
#: model:ir.model.fields,field_description:account.field_cash_box_out__create_date
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__create_date
#: model:ir.model.fields,field_description:account.field_validate_account_move__create_date
msgid "Created on"
msgstr "Creado en"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_accrual_accounting_wizard__company_currency_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement__currency_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__currency_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__currency_id
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__currency_id
#: model:ir.model.fields,field_description:account.field_account_chart_template__currency_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__currency_id
#: model:ir.model.fields,field_description:account.field_account_journal__currency_id
#: model:ir.model.fields,field_description:account.field_account_move__currency_id
#: model:ir.model.fields,field_description:account.field_account_move_line__currency_id
#: model:ir.model.fields,field_description:account.field_account_move_reversal__currency_id
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__currency_id
#: model:ir.model.fields,field_description:account.field_account_payment__currency_id
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__currency_id
#: model:ir.model.fields,field_description:account.field_res_config_settings__currency_id
#: model:ir.model.fields,field_description:account.field_res_partner__currency_id
#: model:ir.model.fields,field_description:account.field_res_users__currency_id
#: model_terms:ir.ui.view,arch_db:account.report_journal
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "Currency"
msgstr "Divisa"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__state
msgid "Invoice Status"
msgstr "Estado de factura"

#. module: account
#: model:ir.ui.menu,name:account.account_invoicing_menu
#: model:ir.ui.menu,name:account.menu_finance
#: model_terms:ir.ui.view,arch_db:account.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Invoicing"
msgstr "Contabilidad"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__write_uid
#: model:ir.model.fields,field_description:account.field_account_account_tag__write_uid
#: model:ir.model.fields,field_description:account.field_account_account_template__write_uid
#: model:ir.model.fields,field_description:account.field_account_account_type__write_uid
#: model:ir.model.fields,field_description:account.field_account_accrual_accounting_wizard__write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement__write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance__write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__write_uid
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__write_uid
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__write_uid
#: model:ir.model.fields,field_description:account.field_account_chart_template__write_uid
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__write_uid
#: model:ir.model.fields,field_description:account.field_account_common_report__write_uid
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__write_uid
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__write_uid
#: model:ir.model.fields,field_description:account.field_account_group__write_uid
#: model:ir.model.fields,field_description:account.field_account_incoterms__write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_send__write_uid
#: model:ir.model.fields,field_description:account.field_account_journal__write_uid
#: model:ir.model.fields,field_description:account.field_account_journal_group__write_uid
#: model:ir.model.fields,field_description:account.field_account_move__write_uid
#: model:ir.model.fields,field_description:account.field_account_move_line__write_uid
#: model:ir.model.fields,field_description:account.field_account_move_reversal__write_uid
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__write_uid
#: model:ir.model.fields,field_description:account.field_account_payment__write_uid
#: model:ir.model.fields,field_description:account.field_account_payment_method__write_uid
#: model:ir.model.fields,field_description:account.field_account_payment_register__write_uid
#: model:ir.model.fields,field_description:account.field_account_payment_term__write_uid
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__write_uid
#: model:ir.model.fields,field_description:account.field_account_print_journal__write_uid
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__write_uid
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__write_uid
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__write_uid
#: model:ir.model.fields,field_description:account.field_account_tax__write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_group__write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line__write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line_template__write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_report_line__write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_template__write_uid
#: model:ir.model.fields,field_description:account.field_account_unreconcile__write_uid
#: model:ir.model.fields,field_description:account.field_cash_box_out__write_uid
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__write_uid
#: model:ir.model.fields,field_description:account.field_validate_account_move__write_uid
msgid "Last Updated by"
msgstr "Última actualización de"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__write_date
#: model:ir.model.fields,field_description:account.field_account_account_tag__write_date
#: model:ir.model.fields,field_description:account.field_account_account_template__write_date
#: model:ir.model.fields,field_description:account.field_account_account_type__write_date
#: model:ir.model.fields,field_description:account.field_account_accrual_accounting_wizard__write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement__write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance__write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__write_date
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__write_date
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__write_date
#: model:ir.model.fields,field_description:account.field_account_chart_template__write_date
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__write_date
#: model:ir.model.fields,field_description:account.field_account_common_report__write_date
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__write_date
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__write_date
#: model:ir.model.fields,field_description:account.field_account_group__write_date
#: model:ir.model.fields,field_description:account.field_account_incoterms__write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_send__write_date
#: model:ir.model.fields,field_description:account.field_account_journal__write_date
#: model:ir.model.fields,field_description:account.field_account_journal_group__write_date
#: model:ir.model.fields,field_description:account.field_account_move__write_date
#: model:ir.model.fields,field_description:account.field_account_move_line__write_date
#: model:ir.model.fields,field_description:account.field_account_move_reversal__write_date
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__write_date
#: model:ir.model.fields,field_description:account.field_account_payment__write_date
#: model:ir.model.fields,field_description:account.field_account_payment_method__write_date
#: model:ir.model.fields,field_description:account.field_account_payment_register__write_date
#: model:ir.model.fields,field_description:account.field_account_payment_term__write_date
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__write_date
#: model:ir.model.fields,field_description:account.field_account_print_journal__write_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__write_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__write_date
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__write_date
#: model:ir.model.fields,field_description:account.field_account_tax__write_date
#: model:ir.model.fields,field_description:account.field_account_tax_group__write_date
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line__write_date
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line_template__write_date
#: model:ir.model.fields,field_description:account.field_account_tax_report_line__write_date
#: model:ir.model.fields,field_description:account.field_account_tax_template__write_date
#: model:ir.model.fields,field_description:account.field_account_unreconcile__write_date
#: model:ir.model.fields,field_description:account.field_cash_box_out__write_date
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__write_date
#: model:ir.model.fields,field_description:account.field_validate_account_move__write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: account
#: model:ir.ui.menu,name:account.account_report_folder
#: model:ir.ui.menu,name:account.menu_finance_reports
msgid "Reporting"
msgstr "Informe"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__invoice_user_id
#: model:ir.model.fields,field_description:account.field_account_move__invoice_user_id
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Salesperson"
msgstr "Vendedor"

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "Tax ID"
msgstr "NIT"

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__price_unit
msgid "Unit Price"
msgstr "Precio unidad"
