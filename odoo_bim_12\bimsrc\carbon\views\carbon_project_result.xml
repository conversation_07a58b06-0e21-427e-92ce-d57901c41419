<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record model="ir.actions.act_window" id="action_carbon_project_result">
            <field name="name">计算结果</field>
            <field name="res_model">carbon.project.result</field>
            <field name="view_mode">tree,form</field>
            <field name="target">current</field>
            <field name="help" type="html">
                <p class="oe_view_nocontent_create">创建第一条数据
                </p>
            </field>
        </record>
        <record id="carbon_project_result_form" model="ir.ui.view">
            <field name="name">carbon.project.result.form</field>
            <field name="model">carbon.project.result</field>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <group>
                            <field name="project_id"/>
                            <field name="scheme_id"/>
                            <field name="stage_id"/>
                            <field name="category_result"/>
                            <field name="res_all"/>
                            <field name="res_area"/>
                            <field name="res_year"/>
                            <field name="res_area_year"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>
        <record id="carbon_project_result_tree" model="ir.ui.view">
            <field name="name">carbon.project.result.tree</field>
            <field name="model">carbon.project.result</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="project_id"/>
                    <field name="scheme_id"/>
                    <field name="stage_id"/>
                    <field name="category_result"/>
                    <field name="res_all"/>
                    <field name="res_area"/>
                    <field name="res_year"/>
                    <field name="res_area_year"/>
                </tree>
            </field>
        </record>
    </data>
</odoo>

