# -*- coding: utf-8 -*-
from odoo import fields, api, models
from odoo.exceptions import UserError, ValidationError
import time
class LoginRecord(models.Model):
    """
    登录记录
    """
    _name = 'login.record'
    _description = "登录记录"
    # _rec_name = 'name'

 
    type = fields.Selection(
    [
        ('password', '密码登录'),
        ('verifycode', '验证码登录'),
    ], string="登录方式", required=True)
    login_ip = fields.Char('登录IP', required=True)
    login_time = fields.Char('登录时间', required=True)
    sid = fields.Char('SessionID', required=True)
    user_id = fields.Many2one('res.users', string='所属账号', required=True, ondelete='cascade' )

    
  


   
    
    
