# -*- coding: utf-8 -*-
from odoo import fields, api, models
from odoo.exceptions import UserError, ValidationError
import time
class Verifycode(models.Model):
    """
    验证码
    """
    _name = 'verify.code'
    _description = "验证码"
    # _rec_name = 'name'

 
    phone = fields.Char(string="手机号码", required=True)
    code = fields.Char(string="验证码", required=True)
    token = fields.Char(string="token", required=True)
    generation_time = fields.Char(string="生成时间的时间戳", required=True)
    is_expire = fields.<PERSON><PERSON>an(string="是否过期", compute='com_is_expire')
    is_used = fields.<PERSON><PERSON>an(string="是否使用过")

    
    def com_is_expire(self):
        for record in self:
            current_time = time.time()
            elapsed_time = current_time - float(record.generation_time)
            if elapsed_time > 60 * 5:
                record.is_expire = True
            else:
                record.is_expire = False

    def check_verify_code(self, verifycode, phone):
        #验证验证码
        verifycode_record = self.search([('code', '=', verifycode),('phone', '=', phone)],limit=1)
        if verifycode_record:
            if not verifycode_record.is_used and not verifycode_record.is_expire:
                #验证通过
                verifycode_record.is_used = True
                return 'ok'
            else:
                return '验证码已过期或已使用'
        else:
            return '验证码不正确'



   
    
    
