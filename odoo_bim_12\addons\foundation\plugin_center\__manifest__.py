# -*- coding: utf-8 -*-
{
    'name': "插件中心",

    'summary': """
       插件中心
       """,

    'description': """
         插件中心
    """,
    'sequence': 200,

    'author': "周伟",
    'website': "",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/master/openerp/addons/base/module/module_data.xml
    # for the full list
    # 'category': 'zw',
    'category': 'Base Application',
    'version': '0.1',

    # any module necessary for this one to work correctly
    'depends': ['base','web'],

    # always loaded
    'data': [
        'views/assets.xml',
        # 'security/ir.model.access.csv',
        ],
    # only loaded in demonstration mode
    'demo': [
        # 'demo/demo.xml',
    ],
    'qweb': [
        # "static/src/xml/template.xml",
    ],
    'installable': True,
    'application': True,
    'auto_install': True,

}