# -*- coding: utf-8 -*-
from odoo import fields, api, models
from odoo.exceptions import UserError, ValidationError

class StructuralLayer(models.Model):
    """
    结构层
    """
    _name = 'structural.layer'
    _description = "结构层"
    _rec_name = 'name'

  

    name = fields.Char(string='名称', required=True)
    composition_ids = fields.Many2many('structural.layer.composition', 'structural_layer_composition_layer','layer_id','composition_id', string="成分", copy=False)
    
