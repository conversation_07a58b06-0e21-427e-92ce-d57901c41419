{
  // 参考: https://odoo-development.readthedocs.io/en/latest/ide/visual-studio-code/configuration.html
  // use this so the autocomplete/goto definition will work with python extension
  "python.autoComplete.extraPaths": [
    "${workspaceRoot}/odoo/addons",
    "${workspaceRoot}/odoo",
    "${workspaceRoot}/addons",
    "${workspaceRoot}/bimsrc",
    "${workspaceRoot}/thirdparty"
  ],

  // linting
  "python.linting.enabled": true,
  "python.linting.pycodestyleEnabled": true,
  "python.linting.pep8Enabled": true,
  //load the pylint_odoo: https://github.com/OCA/pylint-odoo
  //"python.linting.pylintArgs": ["--load-plugins", "pylint_odoo"],

  // formatting
  "python.formatting.provider": "yapf",

  // add this auto-save option so the pylint will sow errors while editing otherwise
  //it will only show the errors on file save
  "files.autoSave": "afterDelay",
  "files.autoSaveDelay": 500,
  // The following will hide the compiled file in the editor/ add other file to hide them from editor
  "files.exclude": {
    "**/__pycache__": true,
    "**/*.pyc": true,
    "node_modules": true
  },
  "cSpell.words": [
    "pyenv",
    "Odoo",
    "odoo",
    "odoorc",
    "eval",
    "virtualenv",
    "authing",
    "Postgresql",
    "bimsrc",
    "ztree",
    "enterp",
    "superbar",
    "autocomplete",
    "thirdparty",
    "pylint",
    "yapf",
    "mgmt",
    "pycodestyle"
  ]
}