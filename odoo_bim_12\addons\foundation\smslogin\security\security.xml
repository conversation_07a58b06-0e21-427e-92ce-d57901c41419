<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record model="ir.module.category" id="module_smslogin">
            <field name="name">短信登录</field>
            <field name="description">短信登录</field>
        </record>


        <record model="res.groups" id="group_smslogin_customer">
            <field name="name">只读</field>
            <field name="category_id" ref="module_smslogin"/>
        </record>
        <record model="res.groups" id="group_smslogin_manager">
            <field name="name">管理</field>
            <field name="category_id" ref="module_smslogin"/>
            <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
        </record>

    </data>
    <!-- <record id="account_comp_rule_knowcenter_qiniufile" model="ir.rule">
     <field name="name">Account multi-knowcenter_qiniufile</field>
     <field name="model_id" ref="model_knowcenter_qiniufile"/>
     <field name="global" eval="True"/>
     <field name="domain_force">['|',('company_id','=',False),('company_id','child_of',user.active_company_ids.ids)]</field>
    </record>

    <record id="account_comp_knowcenter_provider" model="ir.rule">
     <field name="name">Account multi-knowcenter_provider</field>
     <field name="model_id" ref="model_knowcenter_provider"/>
     <field name="global" eval="True"/>
     <field name="domain_force">['|',('company_id','=',False),('company_id','child_of',user.active_company_ids.ids)]</field>
    </record>

    <record id="account_comp_knowcenter_library" model="ir.rule">
     <field name="name">Account multi-knowcenter_library</field>
     <field name="model_id" ref="model_knowcenter_library"/>
     <field name="global" eval="True"/>
     <field name="domain_force">['|',('company_id','=',False),('company_id','child_of',user.active_company_ids.ids)]</field>
    </record>

    <record id="account_comp_knowcenter_attachment" model="ir.rule">
     <field name="name">Account multi-knowcenter_attachment</field>
     <field name="model_id" ref="model_knowcenter_attachment"/>
     <field name="global" eval="True"/>
     <field name="domain_force">['|',('company_id','=',False),('company_id','child_of',user.active_company_ids.ids)]</field>
    </record> -->

</odoo>