<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">

    <record forcecreate="True" id="ir_cron_security_roles1_action" model="ir.cron">
            <field name="name">角色超管运行</field>
            <field name="model_id" ref="model_security_role"/>
            <field name="state">code</field>
            <field name="code">
model.run_security_role()
            </field>
            <field eval="True" name="active"/>
            <field name="user_id" ref="base.user_root"/>
            <field name="interval_number">1</field>
            <field name="interval_type">hours</field>
            <field name="nextcall">2020-11-26 21:00:00</field>
            <field name="numbercall">-1</field>
            <field eval="True" name="doall"/>
            <field eval="7" name="priority"/>
        </record>


    </data>
</odoo>
