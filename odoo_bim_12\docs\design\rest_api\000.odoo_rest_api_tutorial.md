# RESTful API

## 1.问题

在Odoo中开发业务非常高效，但是如果要对业务提供RESTful API，则有较大的痛点：

1. API文档无法自动生成，必须手动编写；
2. API的参数和返回结果校验无可复用的机制；
3. API逻辑架构没有有效分离，所有的业务都集中在控制器代码中；
4. API源码的管理；

## 2.方案

Odoo开源社区组织[OCA](https://odoo-community.org/)为社区提供了非常丰富的：

- 业务模块
- 功能模块
- 基础模块

关于REST API，社区有一个较为完整的解决方案：https://github.com/OCA/rest-framework

该框架涵盖了本文上面提到的所有问题，以及GraphQL/API调用日志等功能：

![2021-02-21-16-19-42](http://markdown.docs.lyon.pub/2021-02-21-16-19-42.png)

## 3. 代码

相关模块代码已经被引入后端`odoo`源码库，位于以下目录：

- addons/api
- addons/connector

以上两个目录下的模块都是从开源仓库`12.0`分支中提取，并做了以下修改：

- 升级了`swagger-ui`的版本到3.43.0；
- 修复了`swagger-ui`集成到Odoo中，在浏览器中无法上下滚动的问题；

另外，提供了一个如何使用`rest-framework`的示例模块: `ping`。该模块代码位于：

- addons/foundation/ping

## 4. 使用

启动`odoo`后，安装模块`ping`，成功后如下图所示，增加了`REST api`图标:

![2021-02-21-16-25-05](http://markdown.docs.lyon.pub/2021-02-21-16-25-05.png)

点击`REST api`图标，并在右上角选择`api/v2: ping`:

![2021-02-21-16-26-32](http://markdown.docs.lyon.pub/2021-02-21-16-26-32.png)

此时系统进入Swagger UI的界面，在该界面中我们可以：

- 查看对应的API及说明
- `Try it out`来进行接口调用

关于Swagger UI工具的详细使用，可以进行互联网搜索。

## 5. 开发

以下小节描述如何基于`rest-framework`开发模块的REST接口。

### 5.1 代码目录

在原有模块目录下，新增了两个目录：

- datamodels: 校验参数和返回结果的数据模型
- services: API实现的业务逻辑

![2021-02-21-16-32-24](http://markdown.docs.lyon.pub/2021-02-21-16-32-24.png)

在模块的`__init__.py`中，引入上述两个目录/模块:

```python
# -*- coding: utf-8 -*-

from . import controllers
from . import models
from . import services
from . import datamodels
```

### 5.2 控制器Controller和服务Service

在`rest-framework`中，所有的业务接口逻辑在服务中实现，而控制器只是胶水棒将路由与服务连接在一起。

#### 5.2.1 服务定义

定义一个服务包括以下步骤：

- 创建一个Python类，该类必须派生自`Component`
- 类名遵循驼峰命名法，以Service结尾
- 通过`_inherit`继承`base.rest.service`
- `_name`表示该服务的名称
- `_usage`表示在URI中的名称
- `_collection`表示该服务所归属的服务集合，和控制器中的`_collection_name`对应
- `_description`表示该服务的描述，在Swagger UI中呈现

以`ping`模块的`PingService`为例：

```python
from odoo.addons.base_rest import restapi
from odoo.addons.component.core import Component


class PingService(Component):
    _inherit = 'base.rest.service'
    _name = 'ping.service'
    _usage = 'ping'
    _collection = 'ping.services'
    _description = "Ping Service"

    ...
```

#### 5.2.2 控制器

定义一个控制器包括以下步骤：

- 创建一个Python类，该类必须派生自`main.RestController`
- 类名遵循驼峰命名法，以Controller结尾
- `_root_path`定义该服务接口的基础路径
- `_collection_name`代表了该控制器对应的服务集合

```python
# -*- coding: utf-8 -*-
from odoo.addons.base_rest.controllers import main


class PingController(main.RestController):
    _root_path = '/api/v2/'
    _collection_name = 'ping.services'
```

#### 5.2.3 方法

创建控制器类和服务类，只是为REST接口实现提供了一个基础框架，我们还需要在服务中定义具体的方法来响应接口的调用。

定义一个接口方法包括：

- 定义一个函数
- 通过装饰器`@restapi.method`描述接口：这个描述会被用来生成OpenAPI Spec
  - 第一个参数是一个列表，用来描述该方法支持的HTTP路径和HTTP方法
  - `input_param`用来描述输入参数的模型
  - `output_param`用来描述返回结果对应的模型
  - `auth`用来指明鉴权的方法,`public`代表无需鉴权

```python
...

class PingService(Component):
    ...

    @restapi.method(
        [
            (['/'], 'GET'),
        ],
        input_param=restapi.Datamodel("ping.message.param"),
        output_param=restapi.Datamodel("ping.message.response"),
        auth='public')
    def pong(self, ping_message_param):
        PingMessageResponse = self.env.datamodels["ping.message.response"]
        ping_message_response = PingMessageResponse(partial=True)
        ping_message_response.message = f"{ping_message_param.message}"

        return ping_message_response
```

#### 5.2.4 数据模型

数据模型的定义和Odoo中模型定义类似，区别：

- 集成自`Datamodel`
- `fields`的定义来自于`marshmallow`包

```python
from marshmallow import fields

from odoo.addons.datamodel.core import Datamodel


class PingMessageParam(Datamodel):
    _name = "ping.message.param"

    message = fields.String(required=True, allow_none=False)


class PingMessageResponse(Datamodel):
    _name = "ping.message.response"

    message = fields.String(required=True, allow_none=False)

```

### 5.3 完整流程

前提条件：

- 安装`ping`模块

1. Odoo启动时，`base_rest`模块会加载所有模块中定义的控制器、服务、数据模型
2. 点击`REST api`模块图标，
    - `base_rest`模块根据当前所有注册的控制器，生成`api_urls`，并通过QWeb引擎渲染Swagger UI页面
    - 浏览器打开Swagger UI页面
    - 浏览器向`odoo`请求`api_urls`对应的API规范文档(json格式)
    - `base_rest`根据控制器、服务以及每个接口方法中装饰器提供的信息生成API规范文档，并返回
3. 在Swagger UI中点击`Try it out`进行接口调用，`base_rest`会将请求路由给对应的接口方法，并处理返回结果

## 遗留问题

1. 删除数据库时，当Odoo重新创建新数据库`base_rest`模块会报错。 解决方法：中止Odoo运行，并重新启动Odoo
