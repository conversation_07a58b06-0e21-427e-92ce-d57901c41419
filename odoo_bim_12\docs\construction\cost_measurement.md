# /models/{id}/sections

## URI

**URI**: /models/{id}/sections

## 支持的方法

GET

## 介绍

获取所有的标段信息 GET

## HTTP方法

### GET

请求参数：

无

响应结构：

| 字段 | 类型 | 说明 |
|----|----|----|
| name   | string   | 标段名字    |
| id   |Number    |标段id    |


示例：
```
{
 "jsonrpc":2.0,
 "id":null,
 "result":{
           "code":0,
           "message":"xxx",
           "data":{
                   "title":"位置",
                   "data":[{
                           "name":"II",
                           "id":1
                              },
                           {
                           "name":"III",
                           "id":2
                              },
                            ]
                 }   
          }
}

```

# /models/{id}/pits

## URI

**URI**: /models/{id}/pits

## 支持的方法

GET

## 介绍

获取所有的基坑信息 GET

## HTTP方法

### GET

请求参数：

无

响应结构：

| 字段 | 类型 | 说明 |
|----|----|----|
| name   | string   | 基坑名字    |
| id   |Number    |基坑id    |


示例：
```
{
 "jsonrpc":2.0,
 "id":null,
 "result":{
           "code":0,
           "message":"xxx",
           "data":{
                   "title":"位置",
                   "data":[{
                           "name":"II-1",
                           "id":11
                              },
                           {
                           "name":"III-2",
                           "id":22
                              },
                            ]
                 }   
          }
}

```

# /models/{id}/segments

## URI

**URI**: /models/{id}/segments

## 支持的方法

GET

## 介绍

获取所有的节段信息 GET

## HTTP方法

### GET

请求参数：

无

响应结构：

| 字段 | 类型 | 说明 |
|----|----|----|
| name   | string   | 节段名字    |
| id   |Number    |节段id    |


示例：
```
{
 "jsonrpc":2.0,
 "id":null,
 "result":{
           "code":0,
           "message":"xxx",
           "data":{
                   "title":"位置",
                   "data":[{
                           "name":"节段名1",
                           "id":12
                              },
                           {
                           "name":"节段名2",
                           "id":23
                              },
                            ]
                 }   
          }
}

```

# /models/{id}/sections/search

## URI

**URI**: /models/{id}/sections/search

## 支持的方法

POST

## 介绍

根据关键字搜索标段 POST

## HTTP方法

### POST

请求参数：
```
{"keyword":"I",
 "pageSize":2,//代表每页的数据对象数量 （必填）
 "page":5,//代表对应的页码（必填）}

```
响应结构：

| 字段 | 类型 | 说明 |
|----|----|----|
| name   | string   | 标段名字    |
| id   |Number    | 标段id    |
| total   | Number   | 总数据量   |
| count  |Number    |本页实际的数据条目数    |
| prev  | Number   | 上一页的页码    |
| page  | Number   | 当前页的页码    |
| next  |Number    | 下一页的页码    |
| totalPages   | Number  | 总共页的数量    |
| pageSize  |Number    |每页的数据对象数量    |



示例：
```
{
 "jsonrpc":2.0,
 "id":null,
 "result":{
           "code":0,
           "message":"xxx",
           "data":{
                   "total": 100,//总数据量
                   "count": 5,//本页实际的数据条目数
                   "prev": 2, //上一页的页码
                   "page": 3, //当前页的页码
                   "next": 4, //下一页的页码
                   "totalPages": 15,//总共页的数量
                   "pageSize": 10, //每页的数据对象数量
                    "data":[{
                            "name":"标段名1",
                            "id":12
                            },
                            {
                            "name":"标段名2",
                            "id":23
                            }
                            ]
           }
          }
}
```

# /models/{id}/pits/search

## URI

**URI**: /models/{id}/pits/search

## 支持的方法

POST

## 介绍

根据关键字搜索基坑 POST

## HTTP方法

### POST

请求参数：
```

{"keyword":"II-1",
 "pageSize":2,//代表每页的数据对象数量 （必填）
 "page":5,//代表对应的页码（必填）}

```
响应结构：

| 字段 | 类型 | 说明 |
|----|----|----|
| name   | string   | 基坑名字    |
| id   |Number    |基坑id    |
| total   | Number   | 总数据量   |
| count  |Number    |本页实际的数据条目数    |
| prev  | Number   | 上一页的页码    |
| page  | Number   | 当前页的页码    |
| next  |Number    | 下一页的页码    |
| totalPages   | Number  | 总共页的数量    |
| pageSize  |Number    |每页的数据对象数量    |

示例：
```
{
 "jsonrpc":2.0,
 "id":null,
 "result":{
           "code":0,
           "message":"xxx",
           "data":{
                   "total": 100,//总数据量
                   "count": 5,//本页实际的数据条目数
                   "prev": 2, //上一页的页码
                   "page": 3, //当前页的页码
                   "next": 4, //下一页的页码
                   "totalPages": 15,//总共页的数量
                   "pageSize": 10, //每页的数据对象数量
                    "data":[{
                            "name":"基坑名1",
                            "id":12
                            },
                            {
                            "name":"基坑名2",
                            "id":23
                            }
                            ]
           }
          }
}
```

# /models/{id}/segments/search

## URI

**URI**: /models/{id}/segments/search

## 支持的方法

POST

## 介绍

根据关键字搜索节段 POST

## HTTP方法

### POST

请求参数：
```

{"keyword":"wh",
"pageSize":2,//代表每页的数据对象数量 （必填）
 "page":5,//代表对应的页码（必填）}}

```
响应结构：

| 字段 | 类型 | 说明 |
|----|----|----|
| name   | string   | 节段名字    |
| id   |Number    |节段id    |
| total   | Number   | 总数据量   |
| count  |Number    |本页实际的数据条目数    |
| prev  | Number   | 上一页的页码    |
| page  | Number   | 当前页的页码    |
| next  |Number    | 下一页的页码    |
| totalPages   | Number  | 总共页的数量    |
| pageSize  |Number    |每页的数据对象数量    |


示例：
```
{
 "jsonrpc":2.0,
 "id":null,
 "result":{
           "code":0,
           "message":"xxx",
           "data":{
                   "total": 100,//总数据量
                   "count": 5,//本页实际的数据条目数
                   "prev": 2, //上一页的页码
                   "page": 3, //当前页的页码
                   "next": 4, //下一页的页码
                   "totalPages": 15,//总共页的数量
                   "pageSize": 10, //每页的数据对象数量
                    "data":[{
                            "name":"节段名1",
                            "id":12
                            },
                            {
                            "name":"节段名2",
                            "id":23
                            }
                            ]
           }
          }
}
```

#此处复用前面一个构件搜索接口


# /projects/cost/planned_material_listings/sections/{id}

## URI

**URI**: /projects/cost/planned_material_listings/sections/{id}

## 支持的方法

GET

## 介绍

获取指定标段的计量清单信息 GET

## HTTP方法

### GET

请求参数：

无

响应结构：

| 字段 | 类型 | 说明 |
|----|----|----|
| boundingBox   |Array    |二维包围盒，用来显示对应范围   |
| name   |String    |标段的名称   |
| category   |String    |构件类别名称   |
| volumes   |Array    |用量信息   |
| materialTypeName   |String     |材料型号名称   |
| id   |Number     |材料型号id   |
| data   |Array     |具体用量信息   |
| data['name']   |String     |度量名称   |
| data['value']   |String     |值   |


示例：
```
{
 "jsonrpc":2.0,
 "id":null,
 "result":{
           "code":0,
           "message":"xxx",
           "data":{
                    "geometry": {
                        "boundingBox": [
                            [32, 32],
                            [33, 32],
                            [33, 33],
                            [32, 33]
                        ]
                    },
                    "name":"XXX",
                    "data": [{
                        "category": "底板",
                        "volumes": [{
                            "materialTypeName": "混凝土",
                            "id": 5,
                            "data": [{
                                "name": "体积",
                                "value": "33立方米"
                            }, {
                                "name": "重量",
                                "value": "24吨"
                            }]
                        }, {
                            "materialTypeName": "钢",
                            "id": 6,
                            "data": [{
                                "name": "重量",
                                "value": "24吨"
                            }]
                        }]
                    }, {
                        "category": "顶板",
                        "volumes": [{
                            "materialTypeName": "混凝土",
                            "id": 5,
                            "data": [{
                                "name": "体积",
                                "value": "39立方米"
                            }, {
                                "name": "重量",
                                "value": "27吨"
                            }]
                        }, {
                            "materialTypeName": "钢",
                            "id": 6,
                            "data": [{
                                "name": "重量",
                                "value": "30吨"
                            }]
                        }]
                    }]
                }
          }
}

##此处data中的结构等同一期成本计量的接口
```

# /projects/cost/planned_material_listings/sections/search

## URI

**URI**: /projects/cost/planned_material_listings/sections/search

## 支持的方法

POST

## 介绍

查询某个标段的计量清单信息 POST

## HTTP方法

### POST

请求参数：
```

{"constructCode":"ZTJG-ZD-CQ-III1-K42-2"}

```


响应结构：

| 字段 | 类型 | 说明 |
|----|----|----|
| boundingBox   |Array    |二维包围盒，用来显示对应范围   |
| name   |String    |标段的名称   |
| category   |String    |构件类别名称   |
| volumes   |Array    |用量信息   |
| materialTypeName   |String     |材料型号名称   |
| id   |Number     |材料型号id   |
| data   |Array     |具体用量信息   |
| data['name']   |String     |度量名称   |
| data['value']   |String     |值   |


示例：
```
{
 "jsonrpc":2.0,
 "id":null,
 "result":{
           "code":0,
           "message":"xxx",
           "data":{
                    "geometry": {
                        "boundingBox": [
                            [32, 32],
                            [33, 32],
                            [33, 33],
                            [32, 33]
                        ]
                    },
                    "name":"XXX",
                    "data": [{
                        "category": "底板",
                        "volumes": [{
                            "materialTypeName": "混凝土",
                            "id": 5,
                            "data": [{
                                "name": "体积",
                                "value": "33立方米"
                            }, {
                                "name": "重量",
                                "value": "24吨"
                            }]
                        }, {
                            "materialTypeName": "钢",
                            "id": 6,
                            "data": [{
                                "name": "重量",
                                "value": "24吨"
                            }]
                        }]
                    }, {
                        "category": "顶板",
                        "volumes": [{
                            "materialTypeName": "混凝土",
                            "id": 5,
                            "data": [{
                                "name": "体积",
                                "value": "39立方米"
                            }, {
                                "name": "重量",
                                "value": "27吨"
                            }]
                        }, {
                            "materialTypeName": "钢",
                            "id": 6,
                            "data": [{
                                "name": "重量",
                                "value": "30吨"
                            }]
                        }]
                    }]
                }
          }
}

##此处data中的结构等同一期成本计量的接口
```

# /projects/cost/planned_material_listings/pits/{id}

## URI

**URI**: /projects/cost/planned_material_listings/pits/{id}

## 支持的方法

GET

## 介绍

获取指定基坑的计量清单信息 GET

## HTTP方法

### GET

请求参数：

无

响应结构：

| 字段 | 类型 | 说明 |
|----|----|----|
| boundingBox   |Array    |二维包围盒，用来显示对应范围   |
| name   |String    |基坑的名称   |
| category   |String    |构件类别名称   |
| volumes   |Array    |用量信息   |
| materialTypeName   |String     |材料型号名称   |
| id   |Number     |材料型号id   |
| data   |Array     |具体用量信息   |
| data['name']   |String     |度量名称   |
| data['value']   |String     |值   |


示例：
```
{
 "jsonrpc":2.0,
 "id":null,
 "result":{
           "code":0,
           "message":"xxx",
           "data":{
                    "geometry": {
                        "boundingBox": [
                            [32, 32],
                            [33, 32],
                            [33, 33],
                            [32, 33]
                        ]
                    },
                    "name":"XXX",
                    "data": [{
                        "category": "底板",
                        "volumes": [{
                            "materialTypeName": "混凝土",
                            "id": 5,
                            "data": [{
                                "name": "体积",
                                "value": "33立方米"
                            }, {
                                "name": "重量",
                                "value": "24吨"
                            }]
                        }, {
                            "materialTypeName": "钢",
                            "id": 6,
                            "data": [{
                                "name": "重量",
                                "value": "24吨"
                            }]
                        }]
                    }, {
                        "category": "顶板",
                        "volumes": [{
                            "materialTypeName": "混凝土",
                            "id": 5,
                            "data": [{
                                "name": "体积",
                                "value": "39立方米"
                            }, {
                                "name": "重量",
                                "value": "27吨"
                            }]
                        }, {
                            "materialTypeName": "钢",
                            "id": 6,
                            "data": [{
                                "name": "重量",
                                "value": "30吨"
                            }]
                        }]
                    }]
                }
          }
}

##此处data中的结构等同一期成本计量的接口
```

# /projects/cost/planned_material_listings/pits/search

## URI

**URI**: /projects/cost/planned_material_listings/pits/search

## 支持的方法

POST

## 介绍

查询某个基坑的计量清单信息 POST

## HTTP方法

### POST

请求参数：
```

{"constructCode":"ZTJG-ZD-CQ-III1-K42-2"}

```

响应结构：

| 字段 | 类型 | 说明 |
|----|----|----|
| boundingBox   |Array    |二维包围盒，用来显示对应范围   |
| name   |String    |基坑的名称   |
| category   |String    |构件类别名称   |
| volumes   |Array    |用量信息   |
| materialTypeName   |String     |材料型号名称   |
| id   |Number     |材料型号id   |
| data   |Array     |具体用量信息   |
| data['name']   |String     |度量名称   |
| data['value']   |String     |值   |


示例：
```
{
 "jsonrpc":2.0,
 "id":null,
 "result":{
           "code":0,
           "message":"xxx",
           "data":{
                    "geometry": {
                        "boundingBox": [
                            [32, 32],
                            [33, 32],
                            [33, 33],
                            [32, 33]
                        ]
                    },
                    "name":"XXX",
                    "data": [{
                        "category": "底板",
                        "volumes": [{
                            "materialTypeName": "混凝土",
                            "id": 5,
                            "data": [{
                                "name": "体积",
                                "value": "33立方米"
                            }, {
                                "name": "重量",
                                "value": "24吨"
                            }]
                        }, {
                            "materialTypeName": "钢",
                            "id": 6,
                            "data": [{
                                "name": "重量",
                                "value": "24吨"
                            }]
                        }]
                    }, {
                        "category": "顶板",
                        "volumes": [{
                            "materialTypeName": "混凝土",
                            "id": 5,
                            "data": [{
                                "name": "体积",
                                "value": "39立方米"
                            }, {
                                "name": "重量",
                                "value": "27吨"
                            }]
                        }, {
                            "materialTypeName": "钢",
                            "id": 6,
                            "data": [{
                                "name": "重量",
                                "value": "30吨"
                            }]
                        }]
                    }]
                }
          }
}

##此处data中的结构等同一期成本计量的接口
```

# /projects/cost/planned_material_listings/segments/{id}

## URI

**URI**: /projects/cost/planned_material_listings/segments/{id}

## 支持的方法

GET

## 介绍

获取指定节段的计量清单信息 GET

## HTTP方法

### GET

请求参数：

无

响应结构：

| 字段 | 类型 | 说明 |
|----|----|----|
| boundingBox   |Array    |二维包围盒，用来显示对应范围   |
| name   |String    |节段的名称   |
| category   |String    |构件类别名称   |
| volumes   |Array    |用量信息   |
| materialTypeName   |String     |材料型号名称   |
| id   |Number     |材料型号id   |
| data   |Array     |具体用量信息   |
| data['name']   |String     |度量名称   |
| data['value']   |String     |值   |
| gangjin   |Array     |钢筋的数据   |

示例：
```
{
 "jsonrpc":2.0,
 "id":null,
 "result":{
           "code":0,
           "message":"xxx",
           "data":{
                   "geometry": {
                   "boundingBox": [
                                [32, 32],
                                [33, 32],
                                [33, 33],
                                [32, 33]
                            ]
                        },
                    "name":"xxx",
                    "data": [{
                            "category": "底板",
                            "volumes": [{
                                "materialTypeName": "混凝土",
                                "id": 5,
                                "data": [{
                                    "name": "体积",
                                    "value": "33立方米"
                                }, {
                                    "name": "重量",
                                    "value": "24吨"
                                }]
                            }, {
                                "materialTypeName": "钢",
                                "id": 6,
                                "data": [{
                                    "name": "重量",
                                    "value": "24吨"
                                }]
                            }]
                        }],
                        "gangjin": [{
                            "name": "型号1",
                            "id": 1,
                            "data": [{
                                "name": "直径",
                                "value": "10mm"
                            }, {
                                "name": "件数",
                                "value": "300"
                            }, {
                                "name": "总长度",
                                "value": "1km"
                            }, {
                                "name": "总重量",
                                "value": "20吨"
                            }]
                        }]
                        }
  }
}

##此处data中的结构等同一期成本计量的接口
```

# /projects/cost/planned_material_listings/segments/search

## URI

**URI**: /projects/cost/planned_material_listings/segments/search

## 支持的方法

POST

## 介绍

查询某个节段的计量清单信息 POST

## HTTP方法

### GET

请求参数：

无

响应结构：

| 字段 | 类型 | 说明 |
|----|----|----|
| boundingBox   |Array    |二维包围盒，用来显示对应范围   |
| name   |String    |节段的名称   |
| category   |String    |构件类别名称   |
| volumes   |Array    |用量信息   |
| materialTypeName   |String     |材料型号名称   |
| id   |Number     |材料型号id   |
| data   |Array     |具体用量信息   |
| data['name']   |String     |度量名称   |
| data['value']   |String     |值   |
| gangjin   |Array     |钢筋的数据   |

示例：
```
{
 "jsonrpc":2.0,
 "id":null,
 "result":{
           "code":0,
           "message":"xxx",
           "data":{
                   "geometry": {
                   "boundingBox": [
                                [32, 32],
                                [33, 32],
                                [33, 33],
                                [32, 33]
                            ]
                        },
                    "name":"xxx",
                    "data": [{
                            "category": "底板",
                            "volumes": [{
                                "materialTypeName": "混凝土",
                                "id": 5,
                                "data": [{
                                    "name": "体积",
                                    "value": "33立方米"
                                }, {
                                    "name": "重量",
                                    "value": "24吨"
                                }]
                            }, {
                                "materialTypeName": "钢",
                                "id": 6,
                                "data": [{
                                    "name": "重量",
                                    "value": "24吨"
                                }]
                            }]
                        }],
                        "gangjin": [{
                            "name": "型号1",
                            "id": 1,
                            "data": [{
                                "name": "直径",
                                "value": "10mm"
                            }, {
                                "name": "件数",
                                "value": "300"
                            }, {
                                "name": "总长度",
                                "value": "1km"
                            }, {
                                "name": "总重量",
                                "value": "20吨"
                            }]
                        }]
                        }
  }
}

##此处data中的结构等同一期成本计量的接口
```

# /projects/cost/planned_material_listings/constructs/{id}

## URI

**URI**: /projects/cost/planned_material_listings/constructs/<string:id>
```     
此处传参是列表,例子：http://192.168.56.101:8069/projects/cost/planned_material_listings/constructs/['233796','233797']
```

## 支持的方法

GET

## 介绍

获取指定构件的计量清单信息 GET

## HTTP方法

### GET

请求参数：

无

响应结构：

| 字段 | 类型 | 说明 |
|----|----|----|
| name   |String    |构件的名称   |
| category   |String    |构件类别名称   |
| volumes   |Array    |用量信息   |
| materialTypeName   |String     |材料型号名称   |
| id   |Number     |材料型号id   |
| data   |Array     |具体用量信息   |
| data['name']   |String     |度量名称   |
| data['value']   |String     |值   |


示例：
```
{
 "jsonrpc":2.0,
 "id":null,
 "result":{
           "code":0,
           "message":"xxx",
           "data":{  
                    "data": [{
                        "name":"XXX",
                        "category": "底板",
                        "volumes": [{
                            "materialTypeName": "混凝土",
                            "id": 5,
                            "data": [{
                                "name": "体积",
                                "value": "33立方米"
                            }, {
                                "name": "重量",
                                "value": "24吨"
                            }]
                        }, {
                            "materialTypeName": "钢",
                            "id": 6,
                            "data": [{
                                "name": "重量",
                                "value": "24吨"
                            }]
                        }]
                    }]
                }
          }
}

##此处data中的结构等同一期成本计量的接口
```

# /projects/cost/planned_material_listings/constructs/search

## URI

**URI**: /projects/cost/planned_material_listings/constructs/search

## 支持的方法

POST

## 介绍

查询某个构件的计量清单信息 POST

## HTTP方法

### POST

请求参数：
```

{"constructCode":["ZTJG-ZD-CQ-III1-K42-2","ZTJG-ZD-CQ-III1-K42-3"]} \\可以传多个构件编码

```


响应结构：

| 字段 | 类型 | 说明 |
|----|----|----|
| name   |String    |标段的名称   |
| category   |String    |构件类别名称   |
| volumes   |Array    |用量信息   |
| materialTypeName   |String     |材料型号名称   |
| id   |Number     |材料型号id   |
| data   |Array     |具体用量信息   |
| data['name']   |String     |度量名称   |
| data['value']   |String     |值   |


示例：
```
{
 "jsonrpc":2.0,
 "id":null,
 "result":{
           "code":0,
           "message":"xxx",
           "data":{
                    "data": [{
                        "name":"XXX",
                        "category": "底板",
                        "volumes": [{
                            "materialTypeName": "混凝土",
                            "id": 5,
                            "data": [{
                                "name": "体积",
                                "value": "33立方米"
                            }, {
                                "name": "重量",
                                "value": "24吨"
                            }]
                        }, {
                            "materialTypeName": "钢",
                            "id": 6,
                            "data": [{
                                "name": "重量",
                                "value": "24吨"
                            }]
                        }]
                    }]
                }
          }
}

##此处data中的结构等同一期成本计量的接口
```

# /projects/cost/finished_material_listings

## URI

**URI**: /projects/cost/finished_material_listings

## 支持的方法

POST

## 介绍

查询指定时间、指定区域已完工任务构件的计量清单信息 POST

## HTTP方法

### POST

请求参数：
```

{"startTime":"2020-10-10","endTime":"2020-10-12","id":[4,5]}

```


响应结构：

| 字段 | 类型 | 说明 |
|----|----|----|
| name   |String    |标段的名称   |
| category   |String    |构件类别名称   |
| volumes   |Array    |用量信息   |
| materialTypeName   |String     |材料型号名称   |
| id   |Number     |材料型号id   |
| data   |Array     |具体用量信息   |
| data['name']   |String     |度量名称   |
| data['value']   |String     |值   |
| layerNames   |Object     |图层名称  |
| ids   |Array     |构件的SmID  |

示例：
```
{
 "jsonrpc":2.0,
 "id":null,
 "result":{
           "code":0,
           "message":"xxx",
           "data":{ "layerNames":[
                                 {"layerName":"主体结构",
                                  "ids":[1,3,45,2]},
                                 {"layerName":"附加结构",
                                  "ids":[1,3,45,2]}
                                ],
                    "data": [{
                        "name":"XXX",
                        "category": "底板",
                        "volumes": [{
                            "materialTypeName": "混凝土",
                            "id": 5,
                            "data": [{
                                "name": "体积",
                                "value": "33立方米"
                            }, {
                                "name": "重量",
                                "value": "24吨"
                            }]
                        }, {
                            "materialTypeName": "钢",
                            "id": 6,
                            "data": [{
                                "name": "重量",
                                "value": "24吨"
                            }]
                        }]
                    }]
                }
          }
}

##此处data中的结构等同一期成本计量的接口
```