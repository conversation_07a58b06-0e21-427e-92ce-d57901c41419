# -*- coding: utf-8 -*-
from odoo import fields, api, models
from odoo.exceptions import UserError, ValidationError

class MaintenanceLifeCycleInventory(models.Model):
    """
    养护生命周期清单
    """
    _name = 'maintenance.life.cycle.inventory'
    _description = "养护生命周期清单"
    _rec_name = 'name'

  

    name = fields.Char(string='病害名称', required=True)
    code = fields.Char(string='编码', compute='com_code')
    remark = fields.Char(string='备注', required=True)
    carbon_factor = fields.Char(string='碳排放因子', required=True)
    unit_id = fields.Many2one('carbon.unit', '单位', ondelete='restrict', required=True)
    type_id = fields.Many2one('life.cycle.inventory.type', '所属类别', ondelete='cascade')
    inventory_id = fields.Many2one('life.cycle.inventory', '所属清单', ondelete='cascade')


    def com_code(self):
        for record in self:
            record.code = f'F-BH-{record.id}'
    
