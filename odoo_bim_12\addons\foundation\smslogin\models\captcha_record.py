# -*- coding: utf-8 -*-
from odoo import fields, api, models
from odoo.exceptions import UserError, ValidationError
import time
class CaptchaRecord(models.Model):
    """
    校验码
    """
    _name = 'captcha.record'
    _description = "校验码"
    # _rec_name = 'name'

 
    captcha = fields.Char(string="校验码", required=True)
    token = fields.Char(string="token", required=True)
    ip = fields.Char(string="ip")
    generation_time = fields.Char(string="生成时间的时间戳", required=True)
    is_expire = fields.<PERSON><PERSON><PERSON>(string="是否过期", compute='com_is_expire')
    is_used = fields.<PERSON><PERSON><PERSON>(string="是否使用过")

    
    def com_is_expire(self):
        for record in self:
            # record.is_expire = True
            current_time = time.time()
            elapsed_time = current_time - float(record.generation_time)
            if elapsed_time > 60 * 5:
                record.is_expire = True
            else:
                record.is_expire = False


   
    
    
