# -*- coding: utf-8 -*-
from odoo import fields, api, models
from odoo.exceptions import UserError, ValidationError

class CarbonStage(models.Model):
    """
    阶段
    """
    _name = 'carbon.stage'
    _description = "阶段"
    _rec_name = 'name'
    _order = 'sequence'
    
    _sql_constraints = [('name_unique', 'unique(name)', '阶段名称不可重名!!!')]

  

    sequence = fields.Integer(string='序号')
    name = fields.Char(string='阶段名称', required=True)
   
    
