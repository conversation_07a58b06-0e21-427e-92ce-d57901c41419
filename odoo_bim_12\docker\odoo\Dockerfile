FROM ubuntu:16.04

# 安装pyenv
# set the variables as per $(pyenv init -)
ENV LANG="C.UTF-8" \
    LC_ALL="C.UTF-8" \
    PATH="/opt/pyenv/shims:/opt/pyenv/bin:$PATH" \
    PYENV_ROOT="/opt/pyenv" \
    PYENV_SHELL="bash"

# 替换为阿里源
COPY ./sources.list /etc/apt/sources.list

RUN apt-get update && apt-get install -y --fix-missing git \
        && apt-get install -y --no-install-recommends \
    build-essential \
    ca-certificates \
    curl \
    git \
    libbz2-dev \
    libffi-dev \
    libncurses5-dev \
    libncursesw5-dev \
    libreadline-dev \
    libsqlite3-dev \
    liblzma-dev \
    llvm \
    make \
    netbase \
    pkg-config \
    tk-dev \
    wget \
    xz-utils \
    zlib1g-dev \
    libssl-dev \
    libxml2-dev \
    libxslt-dev \
    libldap2-dev \
    libsasl2-dev

COPY pyenv-version.txt python-versions.txt /

RUN git clone -b `cat /pyenv-version.txt` --single-branch --depth 1 https://github.com/pyenv/pyenv.git $PYENV_ROOT \
    && git clone https://github.com/pyenv/pyenv-virtualenv.git $(pyenv root)/plugins/pyenv-virtualenv   \
    && echo 'eval "$(pyenv init -)"' >> ~/.bashrc   \
    && echo 'eval "$(pyenv virtualenv-init -)"' >> ~/.bashrc    \
    && for version in `cat /python-versions.txt`; do wget https://npm.taobao.org/mirrors/python/$version/Python-$version.tar.xz -P $PYENV_ROOT/cache/ && pyenv install $version; done \
    && pyenv global `cat /python-versions.txt` \
    && find $PYENV_ROOT/versions -type d '(' -name '__pycache__' -o -name 'test' -o -name 'tests' ')' -exec rm -rf '{}' + \
    && find $PYENV_ROOT/versions -type f '(' -name '*.pyo' -o -name '*.exe' ')' -exec rm -f '{}' + \
    && rm -rf /tmp/*

RUN apt-get install -y curl \
    && apt-get -y autoclean  \
    && apt-get install -y libpq-dev

# 安装nvm
# replace shell with bash so we can source files
RUN rm /bin/sh && ln -s /bin/bash /bin/sh

# nvm environment variables
ENV NVM_DIR=/usr/local/nvm  \
    NODE_VERSION=14.17.0    \
    NVM_INSTALL_SCRIPT=/tmp/nvm-install.sh  \
    NVM_NODEJS_ORG_MIRROR=http://npm.taobao.org/mirrors/node  \
    NPM_REGISTRY_MIRROR=https://registry.npm.taobao.org 

# install nvm
# https://github.com/creationix/nvm#install-script
RUN mkdir -p ${NVM_DIR}
COPY ./nvm-install-v0.35.3.sh ${NVM_INSTALL_SCRIPT}
RUN bash ${NVM_INSTALL_SCRIPT}

# install node and npm
RUN source $NVM_DIR/nvm.sh \
    && NVM_NODEJS_ORG_MIRROR=${NVM_NODEJS_ORG_MIRROR} nvm install $NODE_VERSION \
    && nvm alias default $NODE_VERSION \
    && nvm use default

# add node and npm to path so the commands are available
ENV NODE_PATH $NVM_DIR/v$NODE_VERSION/lib/node_modules
ENV PATH $NVM_DIR/versions/node/v$NODE_VERSION/bin:$PATH

# confirm installation
RUN node -v \
    && npm --registry ${NPM_REGISTRY_MIRROR} install --global cnpm@7.1.0 \
    && cnpm install --global yarn less

# 拷贝字体
COPY ./fonts/simhei.ttf /usr/share/fonts

# 安装pg客户端
COPY ./pg.sh /tmp/pg.sh
RUN bash /tmp/pg.sh
ENV PATH /usr/local/pgsql/bin:$PATH

RUN mkdir -p /odoo

# 安装odoo依赖
COPY ./odoo.tar.gz /tmp/

# odoo数据目录
RUN mkdir -p /odoo/.data 
VOLUME /odoo/.data

# 用于存储odoo数据库备份
RUN mkdir -p /backup
VOLUME /backup

ENV ODOO_PORT=8069    \
    ODOO_DBHOST=database    \
    ODOO_DBPORT=5432    \
    ODOO_DBUSER=odoo    \
    ODOO_DBPASSWORD=odoo    \
    PGPASSWORD=odoo    \
    ODOO_DBNAME=dev   \
    SENTRY_DSN=http://e4a134f3a6904e0db0765934fc25b285@***********:9000/2   \
    CLUSTER=no_cluster  \
    REDIS_HOST=localhost    \
    REDIS_POST=6379

RUN source ~/.bashrc    
RUN pyenv virtualenv 3.6.5 odoo13-dev  
RUN tar -xvf /tmp/odoo.tar.gz -C /odoo  
WORKDIR /odoo
RUN pip install --upgrade pip  --index-url https://pypi.tuna.tsinghua.edu.cn/simple
RUN pip install --index-url https://pypi.tuna.tsinghua.edu.cn/simple -r /odoo/requirements.txt   
RUN pip install --index-url https://pypi.tuna.tsinghua.edu.cn/simple -r /odoo/odoo/requirements.txt 
RUN apt-get update || true
RUN apt-get install -y xvfb 
RUN apt-get install -y wkhtmltopdf
RUN pip install --index-url https://pypi.tuna.tsinghua.edu.cn/simple xvfbwrapper
RUN rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

EXPOSE 8069
CMD ["yarn", "docker:run:odoo"]
