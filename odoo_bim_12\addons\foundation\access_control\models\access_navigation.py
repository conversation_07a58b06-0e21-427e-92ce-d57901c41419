# -*- coding: utf-8 -*-
from odoo import fields, api, models
from odoo.exceptions import ValidationError

class AccessNavigation(models.Model):
    """
    导航
    """
    _name = 'access.navigation'
    _description = "导航"

    name = fields.Char('名称',required=True)
    path = fields.Char('路径',related='route_id.path')
    route_id = fields.Many2one('access.route', '路由',required=True)
    is_active = fields.<PERSON><PERSON>an('Active',default=True)
    sequence = fields.Integer('排序')


    def name_get(self):
        res = []
        for record in self:
            res.append((record.id, '%s (%s)' % (record.name, record.path)))
        return res
    
    @api.onchange('sequence')
    def sequence_check(self):
        # 获取当前排序
        for record in self:
            if record.sequence:
                res_sequence = self.env['access.navigation'].sudo().search([('sequence', '=', record.sequence)])
                if len(res_sequence) >= 1:
                    raise ValidationError('该排序已经存在,请重新填写！')















