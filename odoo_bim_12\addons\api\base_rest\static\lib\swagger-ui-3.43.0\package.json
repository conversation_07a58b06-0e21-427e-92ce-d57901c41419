{"_from": "swagger-ui-dist", "_id": "swagger-ui-dist@3.43.0", "_inBundle": false, "_integrity": "sha1-sGSizsHSd3b5oSS8cEI8+gu8DT8=", "_location": "/swagger-ui-dist", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "swagger-ui-dist", "name": "swagger-ui-dist", "escapedName": "swagger-ui-dist", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER"], "_resolved": "https://registry.npm.taobao.org/swagger-ui-dist/download/swagger-ui-dist-3.43.0.tgz", "_shasum": "b064a2cec1d27776f9a124bc70423cfa0bbc0d3f", "_spec": "swagger-ui-dist", "_where": "/Users/<USER>/workspace/zthx/bimproject/odoo_bim_12/addons/api/base_rest/static", "bugs": {"url": "https://github.com/swagger-api/swagger-ui/issues"}, "bundleDependencies": false, "contributors": [{"url": "in alphabetical order"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {}, "deprecated": false, "description": "[![NPM version](https://badge.fury.io/js/swagger-ui-dist.svg)](http://badge.fury.io/js/swagger-ui-dist)", "devDependencies": {}, "homepage": "https://github.com/swagger-api/swagger-ui#readme", "license": "Apache-2.0", "main": "index.js", "name": "swagger-ui-dist", "repository": {"type": "git", "url": "git+ssh://**************/swagger-api/swagger-ui.git"}, "version": "3.43.0"}