#!/bin/bash

version=${1-10}

if [ ! -f /etc/apt/sources.list.d/pgdg.list ]; then
    touch /etc/apt/sources.list.d/pgdg.list
    tee -a /etc/apt/sources.list.d/pgdg.list <<'EOF'
deb http://apt.postgresql.org/pub/repos/apt/ bionic-pgdg main
EOF

    echo "Installing postgresql repository key..."
    wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | apt-key add -

    apt-get update
fi

echo "Installing postgresql..."
apt-get install -y postgresql-${version}
