# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-07-01 12:20+0000\n"
"PO-Revision-Date: 2019-08-26 09:06+0000\n"
"Language-Team: <PERSON><PERSON><PERSON> (https://www.transifex.com/odoo/teams/41243/uz/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uz\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__amount_type
msgid ""
"\n"
"    - Group of Taxes: The tax is a set of sub taxes.\n"
"    - Fixed: The tax amount stays the same whatever the price.\n"
"    - Percentage of Price: The tax amount is a % of the price:\n"
"        e.g 100 * 10% = 110 (not price included)\n"
"        e.g 110 / (1 + 10%) = 100 (price included)\n"
"    - Percentage of Price Tax Included: The tax amount is a division of the price:\n"
"        e.g 180 / (1 - 10%) = 200 (not price included)\n"
"        e.g 200 * (1 - 10%) = 180 (price included)\n"
"        "
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__code_digits
msgid "# of Digits"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__number
msgid "#Coins/Bills"
msgstr ""

#. module: account
#: model:mail.template,report_name:account.mail_template_data_payment_receipt
msgid "${(object.name or '').replace('/','-')}"
msgstr ""

#. module: account
#: model:mail.template,subject:account.email_template_edi_invoice
msgid "${object.company_id.name} Invoice (Ref ${object.name or 'n/a'})"
msgstr ""

#. module: account
#: model:mail.template,subject:account.mail_template_data_payment_receipt
msgid ""
"${object.company_id.name} Payment Receipt (Ref ${object.name or 'n/a' })"
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid "%s (Copy)"
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#: code:addons/account/models/account.py:0
#: code:addons/account/models/account.py:0
#: code:addons/account/models/account.py:0
#: code:addons/account/models/account.py:0
#, python-format
msgid "%s (copy)"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "%s (rounding)"
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid "%s Sequence"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "&amp;nbsp;<span>on</span>"
msgstr ""

#. module: account
#: model:ir.actions.report,print_report_name:account.account_invoices
#: model:ir.actions.report,print_report_name:account.account_invoices_without_payment
msgid "(object._get_report_base_filename())"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_form
msgid "-> Reconcile"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "-> View partially reconciled entries"
msgstr ""

#. module: account
#: model:account.payment.term,name:account.account_payment_term_15days
msgid "15 Days"
msgstr ""

#. module: account
#: model:account.payment.term,name:account.account_payment_term_2months
msgid "2 Months"
msgstr ""

#. module: account
#: model:account.payment.term,name:account.account_payment_term_21days
msgid "21 Days"
msgstr ""

#. module: account
#: model:account.payment.term,name:account.account_payment_term_30days
msgid "30 Days"
msgstr ""

#. module: account
#: model:account.payment.term,name:account.account_payment_term_advance
msgid "30% Advance End of Following Month"
msgstr ""

#. module: account
#: model:account.payment.term,name:account.account_payment_term_advance_60days
msgid "30% Now, Balance 60 Days"
msgstr ""

#. module: account
#: model:account.payment.term,name:account.account_payment_term_45days
msgid "45 Days"
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ": Refund"
msgstr ""

#. module: account
#: model:mail.template,body_html:account.mail_template_data_payment_receipt
msgid ""
"<?xml version=\"1.0\"?>\n"
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear ${object.partner_id.name}<br/><br/>\n"
"        Thank you for your payment.\n"
"        Here is your payment receipt <strong>${(object.name or '').replace('/','-')}</strong> amounting\n"
"        to <strong>${format_amount(object.amount, object.currency_id)}</strong> from ${object.company_id.name}.\n"
"        <br/><br/>\n"
"        Do not hesitate to contact us if you have any questions.\n"
"        <br/><br/>\n"
"        Best regards,<br/>\n"
"        % if user and user.signature:\n"
"        ${user.signature | safe}\n"
"        % endif\n"
"    </p>\n"
"</div>\n"
msgstr ""

#. module: account
#: model:mail.template,body_html:account.email_template_edi_invoice
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear\n"
"        % if object.partner_id.parent_id:\n"
"            ${object.partner_id.name} (${object.partner_id.parent_id.name}),\n"
"        % else:\n"
"            ${object.partner_id.name},\n"
"        % endif\n"
"        <br/><br/>\n"
"        Here is your\n"
"        % if object.name:\n"
"            invoice <strong>${object.name}</strong>\n"
"        % else:\n"
"            invoice\n"
"        %endif\n"
"        % if object.invoice_origin:\n"
"            (with reference: ${object.invoice_origin})\n"
"        % endif\n"
"        amounting in <strong>${format_amount(object.amount_total, object.currency_id)}</strong>\n"
"        from ${object.company_id.name}.\n"
"        % if object.invoice_payment_state == 'paid':\n"
"            This invoice is already paid.\n"
"        % else:\n"
"            Please remit payment at your earliest convenience.\n"
"        % endif\n"
"        <br/><br/>\n"
"        Do not hesitate to contact us if you have any questions.\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_my_home_menu_invoice
msgid "<em>Draft Invoice</em>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_move_line_view_kanban
#: model_terms:ir.ui.view,arch_db:account.view_account_move_kanban
msgid "<i class=\"fa fa-clock-o\" aria-label=\"Date\" role=\"img\" title=\"Date\"/>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_kanban
msgid "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_page
msgid "<i class=\"fa fa-download\"/> Download"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid ""
"<i class=\"fa fa-ellipsis-v\" aria-label=\"Selection\" role=\"img\" "
"title=\"Selection\"/>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_page
msgid "<i class=\"fa fa-fw fa-comments\"/><b>Send message</b>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-arrow-right\"/> Configure Email Servers"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_page
msgid "<i class=\"fa fa-print\"/> Print"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid ""
"<span attrs=\"{'invisible': ['|', '|', ('type', '!=', 'out_invoice'), ('state', '!=', 'draft'), ('name', '!=', '/')]}\">Draft Invoice</span>\n"
"                                <span attrs=\"{'invisible': ['|', '|', ('type', '!=', 'out_refund'), ('state', '!=', 'draft'), ('name', '!=', '/')]}\">Draft Credit Note</span>\n"
"                                <span attrs=\"{'invisible': ['|', '|', ('type', '!=', 'in_invoice'), ('state', '!=', 'draft'), ('name', '!=', '/')]}\">Draft Bill</span>\n"
"                                <span attrs=\"{'invisible': ['|', '|', ('type', '!=', 'in_refund'), ('state', '!=', 'draft'), ('name', '!=', '/')]}\">Draft Refund</span>\n"
"                                <span attrs=\"{'invisible': ['|', '|', ('type', '!=', 'out_receipt'), ('state', '!=', 'draft'), ('name', '!=', '/')]}\">Draft Sales Receipt</span>\n"
"                                <span attrs=\"{'invisible': ['|', '|', ('type', '!=', 'in_receipt'), ('state', '!=', 'draft'), ('name', '!=', '/')]}\">Draft Purchase Receipt</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid ""
"<span attrs=\"{'invisible': [('composition_mode', '!=', 'mass_mail')]}\">\n"
"                                        <strong>Email mass mailing</strong> on\n"
"                                        <span>the selected records</span>\n"
"                                    </span>\n"
"                                    <span>Followers of the document and</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_form
msgid ""
"<span attrs=\"{'invisible':[('reconciled_invoices_count','&gt;',1)]}\">Invoice</span>\n"
"                                <span attrs=\"{'invisible':[('reconciled_invoices_count','&lt;=',1)]}\">Invoices</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
msgid ""
"<span class=\"badge badge-pill badge-info\"><i class=\"fa fa-fw fa-clock-o\""
" aria-label=\"Opened\" title=\"Opened\" role=\"img\"/><span class=\"d-none d"
"-md-inline\"> Waiting for Payment</span></span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
msgid ""
"<span class=\"badge badge-pill badge-success\"><i class=\"fa fa-fw fa-"
"check\" aria-label=\"Paid\" title=\"Paid\" role=\"img\"/><span "
"class=\"d-none d-md-inline\"> Paid</span></span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
msgid ""
"<span class=\"badge badge-pill badge-warning\"><i class=\"fa fa-fw fa-"
"remove\" aria-label=\"Cancelled\" title=\"Cancelled\" role=\"img\"/><span "
"class=\"d-none d-md-inline\"> Cancelled</span></span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid ""
"<span class=\"badge badge-warning text-uppercase "
"o_sample_data_label\">Sample data</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\"/>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid ""
"<span class=\"o_form_label mx-3 oe_edit_only\" attrs=\"{'invisible': [ '|', "
"'|', '|', ('state', '!=', 'draft'), ('invoice_payment_term_id', '!=', "
"False), ('type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', "
"'in_refund', 'out_receipt', 'in_receipt'))]}\"> or </span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid ""
"<span class=\"o_form_label\" attrs=\"{'invisible': "
"[('invoice_sequence_number_next_prefix', '=', False)]}\">First "
"Number:</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid ""
"<span class=\"o_form_label\" attrs=\"{'invisible': [('match_amount', '!=', "
"'between')]}\">and</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.validate_account_move_view
msgid ""
"<span class=\"o_form_label\">All selected journal entries will be validated "
"and posted. You won't be able to modify them afterwards.</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Default Incoterm</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Default Sending Options</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Default Taxes</span>\n"
"                                    <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Fiscal Localization</span>\n"
"                                    <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Main Currency</span>\n"
"                                    <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Rounding Method</span>\n"
"                                    <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_form
msgid "<span class=\"o_form_label\">of the month</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.partner_view_buttons
msgid "<span class=\"o_stat_text\">Invoiced</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.setup_bank_account_wizard
msgid ""
"<span class=\"text-muted\">Only journals not yet linked to a bank account "
"are proposed</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid ""
"<span groups=\"account.group_show_line_subtotals_tax_excluded\">Amount</span>\n"
"                                    <span groups=\"account.group_show_line_subtotals_tax_included\">Total Price</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span name=\"button_import_placeholder\"/> Statements"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span role=\"separator\">New</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span role=\"separator\">Reconciliation</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span role=\"separator\">View</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span title=\"Balance in General Ledger\">Balance in GL</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span title=\"Latest Statement\">Latest Statement</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_position_form
msgid "<span> From </span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_position_form
msgid "<span> To </span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<span>Amount Paid</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<span>Balance</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<span>Description</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<span>Disc.%</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<span>Invoice Date</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<span>Invoice Number</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>New Bill</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>New Entry</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>New Invoice</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>New</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>Operations</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<span>Original Amount</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<span>Quantity</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<span>Reference</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>Reporting</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<span>Source Document</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<span>Taxes</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<span>Unit Price</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>Upload Bills</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>Upload Invoices</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "<span>View</span>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong class=\"mr16\">Subtotal</strong>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid ""
"<strong class=\"text-center\">Scan me with your banking "
"app.</strong><br/><br/>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid ""
"<strong class=\"text-center\">The SEPA QR Code informations are not set "
"correctly.</strong><br/>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_page
msgid "<strong class=\"text-muted\">Salesperson</strong>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document_with_payments
msgid "<strong>Amount Due</strong>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "<strong>Company:</strong>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Customer Code:</strong>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<strong>Customer: </strong>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Due Date:</strong>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_statement
msgid "<strong>Ending Balance</strong>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "<strong>Entries Sorted By:</strong>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Invoice Date:</strong>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "<strong>Journal:</strong>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<strong>Memo: </strong>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<strong>Payment Amount: </strong>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<strong>Payment Date: </strong>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<strong>Payment Method: </strong>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Reference:</strong>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Source:</strong>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_statement
msgid "<strong>Starting Balance</strong>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "<strong>Subtotal</strong>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "<strong>Target Moves:</strong>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "<strong>Total</strong>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_account_kanban
msgid "<strong>Type: </strong>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "<strong>Vendor: </strong>"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_view_bank_statement_tree
msgid ""
"A Cash Register allows you to manage cash entries in your cash\n"
"                journals. This feature provides an easy way to follow up cash\n"
"                payments on a daily basis. You can enter the coins that are in\n"
"                your cash box, and then post entries when money comes in or\n"
"                goes out of the cash box."
msgstr ""

#. module: account
#: code:addons/account/models/account_payment_term.py:0
#, python-format
msgid "A Payment Term should have only one line of type Balance."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid "A bank account can belong to only one journal."
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_bank_statement_tree
msgid ""
"A bank statement is a summary of all financial transactions\n"
"                occurring over a given period of time on a bank account. You\n"
"                should receive this periodicaly from your bank."
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_bank_statement_line
msgid "A bank statement line is a financial transaction on a bank account"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_move_journal_line
msgid ""
"A journal entry consists of several journal items, each of\n"
"                which is either a debit or a credit transaction."
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_journal_form
msgid ""
"A journal is used to record transactions of all accounting data\n"
"                related to the day-to-day business."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"A payment journal entry generated in a journal configured to post entries "
"only when payments are reconciled with a bank statement cannot be manually "
"posted. Those will be posted automatically after performing the bank "
"reconciliation."
msgstr ""

#. module: account
#: code:addons/account/models/reconciliation_widget.py:0
#, python-format
msgid "A reconciliation must involve at least 2 move lines."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"A rounding per line is advised if your prices are tax-included. That way, "
"the sum of line subtotals equals the total with taxes."
msgstr ""

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid "A selected move line was already reconciled."
msgstr ""

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid "A selected statement line was already reconciled with an account move."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid "A tag defined to be used on taxes must always have a country set."
msgstr ""

#. module: account
#: model:ir.model.constraint,message:account.constraint_account_fiscal_position_tax_tax_src_dest_uniq
msgid "A tax fiscal position could be defined only one time on same taxes."
msgstr ""

#. module: account
#: code:addons/account/models/res_users.py:0
#, python-format
msgid ""
"A user cannot have both Tax B2B and Tax B2C.\n"
"You should go in General Settings, and choose to display Product Prices\n"
"either in 'Tax-Included' or in 'Tax-Excluded' mode\n"
"(or switch twice the mode if you are already in the desired one)."
msgstr ""

#. module: account
#: model:res.groups,name:account.group_warning_account
msgid "A warning can be set on a partner (Account)"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__access_warning
msgid "Access warning"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:0
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: model:ir.model,name:account.model_account_account
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__account_id
#: model:ir.model.fields,field_description:account.field_account_move_line__account_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__account_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__account_id
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line__account_id
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line_template__account_id
#: model_terms:ir.ui.view,arch_db:account.report_journal
#: model_terms:ir.ui.view,arch_db:account.view_account_form
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
#: model_terms:ir.ui.view,arch_db:account.view_account_search
#, python-format
msgid "Account"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"Account %s (%s) does not allow reconciliation. First change the "
"configuration of this account to allow it."
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_cash_rounding
msgid "Account Cash Rounding"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_common_report
msgid "Account Common Report"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__currency_id
#: model:ir.model.fields,field_description:account.field_account_account_template__currency_id
msgid "Account Currency"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__account_dest_id
msgid "Account Destination"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Account Entry"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_group
#: model_terms:ir.ui.view,arch_db:account.view_account_group_form
#: model_terms:ir.ui.view,arch_db:account.view_account_group_tree
msgid "Account Group"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_account_group_tree
msgid "Account Groups"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__company_partner_id
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__partner_id
msgid "Account Holder"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__acc_holder_name
msgid "Account Holder Name"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_invoice_send
msgid "Account Invoice Send"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__journal_id
#: model:ir.model.fields,field_description:account.field_res_partner_bank__journal_id
#: model_terms:ir.ui.view,arch_db:account.view_account_bank_journal_tree
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_tree
msgid "Account Journal"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_journal_group
msgid "Account Journal Group"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_report_account_report_journal
msgid "Account Journal Report"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__account_ids
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__account_ids
#: model_terms:ir.ui.view,arch_db:account.view_account_position_form
msgid "Account Mapping"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_move_reversal
msgid "Account Move Reversal"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__bank_acc_number
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__acc_number
msgid "Account Number"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__property_account_payable_id
#: model:ir.model.fields,field_description:account.field_res_users__property_account_payable_id
msgid "Account Payable"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_print_journal
msgid "Account Print Journal"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_category_property_form
msgid "Account Properties"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__property_account_receivable_id
#: model:ir.model.fields,field_description:account.field_res_users__property_account_receivable_id
msgid "Account Receivable"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_reconciliation_widget
msgid "Account Reconciliation widget"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__account_root_id
msgid "Account Root"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__account_src_id
msgid "Account Source"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_bank_statement_graph
#: model_terms:ir.ui.view,arch_db:account.account_bank_statement_pivot
#: model_terms:ir.ui.view,arch_db:account.account_move_line_graph_date
msgid "Account Statistics"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_account_tag
msgid "Account Tag"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.account_tag_action
msgid "Account Tags"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_tax_view_tree
#: model_terms:ir.ui.view,arch_db:account.view_tax_form
#: model_terms:ir.ui.view,arch_db:account.view_tax_tree
msgid "Account Tax"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_tax_group_tree
msgid "Account Tax Group"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_tax_report_line
msgid "Account Tax Report Line"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_form
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_tree
msgid "Account Tax Template"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_taxcloud
msgid "Account TaxCloud"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
#: model_terms:ir.ui.view,arch_db:account.view_account_template_form
#: model_terms:ir.ui.view,arch_db:account.view_account_template_search
#: model_terms:ir.ui.view,arch_db:account.view_account_template_tree
msgid "Account Template"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_stock_valuation_account_id
#: model:ir.model.fields,field_description:account.field_res_company__property_stock_valuation_account_id
msgid "Account Template for Stock Valuation"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_account_template_form
msgid "Account Templates"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_account_type
#: model:ir.model.fields,field_description:account.field_account_account_type__name
#: model:ir.model.fields,field_description:account.field_account_accrual_accounting_wizard__account_type
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__related_acc_type
#: model_terms:ir.ui.view,arch_db:account.view_account_search
#: model_terms:ir.ui.view,arch_db:account.view_account_template_search
#: model_terms:ir.ui.view,arch_db:account.view_account_type_form
#: model_terms:ir.ui.view,arch_db:account.view_account_type_search
#: model_terms:ir.ui.view,arch_db:account.view_account_type_tree
msgid "Account Type"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_account__user_type_id
msgid ""
"Account Type is used for information purpose, to generate country-specific "
"legal reports, and set the rules to close a fiscal year and generate opening"
" entries."
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_account_type_form
msgid "Account Types"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__type_control_ids
msgid "Account Types Allowed"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_unreconcile
msgid "Account Unreconcile"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_root
msgid "Account codes first 2 digits"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_group_search
msgid "Account group"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_group_search
msgid "Account groups"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_setup_bank_manual_config__acc_holder_name
msgid ""
"Account holder name, in case it is different than the name of the Account "
"Holder"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__account_src_id
msgid "Account on Product"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_repartition_line__account_id
#: model:ir.model.fields,help:account.field_account_tax_repartition_line_template__account_id
msgid "Account on which to post the tax amount"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_report_account_report_invoice_with_payments
msgid "Account report with payment lines"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template__tag_ids
msgid "Account tag"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__cash_basis_base_account_id
#: model:ir.model.fields,help:account.field_account_tax_template__cash_basis_base_account_id
msgid ""
"Account that will be set on lines created in cash basis journal entry and "
"used to keep track of the tax base amount."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__account_dest_id
msgid "Account to Use Instead"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_accrual_accounting_wizard__revenue_accrual_account
#: model:ir.model.fields,help:account.field_res_company__revenue_accrual_account_id
msgid "Account used to move the period of a revenue"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_accrual_accounting_wizard__expense_accrual_account
#: model:ir.model.fields,help:account.field_res_company__expense_accrual_account_id
msgid "Account used to move the period of an expense"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__cash_basis_transition_account_id
#: model:ir.model.fields,help:account.field_account_tax_template__cash_basis_transition_account_id
msgid ""
"Account used to transition the tax amount for cash basis taxes. It will "
"contain the tax amount as long as the original invoice has not been "
"reconciled ; at reconciliation, this amount cancelled on this account and "
"put on the regular tax account."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_search
msgid "Account with Entries"
msgstr ""

#. module: account
#: model:ir.actions.server,name:account.ir_cron_auto_post_draft_entry_ir_actions_server
#: model:ir.cron,cron_name:account.ir_cron_auto_post_draft_entry
#: model:ir.cron,name:account.ir_cron_auto_post_draft_entry
msgid "Account; Post draft entries with auto_post set to True up to today"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_accountant
#: model:ir.ui.menu,name:account.account_account_menu
#: model:ir.ui.menu,name:account.menu_finance_entries
#: model_terms:ir.ui.view,arch_db:account.product_template_form_view
#: model_terms:ir.ui.view,arch_db:account.view_account_analytic_line_form_inherit_account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Accounting"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__accounting_date
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Accounting Date"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "Accounting Documents"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Accounting Entries"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.open_account_journal_dashboard_kanban
msgid "Accounting Overview"
msgstr ""

#. module: account
#: code:addons/account/models/company.py:0
#: model_terms:ir.ui.view,arch_db:account.onboarding_fiscal_year_step
#, python-format
msgid "Accounting Periods"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Accounting-related settings are managed on"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_account_tag__applicability__accounts
#: model_terms:ir.ui.view,arch_db:account.tax_adjustments_wizard
#: model_terms:ir.ui.view,arch_db:account.view_account_search
msgid "Accounts"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__account_control_ids
msgid "Accounts Allowed"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_position_template_form
msgid "Accounts Mapping"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_account_template
msgid "Accounts Mapping Template of Fiscal Position"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_account
msgid "Accounts Mapping of Fiscal Position"
msgstr ""

#. module: account
#: code:addons/account/wizard/account_accrual_accounting.py:0
#, python-format
msgid ""
"Accrual Adjusting Entries ({percent}%% recognized) have been created for "
"this invoice on {date}"
msgstr ""

#. module: account
#: code:addons/account/wizard/account_accrual_accounting.py:0
#: code:addons/account/wizard/account_accrual_accounting.py:0
#, python-format
msgid "Accrual Adjusting Entry (%s%% recognized) for invoice: %s"
msgstr ""

#. module: account
#: code:addons/account/wizard/account_accrual_accounting.py:0
#: code:addons/account/wizard/account_accrual_accounting.py:0
#, python-format
msgid "Accrual Adjusting Entry ({percent}% recognized) for invoice:"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_accrual_accounting_wizard__journal_id
#: model:ir.model.fields,field_description:account.field_res_company__accrual_default_journal_id
msgid "Accrual Default Journal"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_accrual_accounting_wizard_form_view
msgid "Accrued Account"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_needaction
#: model:ir.model.fields,field_description:account.field_account_journal__message_needaction
#: model:ir.model.fields,field_description:account.field_account_move__message_needaction
#: model:ir.model.fields,field_description:account.field_account_payment__message_needaction
msgid "Action Needed"
msgstr ""

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_entries_actions
msgid "Actions"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Activate Other Currencies"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag__active
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__active
#: model:ir.model.fields,field_description:account.field_account_incoterms__active
#: model:ir.model.fields,field_description:account.field_account_journal__active
#: model:ir.model.fields,field_description:account.field_account_payment_term__active
#: model:ir.model.fields,field_description:account.field_account_tax__active
#: model:ir.model.fields,field_description:account.field_account_tax_template__active
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Active"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_accrual_accounting_wizard__active_move_line_ids
msgid "Active Move Line"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__active_domain
msgid "Active domain"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__activity_ids
#: model:ir.model.fields,field_description:account.field_account_move__activity_ids
#: model:ir.model.fields,field_description:account.field_account_payment__activity_ids
msgid "Activities"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__activity_exception_decoration
#: model:ir.model.fields,field_description:account.field_account_move__activity_exception_decoration
#: model:ir.model.fields,field_description:account.field_account_payment__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__activity_state
#: model:ir.model.fields,field_description:account.field_account_move__activity_state
#: model:ir.model.fields,field_description:account.field_account_payment__activity_state
msgid "Activity State"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:0
#, python-format
msgid "Add"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Add Credit Note"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__add_sign
msgid "Add Sign"
msgstr ""

#. module: account
#: model:ir.actions.server,name:account.action_new_bank_setting
#: model:ir.ui.menu,name:account.menu_action_account_bank_journal_form
msgid "Add a Bank Account"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_bank_account_step
msgid "Add a bank"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_journal_form
msgid "Add a journal"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_journal_group_list
msgid "Add a journal group"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Add a line"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_form
msgid "Add a new account"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.account_tag_action
msgid "Add a new tag"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Add a note"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Add a payment QR code to your invoices"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_cash_rounding__strategy__add_invoice_line
msgid "Add a rounding line"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__has_second_line
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__has_second_line
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Add a second line"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Add a section"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Add an EPC QR code to your invoices so that your customers can pay instantly"
" with their mobile banking application. EPC QR codes are used by many "
"European banks to process SEPA payments."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Add an internal note..."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid "Add contacts to notify..."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__partner_ids
msgid "Additional Contacts"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_repartition_line_template__tag_ids
msgid ""
"Additional tags that will be assigned by this repartition line for use in "
"financial reports"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__adjustment_type
msgid "Adjustment Type"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_group__property_advance_tax_payment_account_id
msgid "Advance Tax payment account"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_advance_tax_payment_account_id
msgid "Advance tax payment account"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_form
#: model_terms:ir.ui.view,arch_db:account.view_tax_form
msgid "Advanced Options"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Advanced Settings"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__include_base_amount
msgid "Affect Base of Subsequent Taxes"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template__include_base_amount
msgid "Affect Subsequent Taxes"
msgstr ""

#. module: account
#: model:ir.actions.report,name:account.action_report_aged_partner_balance
msgid "Aged Partner Balance"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_report_account_report_agedpartnerbalance
msgid "Aged Partner Balance Report"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__alias_id
msgid "Alias"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__alias_name
msgid "Alias Name"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__alias_domain
msgid "Alias domain"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_common_journal_report__target_move__all
#: model:ir.model.fields.selection,name:account.selection__account_common_report__target_move__all
#: model:ir.model.fields.selection,name:account.selection__account_print_journal__target_move__all
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "All Entries"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__all_lines_reconciled
msgid "All Lines Reconciled"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_common_journal_report__target_move__posted
#: model:ir.model.fields.selection,name:account.selection__account_common_report__target_move__posted
#: model:ir.model.fields.selection,name:account.selection__account_print_journal__target_move__posted
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "All Posted Entries"
msgstr ""

#. module: account
#: code:addons/account/wizard/account_accrual_accounting.py:0
#, python-format
msgid "All accounts on the lines must be from the same type."
msgstr ""

#. module: account
#: code:addons/account/models/company.py:0
#, python-format
msgid "All entries are hashed."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid ""
"All invoices and payments have been matched, your accounts' balances are "
"clean."
msgstr ""

#. module: account
#: code:addons/account/wizard/account_accrual_accounting.py:0
#, python-format
msgid "All lines must be from the same company."
msgstr ""

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid ""
"All the account entries lines must be processed in order to close the "
"statement."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template__reconcile
msgid "Allow Invoices & payments Matching"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_product_margin
msgid "Allow Product Margin"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__reconcile
msgid "Allow Reconciliation"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_check_printing
msgid "Allow check printing and deposits"
msgstr ""

#. module: account
#: model:res.groups,name:account.group_cash_rounding
msgid "Allow the cash rounding management"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Allow to configure taxes using cash basis"
msgstr ""

#. module: account
#: model:res.groups,name:account.group_fiscal_year
msgid "Allow to define fiscal years of more or less than a year"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Allows to tag analytic entries and to manage analytic distributions"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Allows you to use the analytic accounting."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__amount
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__amount
#: model:ir.model.fields,field_description:account.field_account_payment__amount
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_amount
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_amount
#: model:ir.model.fields,field_description:account.field_account_tax__amount
#: model:ir.model.fields,field_description:account.field_account_tax_template__amount
#: model:ir.model.fields,field_description:account.field_cash_box_out__amount
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__amount
#: model_terms:ir.ui.view,arch_db:account.account_accrual_accounting_wizard_form_view
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_tree
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
#, python-format
msgid "Amount"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__amount_currency
msgid "Amount Currency"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__amount_residual
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
#: model_terms:ir.ui.view,arch_db:account.view_invoice_tree
msgid "Amount Due"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__amount_residual_signed
msgid "Amount Due Signed"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_total_amount
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_total_amount
msgid "Amount Matching"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_total_amount_param
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_total_amount_param
msgid "Amount Matching %"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_amount_max
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_amount_max
msgid "Amount Max Parameter"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_amount_min
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_amount_min
msgid "Amount Min Parameter"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_nature
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_nature
msgid "Amount Nature"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__match_nature__amount_paid
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__match_nature__amount_paid
msgid "Amount Paid"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__match_nature__both
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__match_nature__both
msgid "Amount Paid/Received"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__match_nature__amount_received
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__match_nature__amount_received
msgid "Amount Received"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__amount_type
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__amount_type
msgid "Amount Type"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_partial_reconcile__amount
msgid "Amount concerned by this matching. Assumed to be always positive"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__amount_from_label_regex
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__amount_from_label_regex
msgid "Amount from Label (regex)"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__amount_currency
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__amount_currency
msgid "Amount in Currency"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Amount type"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:0
#, python-format
msgid "Amount:"
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid "An Off-Balance account can not be reconcilable"
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid "An Off-Balance account can not have taxes"
msgstr ""

#. module: account
#: model:ir.model.constraint,message:account.constraint_account_fiscal_position_account_account_src_dest_uniq
msgid ""
"An account fiscal position could be defined only one time on same accounts."
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_form
msgid ""
"An account is part of a ledger allowing your company\n"
"                to register all kinds of debit and credit transactions.\n"
"                Companies present their annual accounts in two main parts: the\n"
"                balance sheet and the income statement (profit and loss\n"
"                account). The annual accounts of a company are required by law\n"
"                to disclose a certain amount of information."
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_type_form
msgid ""
"An account type is used to determine how an account is used in\n"
"                each journal. The deferral method of an account type determines\n"
"                the process for the annual closing. Reports such as the Balance\n"
"                Sheet and the Profit and Loss report use the category\n"
"                (profit/loss or balance sheet)."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"An error occured when computing the inalterability. Impossible to get the "
"unique previous posted journal entry."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "Analytic"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Analytic Acc."
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_analytic_account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__analytic_account_id
#: model:ir.model.fields,field_description:account.field_account_move_line__analytic_account_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__analytic_account_id
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search_analytic_accounting
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Analytic Account"
msgstr ""

#. module: account
#: model:ir.ui.menu,name:account.account_analytic_group_menu
msgid "Analytic Account Groups"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__group_analytic_accounting
#: model:ir.ui.menu,name:account.menu_analytic_accounting
msgid "Analytic Accounting"
msgstr ""

#. module: account
#: model:ir.ui.menu,name:account.account_analytic_def_account
msgid "Analytic Accounts"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template__analytic
msgid "Analytic Cost"
msgstr ""

#. module: account
#: model:ir.ui.menu,name:account.menu_action_analytic_lines_tree
msgid "Analytic Items"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_analytic_line
msgid "Analytic Line"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "Analytic Lines"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_analytic_tag
#: model:ir.model.fields,field_description:account.field_account_move_line__analytic_tag_ids
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__analytic_tag_ids
#: model:ir.model.fields,field_description:account.field_res_config_settings__group_analytic_tags
#: model:ir.ui.menu,name:account.account_analytic_tag_menu
msgid "Analytic Tags"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Analytic Tags."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__analytic_line_ids
msgid "Analytic lines"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Analytics"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__no_auto_thread
msgid ""
"Answers do not go in the original document discussion thread. This has an "
"impact on the generated message-id."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__tax_exigible
msgid "Appears in VAT report"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag__applicability
msgid "Applicability"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__tax_adjustments_wizard__adjustment_type__credit
msgid "Applied on credit journal item"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__tax_adjustments_wizard__adjustment_type__debit
msgid "Applied on debit journal item"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_onboarding_sale_tax_form
#: model_terms:ir.ui.view,arch_db:account.setup_financial_year_opening_form
msgid "Apply"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position__auto_apply
#: model:ir.model.fields,help:account.field_account_fiscal_position_template__auto_apply
msgid "Apply automatically this fiscal position."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position__country_group_id
#: model:ir.model.fields,help:account.field_account_fiscal_position_template__country_group_id
msgid "Apply only if delivery or invoicing country match the group."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position__country_id
#: model:ir.model.fields,help:account.field_account_fiscal_position_template__country_id
msgid "Apply only if delivery or invoicing country match."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position__vat_required
#: model:ir.model.fields,help:account.field_account_fiscal_position_template__vat_required
msgid "Apply only if partner has a VAT number."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Apply right VAT rates for digital products sold in EU"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_company__fiscalyear_last_month__4
msgid "April"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_incoterms_form
#: model_terms:ir.ui.view,arch_db:account.account_incoterms_view_search
#: model_terms:ir.ui.view,arch_db:account.account_tag_view_form
#: model_terms:ir.ui.view,arch_db:account.account_tag_view_search
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_search
#: model_terms:ir.ui.view,arch_db:account.view_account_position_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_position_form
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_search
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_form
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_search
msgid "Archived"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_account_type__internal_group__asset
msgid "Asset"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/account_selection.js:0
#: model_terms:ir.ui.view,arch_db:account.view_account_search
#, python-format
msgid "Assets"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__account_ids
msgid "Associated Account Templates"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__at_least_one_inbound
msgid "At Least One Inbound"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__at_least_one_outbound
msgid "At Least One Outbound"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid "Attach a file"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_attachment_count
#: model:ir.model.fields,field_description:account.field_account_journal__message_attachment_count
#: model:ir.model.fields,field_description:account.field_account_move__message_attachment_count
#: model:ir.model.fields,field_description:account.field_account_payment__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__attachment_ids
msgid "Attachments"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_company__fiscalyear_last_month__8
msgid "August"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__author_id
msgid "Author"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__author_id
msgid ""
"Author of the message. If not set, email_from may hold an email address that"
" did not match any partner."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Auto-Complete"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move__invoice_vendor_bill_id
msgid "Auto-complete from a past bill."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__auto_reconcile
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__auto_reconcile
msgid "Auto-validate"
msgstr ""

#. module: account
#: code:addons/account/models/company.py:0
#, python-format
msgid "Automatic Balancing Line"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_currency_rate_live
msgid "Automatic Currency Rates"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Automatic Import"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__price_average
msgid "Average Price"
msgstr ""

#. module: account
#: code:addons/account/models/chart_template.py:0
#, python-format
msgid "BILL"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_partner__trust__bad
msgid "Bad Debtor"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__balance
#: model:ir.model.fields.selection,name:account.selection__account_payment_term_line__value__balance
msgid "Balance"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__balance_end
msgid "Balance as calculated based on Opening Balance and transaction lines"
msgstr ""

#. module: account
#: code:addons/account/models/chart_template.py:0
#: model:ir.model.fields,field_description:account.field_account_journal__bank_id
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__bank_id
#: model:ir.model.fields,field_description:account.field_res_partner__bank_account_count
#: model:ir.model.fields,field_description:account.field_res_users__bank_account_count
#: model:ir.model.fields.selection,name:account.selection__account_journal__type__bank
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
#, python-format
msgid "Bank"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Bank &amp; Cash"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__bank_account_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__invoice_partner_bank_id
#: model:ir.model.fields,field_description:account.field_account_journal__bank_account_id
#: model:ir.model.fields,field_description:account.field_account_move__invoice_partner_bank_id
#: model_terms:ir.ui.view,arch_db:account.onboarding_bank_account_step
#: model_terms:ir.ui.view,arch_db:account.view_account_bank_journal_form
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Bank Account"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_bank_journal_form
msgid "Bank Account Name"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__account_number
msgid "Bank Account Number"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move__invoice_partner_bank_id
msgid ""
"Bank Account Number to which the invoice will be paid. A Company bank "
"account if this is a Customer Invoice or Vendor Credit Note, otherwise a "
"Partner bank account number."
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_account_bank_journal_form
#: model:ir.model,name:account.model_res_partner_bank
#: model:ir.ui.menu,name:account.menu_action_account_invoice_bank_journal_form
#: model_terms:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Bank Accounts"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__bank_statements_source
msgid "Bank Feeds"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__bank_bic
msgid "Bank Identifier Code"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_yodlee
msgid "Bank Interface - Sync your bank feeds automatically"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__bank_journal_ids
msgid "Bank Journals"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__bank_partner_id
msgid "Bank Partner"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_action.js:0
#: model:ir.model.fields.selection,name:account.selection__account_journal__post_at__bank_rec
#, python-format
msgid "Bank Reconciliation"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_tree
msgid "Bank Reconciliation Move Presets"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_search
msgid "Bank Reconciliation Move preset"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_bank_reconciliation_start
#: model:ir.model.fields,field_description:account.field_res_config_settings__account_bank_reconciliation_start
msgid "Bank Reconciliation Threshold"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_bank_statement
#: model_terms:ir.ui.view,arch_db:account.report_statement
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_form
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Bank Statement"
msgstr ""

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid "Bank Statement %s"
msgstr ""

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid "Bank Statement %s.pdf"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_bank_statement_cashbox
msgid "Bank Statement Cashbox"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_bank_statement_closebalance
msgid "Bank Statement Closing Balance"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_bank_statement_line
msgid "Bank Statement Lines"
msgstr ""

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid "Bank Statement.pdf"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_bank_statement_tree
#: model_terms:ir.ui.view,arch_db:account.view_account_bank_journal_form
msgid "Bank Statements"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line__bank_account_id
msgid "Bank account that was used in this transaction."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_setup_bank_manual_config__acc_type
msgid ""
"Bank account type: Normal or IBAN. Inferred from the bank account number."
msgstr ""

#. module: account
#: model:account.account.type,name:account.data_account_type_liquidity
#: model:ir.actions.act_window,name:account.action_account_moves_journal_bank_cash
#: model:ir.ui.menu,name:account.menu_action_account_moves_journal_bank_cash
msgid "Bank and Cash"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_setup_bank_manual_config
msgid "Bank setup manual config"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__statement_line_id
msgid "Bank statement line reconciled with this entry"
msgstr ""

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:0
#, python-format
msgid "Bank: Balance"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_tax_repartition_line__repartition_type__base
#: model:ir.model.fields.selection,name:account.selection__account_tax_repartition_line_template__repartition_type__base
msgid "Base"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__tax_base_amount
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "Base Amount"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__cash_basis_base_account_id
#: model:ir.model.fields,field_description:account.field_account_tax_template__cash_basis_base_account_id
msgid "Base Tax Received Account"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_repartition_line__repartition_type
#: model:ir.model.fields,help:account.field_account_tax_repartition_line_template__repartition_type
msgid "Base on which the factor will be applied."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line__repartition_type
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line_template__repartition_type
msgid "Based On"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_journal__invoice_reference_type__partner
msgid "Based on Customer"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_journal__invoice_reference_type__invoice
#: model:ir.model.fields.selection,name:account.selection__account_tax__tax_exigibility__on_invoice
#: model:ir.model.fields.selection,name:account.selection__account_tax_template__tax_exigibility__on_invoice
msgid "Based on Invoice"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__tax_exigibility
#: model:ir.model.fields,help:account.field_account_tax_template__tax_exigibility
msgid ""
"Based on Invoice: the tax is due as soon as the invoice is validated.\n"
"Based on Payment: the tax is due as soon as the payment of the invoice is received."
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_tax__tax_exigibility__on_payment
#: model:ir.model.fields.selection,name:account.selection__account_tax_template__tax_exigibility__on_payment
msgid "Based on Payment"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Batch Payments"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Bill"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_invoice_tree
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Bill Date"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_invoice_extract
msgid "Bill Digitalization"
msgstr ""

#. module: account
#: model:res.groups,name:account.group_account_invoice
msgid "Billing"
msgstr ""

#. module: account
#: model:res.groups,name:account.group_account_manager
msgid "Billing Administrator"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_move_in_invoice_type
#: model:ir.ui.menu,name:account.menu_action_move_in_invoice_type
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Bills"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Bills Analysis"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Bills to Pay"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Bills to Validate"
msgstr ""

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:0
#, python-format
msgid "Bills to pay"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_partner__invoice_warn__block
msgid "Blocking Message"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_type__include_initial_balance
msgid "Bring Accounts Balance Forward"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.open_account_journal_dashboard_kanban
msgid "Browse available countries."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_budget
msgid "Budget Management"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__name
msgid "Button Label"
msgstr ""

#. module: account
#: model:ir.filters,name:account.filter_invoice_country
msgid "By Country"
msgstr ""

#. module: account
#: model:ir.filters,name:account.filter_invoice_refund
msgid "By Credit Note"
msgstr ""

#. module: account
#: model:ir.filters,name:account.filter_invoice_product
msgid "By Product"
msgstr ""

#. module: account
#: model:ir.filters,name:account.filter_invoice_product_category
msgid "By Product Category"
msgstr ""

#. module: account
#: model:ir.filters,name:account.filter_invoice_report_salespersons
msgid "By Salespersons"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position__active
msgid ""
"By unchecking the active field, you may hide a fiscal position without "
"deleting it."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_incoterms__active
msgid ""
"By unchecking the active field, you may hide an INCOTERM you will not use."
msgstr ""

#. module: account
#: code:addons/account/models/chart_template.py:0
#: code:addons/account/models/chart_template.py:0
#, python-format
msgid "CABA"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "CAMT Import"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_CIP
msgid "CARRIAGE AND INSURANCE PAID TO"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_CPT
msgid "CARRIAGE PAID TO"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_CFR
msgid "COST AND FREIGHT"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_CIF
msgid "COST, INSURANCE AND FREIGHT"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "CSV Import"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "CUST"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__visible
msgid "Can be Visible?"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_accrual_accounting_wizard_form_view
#: model_terms:ir.ui.view,arch_db:account.account_common_report_view
#: model_terms:ir.ui.view,arch_db:account.account_invoice_onboarding_sale_tax_form
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
#: model_terms:ir.ui.view,arch_db:account.account_unreconcile_view
#: model_terms:ir.ui.view,arch_db:account.cash_box_out_form
#: model_terms:ir.ui.view,arch_db:account.setup_bank_account_wizard
#: model_terms:ir.ui.view,arch_db:account.setup_financial_year_opening_form
#: model_terms:ir.ui.view,arch_db:account.tax_adjustments_wizard
#: model_terms:ir.ui.view,arch_db:account.validate_account_move_view
#: model_terms:ir.ui.view,arch_db:account.view_account_bnk_stmt_cashbox_footer
#: model_terms:ir.ui.view,arch_db:account.view_account_bnk_stmt_check
#: model_terms:ir.ui.view,arch_db:account.view_account_move_reversal
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_form
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_form_multi
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_invoice_form
msgid "Cancel"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Cancel Entry"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_invoice_report__state__cancel
#: model:ir.model.fields.selection,name:account.selection__account_move__state__cancel
#: model:ir.model.fields.selection,name:account.selection__account_payment__state__cancelled
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Cancelled"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "Cancelled Invoice"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Cannot create an invoice of type %s with a journal having %s as type."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"Cannot create unbalanced journal entry. Ids: %s\n"
"Differences debit - credit: %s"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"Cannot find a chart of accounts for this company, You should configure it. \n"
"Please go to Account Configuration."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#: code:addons/account/models/chart_template.py:0
#, python-format
msgid "Cannot generate an unused account code."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"Cannot generate an unused journal code. Please fill the 'Shortcode' field."
msgstr ""

#. module: account
#: code:addons/account/models/chart_template.py:0
#: model:ir.model.fields.selection,name:account.selection__account_journal__type__cash
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
#, python-format
msgid "Cash"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__tax_exigibility
msgid "Cash Basis"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__tax_cash_basis_journal_id
msgid "Cash Basis Journal"
msgstr ""

#. module: account
#: code:addons/account/models/chart_template.py:0
#, python-format
msgid "Cash Basis Taxes"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__cash_basis_transition_account_id
#: model:ir.model.fields,field_description:account.field_account_tax_template__cash_basis_transition_account_id
msgid "Cash Basis Transition Account"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_cash_box_out
msgid "Cash Box Out"
msgstr ""

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid "Cash Control"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__default_cash_difference_expense_account_id
#: model:ir.model.fields,field_description:account.field_res_company__default_cash_difference_expense_account_id
msgid "Cash Difference Expense Account"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__default_cash_difference_income_account_id
#: model:ir.model.fields,field_description:account.field_res_company__default_cash_difference_income_account_id
msgid "Cash Difference Income Account"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_view_bank_statement_tree
msgid "Cash Registers"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__group_cash_rounding
msgid "Cash Rounding"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__invoice_cash_rounding_id
msgid "Cash Rounding Method"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.rounding_list_action
#: model:ir.ui.menu,name:account.menu_action_rounding_form_view
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Cash Roundings"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_statement
msgid "Cash Statement"
msgstr ""

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid "Cash difference observed during the counting (%s)"
msgstr ""

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:0
#, python-format
msgid "Cash: Balance"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_cashbox_line
msgid "CashBox Line"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__cashbox_id
msgid "Cashbox"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__cashbox_lines_ids
msgid "Cashbox Lines"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_account_expense_categ_id
msgid "Category of Expense Account"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_account_income_categ_id
msgid "Category of Income Account"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_payment__writeoff_label
msgid "Change label of the counterpart that will hold the payment difference"
msgstr ""

#. module: account
#: code:addons/account/controllers/portal.py:0
#, python-format
msgid ""
"Changing VAT number is not allowed once invoices have been issued for your "
"account. Please contact us directly for this operation."
msgstr ""

#. module: account
#: code:addons/account/controllers/portal.py:0
#, python-format
msgid ""
"Changing your company name is not allowed once invoices have been issued for"
" your account. Please contact us directly for this operation."
msgstr ""

#. module: account
#: code:addons/account/controllers/portal.py:0
#, python-format
msgid ""
"Changing your name is not allowed once invoices have been issued for your "
"account. Please contact us directly for this operation."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template__chart_template_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__chart_template_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__chart_template_id
#: model:ir.model.fields,field_description:account.field_account_tax_template__chart_template_id
#: model:ir.model.fields,field_description:account.field_res_company__chart_template_id
msgid "Chart Template"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.open_account_charts_modules
msgid "Chart Templates"
msgstr ""

#. module: account
#: code:addons/account/models/company.py:0
#: model:ir.actions.act_window,name:account.action_account_form
#: model:ir.ui.menu,name:account.menu_action_account_form
#: model_terms:ir.ui.view,arch_db:account.onboarding_chart_of_account_step
#, python-format
msgid "Chart of Accounts"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_form
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_tree
msgid "Chart of Accounts Template"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_account_chart_template_form
msgid "Chart of Accounts Templates"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_chart_of_account_step
msgid "Chart of account set."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_list
msgid "Chart of accounts"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_view_account_bnk_stmt_check
msgid "Check Closing Balance"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Check all"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__is_difference_zero
msgid "Check if difference is zero."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Check that you have no bank statement lines to"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Check them"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_account__reconcile
msgid ""
"Check this box if this account allows invoices & payments matching of "
"journal items."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__refund_sequence
msgid ""
"Check this box if you don't want to share the same sequence for invoices and"
" credit notes made from this journal"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_account_tag__tax_negate
msgid ""
"Check this box to negate the absolute value of the balance of the lines "
"associated with this tag in tax report computation."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__price_include
#: model:ir.model.fields,help:account.field_account_tax_template__price_include
msgid ""
"Check this if the price you use on the product and invoices includes this "
"tax."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template__reconcile
msgid ""
"Check this option if you want the user to reconcile entries in this account."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Checks"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_report_line__children_line_ids
msgid "Children Lines"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__children_tax_ids
#: model:ir.model.fields,field_description:account.field_account_tax_template__children_tax_ids
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_form
#: model_terms:ir.ui.view,arch_db:account.view_tax_form
msgid "Children Taxes"
msgstr ""

#. module: account
#: code:addons/account/models/chart_template.py:0
#, python-format
msgid "Choose Accounting Template"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_onboarding_sale_tax_form
#: model_terms:ir.ui.view,arch_db:account.onboarding_sale_tax_step
msgid "Choose a default sales tax for your products."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Choose counterpart or Create Write-off"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_reversal__refund_method
msgid ""
"Choose how you want to credit this invoice. You cannot \"modify\" nor "
"\"cancel\" if the invoice is already reconciled."
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.actions_account_fiscal_year
msgid "Click here to create a new fiscal year."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/tours/account.js:0
#, python-format
msgid "Click to <b>send the invoice by email.</b>"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/tours/account.js:0
#, python-format
msgid "Click to <b>send the invoice.</b>"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/tours/account.js:0
#, python-format
msgid ""
"Click to <b>validate your invoice.</b> A reference will be assigned to this "
"invoice and you will not be able to modify it anymore."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Close"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Close statement"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_company__account_dashboard_onboarding_state__closed
#: model:ir.model.fields.selection,name:account.selection__res_company__account_invoice_onboarding_state__closed
msgid "Closed"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__date_done
msgid "Closed On"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__code
#: model:ir.model.fields,field_description:account.field_account_account_template__code
#: model:ir.model.fields,field_description:account.field_account_analytic_line__code
#: model:ir.model.fields,field_description:account.field_account_incoterms__code
#: model:ir.model.fields,field_description:account.field_account_payment__payment_method_code
#: model:ir.model.fields,field_description:account.field_account_payment_method__code
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__new_journal_code
#: model:ir.model.fields,field_description:account.field_account_tax_report_line__code
msgid "Code"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_group__code_prefix
msgid "Code Prefix"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__coin_value
msgid "Coin/Bill Value"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Collect customer payments in one-click using Euro SEPA Service"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Collect information and produce statistics on the trade in goods in Europe "
"with intrastat"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag__color
#: model:ir.model.fields,field_description:account.field_account_journal__color
msgid "Color Index"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__commercial_partner_id
#: model:ir.model.fields,help:account.field_account_invoice_report__commercial_partner_id
msgid "Commercial Entity"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_common_journal_report
msgid "Common Journal Report"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_account_common_menu
msgid "Common Report"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__invoice_reference_model
msgid "Communication Standard"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__invoice_reference_type
msgid "Communication Type"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_res_company
msgid "Companies"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__ref_company_ids
#: model:ir.model.fields,field_description:account.field_res_users__ref_company_ids
msgid "Companies that refers to partner"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__company_id
#: model:ir.model.fields,field_description:account.field_account_accrual_accounting_wizard__company_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement__company_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__company_id
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__company_id
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__company_id
#: model:ir.model.fields,field_description:account.field_account_common_report__company_id
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__company_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__company_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__company_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__company_id
#: model:ir.model.fields,field_description:account.field_account_journal__company_id
#: model:ir.model.fields,field_description:account.field_account_journal_group__company_id
#: model:ir.model.fields,field_description:account.field_account_move__company_id
#: model:ir.model.fields,field_description:account.field_account_move_line__company_id
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__company_id
#: model:ir.model.fields,field_description:account.field_account_payment__company_id
#: model:ir.model.fields,field_description:account.field_account_payment_term__company_id
#: model:ir.model.fields,field_description:account.field_account_print_journal__company_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__company_id
#: model:ir.model.fields,field_description:account.field_account_root__company_id
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__company_id
#: model:ir.model.fields,field_description:account.field_account_tax__company_id
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line__company_id
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Company"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__company_currency_id
#: model:ir.model.fields,field_description:account.field_account_move_line__company_currency_id
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__company_currency_id
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__company_currency_id
msgid "Company Currency"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_base_document_layout
msgid "Company Document Layout"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__has_chart_of_accounts
msgid "Company has a chart of accounts"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__company_id
#: model:ir.model.fields,help:account.field_account_bank_statement_line__company_id
#: model:ir.model.fields,help:account.field_account_journal__company_id
#: model:ir.model.fields,help:account.field_account_move__company_id
#: model:ir.model.fields,help:account.field_account_move_line__company_id
#: model:ir.model.fields,help:account.field_account_partial_reconcile__company_id
#: model:ir.model.fields,help:account.field_account_payment__company_id
msgid "Company related to this journal"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__complete_tax_set
msgid "Complete Set of Taxes"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_report_line__name
msgid "Complete name for this report line, to be used in report."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__composer_id
msgid "Composer"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__composition_mode
msgid "Composition mode"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Compute tax rates based on U.S. ZIP codes"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__balance_end
msgid "Computed Balance"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__tax_audit
msgid ""
"Computed field, listing the tax grids impacted by this line, and the amount "
"it applies to each of them."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Conditions on Bank Statement Line"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_configuration
msgid "Configuration"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_hash_integrity
msgid "Configuration review"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.onboarding_fiscal_year_step
msgid "Configure"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_configure_tax_report
msgid "Configure Tax Report"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_bnk_stmt_cashbox_footer
#: model_terms:ir.ui.view,arch_db:account.view_account_bnk_stmt_check
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_form
msgid "Confirm"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Confirmed"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_bnk_stmt_check
msgid ""
"Confirming this will create automatically a journal entry with the "
"difference in the profit/loss account set on the cash journal."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Congrats, you're all done!"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_onboarding_panel
msgid "Congratulations! You are all set."
msgstr ""

#. module: account
#: model:ir.model,name:account.model_res_partner
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_page
msgid "Contact"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__match_label__contains
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__match_note__contains
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__match_transaction_type__contains
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__match_label__contains
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__match_note__contains
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__match_transaction_type__contains
msgid "Contains"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__body
msgid "Contents"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Control-Access"
msgstr ""

#. module: account
#: code:addons/account/models/company.py:0
#, python-format
msgid "Corrupted data on journal entry with id %s."
msgstr ""

#. module: account
#: model:account.account.type,name:account.data_account_type_direct_costs
msgid "Cost of Revenue"
msgstr ""

#. module: account
#: code:addons/account/models/chart_template.py:0
#, python-format
msgid ""
"Could not install new chart of account as there are already accounting "
"entries existing."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__account_id
msgid "Counterpart Account"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Counterpart Values"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag__country_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__country_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__country_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__country_id
#: model:ir.model.fields,field_description:account.field_account_move_line__country_id
#: model:ir.model.fields,field_description:account.field_account_tax__country_id
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line__country_id
#: model:ir.model.fields,field_description:account.field_account_tax_report_line__country_id
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__country_id
#: model_terms:ir.ui.view,arch_db:account.account_tax_report_line_search
msgid "Country"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__country_group_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__country_group_id
msgid "Country Group"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_report_line__country_id
msgid "Country for which this line is available."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_account_tag__country_id
msgid "Country for which this tag is available, when applied on taxes."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_hash_integrity
msgid "Coverage"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.setup_bank_account_wizard
msgid "Create"
msgstr ""

#. module: account
#: model:ir.actions.server,name:account.action_accrual_entry
msgid "Create Accrual Entry"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.account_accrual_accounting_wizard_action
msgid "Create Accrual Entry for the expense/revenue recognition"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_accrual_accounting_wizard_form_view
msgid "Create Journal Entry"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_form_multi
msgid "Create Payment"
msgstr ""

#. module: account
#: code:addons/account/models/company.py:0
#, python-format
msgid "Create a Bank Account"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_bank_journal_form
msgid "Create a bank account"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Create a counterpart"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_move_out_refund_type
msgid "Create a credit note"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_move_out_invoice_type
msgid "Create a customer invoice"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_move_journal_line
msgid "Create a journal entry"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_group_tree
msgid "Create a new account group"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_view_bank_statement_tree
msgid "Create a new cash log"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.res_partner_action_customer
msgid "Create a new customer in your address book"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_fiscal_position_form
#: model_terms:ir.actions.act_window,help:account.action_account_fiscal_position_template_form
msgid "Create a new fiscal position"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_incoterms_tree
msgid "Create a new incoterm"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.product_product_action_purchasable
msgid "Create a new purchasable product"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_reconcile_model
msgid "Create a new reconciliation model"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_move_out_receipt_type
msgid "Create a new sales receipt"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.product_product_action_sellable
msgid "Create a new sellable product"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.res_partner_action_supplier
msgid "Create a new supplier in your address book"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_tax_form
msgid "Create a new tax"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_move_in_invoice_type
msgid "Create a vendor bill"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_move_in_refund_type
msgid "Create a vendor credit note"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_accrual_accounting_wizard
msgid "Create accrual entry."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.tax_adjustments_wizard
msgid "Create and post move"
msgstr ""

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:0
#, python-format
msgid "Create cash statement"
msgstr ""

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:0
#, python-format
msgid "Create invoice/bill"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_move_out_invoice_type
msgid ""
"Create invoices, register payments and keep track of the discussions with "
"your customers."
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_move_in_invoice_type
msgid ""
"Create invoices, register payments and keep track of the discussions with "
"your vendors."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Create model"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.rounding_list_action
msgid "Create the first cash rounding"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__create_uid
#: model:ir.model.fields,field_description:account.field_account_account_tag__create_uid
#: model:ir.model.fields,field_description:account.field_account_account_template__create_uid
#: model:ir.model.fields,field_description:account.field_account_account_type__create_uid
#: model:ir.model.fields,field_description:account.field_account_accrual_accounting_wizard__create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement__create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance__create_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__create_uid
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__create_uid
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__create_uid
#: model:ir.model.fields,field_description:account.field_account_chart_template__create_uid
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__create_uid
#: model:ir.model.fields,field_description:account.field_account_common_report__create_uid
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__create_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__create_uid
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__create_uid
#: model:ir.model.fields,field_description:account.field_account_group__create_uid
#: model:ir.model.fields,field_description:account.field_account_incoterms__create_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_send__create_uid
#: model:ir.model.fields,field_description:account.field_account_journal__create_uid
#: model:ir.model.fields,field_description:account.field_account_journal_group__create_uid
#: model:ir.model.fields,field_description:account.field_account_move__create_uid
#: model:ir.model.fields,field_description:account.field_account_move_line__create_uid
#: model:ir.model.fields,field_description:account.field_account_move_reversal__create_uid
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__create_uid
#: model:ir.model.fields,field_description:account.field_account_payment__create_uid
#: model:ir.model.fields,field_description:account.field_account_payment_method__create_uid
#: model:ir.model.fields,field_description:account.field_account_payment_register__create_uid
#: model:ir.model.fields,field_description:account.field_account_payment_term__create_uid
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__create_uid
#: model:ir.model.fields,field_description:account.field_account_print_journal__create_uid
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__create_uid
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__create_uid
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__create_uid
#: model:ir.model.fields,field_description:account.field_account_tax__create_uid
#: model:ir.model.fields,field_description:account.field_account_tax_group__create_uid
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line__create_uid
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line_template__create_uid
#: model:ir.model.fields,field_description:account.field_account_tax_report_line__create_uid
#: model:ir.model.fields,field_description:account.field_account_tax_template__create_uid
#: model:ir.model.fields,field_description:account.field_account_unreconcile__create_uid
#: model:ir.model.fields,field_description:account.field_cash_box_out__create_uid
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__create_uid
#: model:ir.model.fields,field_description:account.field_validate_account_move__create_uid
msgid "Created by"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Created by: %s"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__create_date
#: model:ir.model.fields,field_description:account.field_account_account_tag__create_date
#: model:ir.model.fields,field_description:account.field_account_account_template__create_date
#: model:ir.model.fields,field_description:account.field_account_account_type__create_date
#: model:ir.model.fields,field_description:account.field_account_accrual_accounting_wizard__create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement__create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance__create_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__create_date
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__create_date
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__create_date
#: model:ir.model.fields,field_description:account.field_account_chart_template__create_date
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__create_date
#: model:ir.model.fields,field_description:account.field_account_common_report__create_date
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__create_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__create_date
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__create_date
#: model:ir.model.fields,field_description:account.field_account_group__create_date
#: model:ir.model.fields,field_description:account.field_account_incoterms__create_date
#: model:ir.model.fields,field_description:account.field_account_invoice_send__create_date
#: model:ir.model.fields,field_description:account.field_account_journal__create_date
#: model:ir.model.fields,field_description:account.field_account_journal_group__create_date
#: model:ir.model.fields,field_description:account.field_account_move__create_date
#: model:ir.model.fields,field_description:account.field_account_move_line__create_date
#: model:ir.model.fields,field_description:account.field_account_move_reversal__create_date
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__create_date
#: model:ir.model.fields,field_description:account.field_account_payment__create_date
#: model:ir.model.fields,field_description:account.field_account_payment_method__create_date
#: model:ir.model.fields,field_description:account.field_account_payment_register__create_date
#: model:ir.model.fields,field_description:account.field_account_payment_term__create_date
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__create_date
#: model:ir.model.fields,field_description:account.field_account_print_journal__create_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__create_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__create_date
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__create_date
#: model:ir.model.fields,field_description:account.field_account_tax__create_date
#: model:ir.model.fields,field_description:account.field_account_tax_group__create_date
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line__create_date
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line_template__create_date
#: model:ir.model.fields,field_description:account.field_account_tax_report_line__create_date
#: model:ir.model.fields,field_description:account.field_account_tax_template__create_date
#: model:ir.model.fields,field_description:account.field_account_unreconcile__create_date
#: model:ir.model.fields,field_description:account.field_cash_box_out__create_date
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__create_date
#: model:ir.model.fields,field_description:account.field_validate_account_move__create_date
msgid "Created on"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__credit
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "Credit"
msgstr ""

#. module: account
#: model:account.account.type,name:account.data_account_type_credit_card
msgid "Credit Card"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_reversal__refund_method
msgid "Credit Method"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__credit_move_id
msgid "Credit Move"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#: code:addons/account/models/account_move.py:0
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
#, python-format
msgid "Credit Note"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Credit Note Created"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__refund_sequence_id
msgid "Credit Note Entry Sequence"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_move_out_refund_type
#: model:ir.ui.menu,name:account.menu_action_move_out_refund_type
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Credit Notes"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__refund_sequence_number_next
msgid "Credit Notes Next Number"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__credit_account_id
msgid "Credit account"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__matched_credit_ids
msgid "Credit journal items that are matched with this journal item."
msgstr ""

#. module: account
#: model:ir.ui.menu,name:account.menu_action_currency_form
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Currencies"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_accrual_accounting_wizard__company_currency_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement__currency_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__currency_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__currency_id
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__currency_id
#: model:ir.model.fields,field_description:account.field_account_chart_template__currency_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__currency_id
#: model:ir.model.fields,field_description:account.field_account_journal__currency_id
#: model:ir.model.fields,field_description:account.field_account_move__currency_id
#: model:ir.model.fields,field_description:account.field_account_move_line__currency_id
#: model:ir.model.fields,field_description:account.field_account_move_reversal__currency_id
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__currency_id
#: model:ir.model.fields,field_description:account.field_account_payment__currency_id
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__currency_id
#: model:ir.model.fields,field_description:account.field_res_config_settings__currency_id
#: model:ir.model.fields,field_description:account.field_res_partner__currency_id
#: model:ir.model.fields,field_description:account.field_res_users__currency_id
#: model_terms:ir.ui.view,arch_db:account.report_journal
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "Currency"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Currency exchange rate difference"
msgstr ""

#. module: account
#: model:account.account.type,name:account.data_account_type_current_assets
msgid "Current Assets"
msgstr ""

#. module: account
#: model:account.account.type,name:account.data_account_type_current_liabilities
msgid "Current Liabilities"
msgstr ""

#. module: account
#: model:account.account.type,name:account.data_unaffected_earnings
msgid "Current Year Earnings"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_payment__partner_type__customer
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_tree
#: model_terms:ir.ui.view,arch_db:account.view_invoice_tree
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Customer"
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:0
#: model:ir.model.fields.selection,name:account.selection__account_invoice_report__type__out_refund
#: model:ir.model.fields.selection,name:account.selection__account_move__type__out_refund
#, python-format
msgid "Customer Credit Note"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_invoice_report__type__out_invoice
#: model:ir.model.fields.selection,name:account.selection__account_move__type__out_invoice
msgid "Customer Invoice"
msgstr ""

#. module: account
#: code:addons/account/models/chart_template.py:0
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
#, python-format
msgid "Customer Invoices"
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:0
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#, python-format
msgid "Customer Payment"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__property_payment_term_id
#: model:ir.model.fields,field_description:account.field_res_users__property_payment_term_id
msgid "Customer Payment Terms"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Customer Payments"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move__access_url
msgid "Customer Portal URL"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__customer_rank
#: model:ir.model.fields,field_description:account.field_res_users__customer_rank
msgid "Customer Rank"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_product_product__taxes_id
#: model:ir.model.fields,field_description:account.field_product_template__taxes_id
msgid "Customer Taxes"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Customer/Vendor"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Customer/Vendor Matching"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.res_partner_action_customer
#: model:ir.ui.menu,name:account.menu_account_customer
#: model:ir.ui.menu,name:account.menu_finance_receivables
#: model_terms:ir.ui.view,arch_db:account.res_partner_view_search
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Customers"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_invoice_layout_step
msgid "Customize"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_invoice_layout_step
msgid "Customize the look of your invoices."
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_DAF
msgid "DELIVERED AT FRONTIER"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_DAP
msgid "DELIVERED AT PLACE"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_DAT
msgid "DELIVERED AT TERMINAL"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_DDP
msgid "DELIVERED DUTY PAID"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_DDU
msgid "DELIVERED DUTY UNPAID"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_DEQ
msgid "DELIVERED EX QUAY"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_DES
msgid "DELIVERED EX SHIP"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_cash_rounding__rounding_method__down
msgid "DOWN"
msgstr ""

#. module: account
#: model:ir.actions.server,name:account.action_check_hash_integrity
msgid "Data Inalterability Check"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_hash_integrity
msgid "Data consistency check"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: model:ir.model.fields,field_description:account.field_account_accrual_accounting_wizard__date
#: model:ir.model.fields,field_description:account.field_account_bank_statement__date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__date
#: model:ir.model.fields,field_description:account.field_account_move__date
#: model:ir.model.fields,field_description:account.field_account_move_line__date
#: model:ir.model.fields,field_description:account.field_account_payment__payment_date
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__date
#: model:ir.model.fields.selection,name:account.selection__account_print_journal__sort_selection__date
#: model_terms:ir.ui.view,arch_db:account.report_journal
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_search
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#, python-format
msgid "Date"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_company__account_opening_date
msgid ""
"Date at which the opening entry of this company's accounting has been "
"posted."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_financial_year_op__opening_date
msgid ""
"Date from which the accounting is managed in Odoo. It is the date of the "
"opening entry."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:0
#, python-format
msgid "Date:"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "Dates"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__day_of_the_month
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_tree
msgid "Day of the month"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term_line__day_of_the_month
msgid ""
"Day of the month on which the invoice must come to its term. If zero or "
"negative, this value will be ignored, and no specific day will be set. If "
"greater than the last day of a month, this number will instead select the "
"last day of this month."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__debit
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "Debit"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__debit_move_id
msgid "Debit Move"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__debit_account_id
msgid "Debit account"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__matched_debit_ids
msgid "Debit journal items that are matched with this journal item."
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_company__fiscalyear_last_month__12
msgid "December"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__decimal_separator
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__decimal_separator
msgid "Decimal Separator"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__refund_sequence
msgid "Dedicated Credit Note Sequence"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__default_credit_account_id
msgid "Default Credit Account"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__default_debit_account_id
msgid "Default Debit Account"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Default Incoterm of your company"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_default_pos_receivable_account_id
msgid "Default PoS Receivable Account"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_purchase_tax_id
#: model:ir.model.fields,field_description:account.field_res_config_settings__purchase_tax_id
msgid "Default Purchase Tax"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_sale_tax_id
#: model:ir.model.fields,field_description:account.field_res_config_settings__sale_tax_id
msgid "Default Sale Tax"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__tax_ids
#: model:ir.model.fields,field_description:account.field_account_account_template__tax_ids
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_form
#: model_terms:ir.ui.view,arch_db:account.view_account_template_form
msgid "Default Taxes"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__use_invoice_terms
msgid "Default Terms & Conditions"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__invoice_terms
msgid "Default Terms and Conditions"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__incoterm_id
#: model:ir.model.fields,field_description:account.field_res_config_settings__incoterm_id
msgid "Default incoterm"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Default taxes applied to local transactions"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_product_product__supplier_taxes_id
#: model:ir.model.fields,help:account.field_product_template__supplier_taxes_id
msgid "Default taxes used when buying the product."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_product_product__taxes_id
#: model:ir.model.fields,help:account.field_product_template__taxes_id
msgid "Default taxes used when selling the product."
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_type_form
msgid "Define a new account type"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Define the smallest coinage of the currency used to pay by cash"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_fiscal_year_step
msgid "Define your fiscal years &amp; tax returns periodicity."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__bank_statements_source
msgid "Defines how the bank statements will be registered"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move__invoice_cash_rounding_id
msgid ""
"Defines the smallest coinage of the currency that can be used to pay by "
"cash."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_form
#: model_terms:ir.ui.view,arch_db:account.view_tax_form
msgid "Definition"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__trust
#: model:ir.model.fields,field_description:account.field_res_users__trust
msgid "Degree of trust you have in this debtor"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__auto_delete
msgid "Delete Emails"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__auto_delete_message
msgid "Delete Message Copy"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__auto_delete
msgid "Delete sent emails (mass mailing only)"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__deprecated
msgid "Deprecated"
msgstr ""

#. module: account
#: model:account.account.type,name:account.data_account_type_depreciation
msgid "Depreciation"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.cash_box_out_form
msgid "Describe why you put/take money from the cash register:"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: model:ir.model.fields,field_description:account.field_account_account_type__note
#: model_terms:ir.ui.view,arch_db:account.view_account_type_form
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#, python-format
msgid "Description"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term__note
msgid "Description on the Invoice"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__destination_account_id
msgid "Destination Account"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__auto_apply
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__auto_apply
msgid "Detect Automatically"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_template__type_tax_use
msgid ""
"Determines where the tax is selectable. Note : 'None' means a tax can't be "
"used by itself, however it can still be used in a group."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__type_tax_use
msgid ""
"Determines where the tax is selectable. Note : 'None' means a tax can't be "
"used by itself, however it can still be used in a group. 'adjustment' is "
"used to perform tax adjustment."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__difference
msgid "Difference"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__writeoff_account_id
msgid "Difference Account"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__difference
msgid ""
"Difference between the computed ending balance and the specified ending "
"balance."
msgstr ""

#. module: account
#: model:ir.model,name:account.model_digest_digest
msgid "Digest"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Digitalize your scanned or PDF vendor bills with OCR and Artificial "
"Intelligence"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Disc.%"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__discount
msgid "Discount (%)"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__display_name
#: model:ir.model.fields,field_description:account.field_account_account_tag__display_name
#: model:ir.model.fields,field_description:account.field_account_account_template__display_name
#: model:ir.model.fields,field_description:account.field_account_account_type__display_name
#: model:ir.model.fields,field_description:account.field_account_accrual_accounting_wizard__display_name
#: model:ir.model.fields,field_description:account.field_account_bank_statement__display_name
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__display_name
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance__display_name
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__display_name
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__display_name
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__display_name
#: model:ir.model.fields,field_description:account.field_account_chart_template__display_name
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__display_name
#: model:ir.model.fields,field_description:account.field_account_common_report__display_name
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__display_name
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__display_name
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__display_name
#: model:ir.model.fields,field_description:account.field_account_group__display_name
#: model:ir.model.fields,field_description:account.field_account_incoterms__display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_report__display_name
#: model:ir.model.fields,field_description:account.field_account_invoice_send__display_name
#: model:ir.model.fields,field_description:account.field_account_journal__display_name
#: model:ir.model.fields,field_description:account.field_account_journal_group__display_name
#: model:ir.model.fields,field_description:account.field_account_move__display_name
#: model:ir.model.fields,field_description:account.field_account_move_line__display_name
#: model:ir.model.fields,field_description:account.field_account_move_reversal__display_name
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__display_name
#: model:ir.model.fields,field_description:account.field_account_payment__display_name
#: model:ir.model.fields,field_description:account.field_account_payment_method__display_name
#: model:ir.model.fields,field_description:account.field_account_payment_register__display_name
#: model:ir.model.fields,field_description:account.field_account_payment_term__display_name
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__display_name
#: model:ir.model.fields,field_description:account.field_account_print_journal__display_name
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__display_name
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__display_name
#: model:ir.model.fields,field_description:account.field_account_reconciliation_widget__display_name
#: model:ir.model.fields,field_description:account.field_account_root__display_name
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__display_name
#: model:ir.model.fields,field_description:account.field_account_tax__display_name
#: model:ir.model.fields,field_description:account.field_account_tax_group__display_name
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line__display_name
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line_template__display_name
#: model:ir.model.fields,field_description:account.field_account_tax_report_line__display_name
#: model:ir.model.fields,field_description:account.field_account_tax_template__display_name
#: model:ir.model.fields,field_description:account.field_account_unreconcile__display_name
#: model:ir.model.fields,field_description:account.field_cash_box_out__display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_agedpartnerbalance__display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_hash_integrity__display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_invoice_with_payments__display_name
#: model:ir.model.fields,field_description:account.field_report_account_report_journal__display_name
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__display_name
#: model:ir.model.fields,field_description:account.field_validate_account_move__display_name
msgid "Display Name"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__qr_code
#: model:ir.model.fields,field_description:account.field_res_config_settings__qr_code
msgid "Display SEPA QR code"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__display_type
msgid "Display Type"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_template__description
msgid "Display on Invoices"
msgstr ""

#. module: account
#: code:addons/account/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__auto_delete_message
msgid ""
"Do not keep a copy of the email in the document communication history (mass "
"mailing only)"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_company__account_dashboard_onboarding_state__done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_invoice_onboarding_state__done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_onboarding_invoice_layout_state__done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_onboarding_sale_tax_state__done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_onboarding_sample_invoice_state__done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_setup_bank_data_state__done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_setup_coa_state__done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_setup_fy_data_state__done
msgid "Done"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_page
msgid "Download"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_invoice_report__state__draft
#: model:ir.model.fields.selection,name:account.selection__account_move__state__draft
#: model:ir.model.fields.selection,name:account.selection__account_payment__state__draft
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Draft"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Draft Bill"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Draft Credit Note"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Draft Entry"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
#, python-format
msgid "Draft Invoice"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Draft Invoices"
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:0
#, python-format
msgid "Draft Payment"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Draft Purchase Receipt"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Draft Sales Receipt"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Draft Vendor Credit Note"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_bank_statement_draft_tree
msgid "Draft statements"
msgstr ""

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:0
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_form
#, python-format
msgid "Due"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__residual
msgid "Due Amount"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/controllers/portal.py:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: model:ir.model.fields,field_description:account.field_account_invoice_report__invoice_date_due
#: model:ir.model.fields,field_description:account.field_account_move__invoice_date_due
#: model:ir.model.fields,field_description:account.field_account_move_line__date_maturity
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
#, python-format
msgid "Due Date"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_form
msgid "Due Date Computation"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_tree
msgid "Due Type"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_form
msgid "Due the"
msgstr ""

#. module: account
#: model:ir.actions.server,name:account.action_duplicate_account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#: model_terms:ir.ui.view,arch_db:account.view_move_line_tree_grouped
msgid "Duplicate"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"Duplicated vendor reference detected. You probably encoded twice the same vendor bill/credit note:\n"
"%s"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_reports
msgid "Dynamic Reports"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_l10n_eu_service
msgid "EU Digital Goods VAT"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_EXW
msgid "EX WORKS"
msgstr ""

#. module: account
#: code:addons/account/models/chart_template.py:0
#: code:addons/account/models/chart_template.py:0
#: code:addons/account/models/chart_template.py:0
#, python-format
msgid "EXCH"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_line_tree_grouped
msgid "Edit"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Either pass both debit and credit or none."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__is_email
msgid "Email"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Email Alias"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__email_from
msgid ""
"Email address of the sender. This field is set when no matching partner is "
"found and replaces the author_id field in the chatter."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__invoice_is_email
msgid "Email by default"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Email your Invoices/Bills"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__end_bank_stmt_ids
msgid "End Bank Stmt"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__date_to
#: model:ir.model.fields,field_description:account.field_account_common_report__date_to
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__date_to
#: model:ir.model.fields,field_description:account.field_account_print_journal__date_to
msgid "End Date"
msgstr ""

#. module: account
#: model:account.payment.term,name:account.account_payment_term_end_following_month
msgid "End of Following Month"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__balance_end_real
msgid "Ending Balance"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__cashbox_end_id
msgid "Ending Cashbox"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_year__date_to
msgid "Ending Date, included in the fiscal year."
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_move_line_form
msgid "Entries"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_print_journal__sort_selection
msgid "Entries Sorted by"
msgstr ""

#. module: account
#: code:addons/account/models/company.py:0
#, python-format
msgid "Entries are hashed from %s (%s)"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Entries are not from the same account."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Entries to Review"
msgstr ""

#. module: account
#: code:addons/account/models/account_analytic_line.py:0
#, python-format
msgid "Entries: "
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__sequence_id
msgid "Entry Sequence"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__move_line_ids
msgid "Entry lines"
msgstr ""

#. module: account
#. openerp-web
#: model:account.account.type,name:account.data_account_type_equity
#: code:addons/account/static/src/js/account_selection.js:0
#: model:ir.model.fields.selection,name:account.selection__account_account_type__internal_group__equity
#: model_terms:ir.ui.view,arch_db:account.view_account_search
#, python-format
msgid "Equity"
msgstr ""

#. module: account
#: code:addons/account/models/res_config_settings.py:0
#, python-format
msgid "Error!"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_journal__invoice_reference_model__euro
msgid "European"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__decimal_separator
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__decimal_separator
msgid ""
"Every character that is nor a digit nor this separator will be removed from "
"the matching string"
msgstr ""

#. module: account
#: code:addons/account/models/chart_template.py:0
#, python-format
msgid "Exchange Difference"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__currency_exchange_journal_id
#: model:ir.model.fields,field_description:account.field_res_config_settings__currency_exchange_journal_id
msgid "Exchange Gain or Loss Journal"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__exchange_move_id
msgid "Exchange Move"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__exclude_from_invoice_tab
msgid "Exclude From Invoice Tab"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_group__excluded_journal_ids
msgid "Excluded Journals"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__expects_chart_of_accounts
msgid "Expects a Chart of Accounts"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/account_selection.js:0
#: model:ir.model.fields.selection,name:account.selection__account_account_type__internal_group__expense
#: model:ir.model.fields.selection,name:account.selection__account_accrual_accounting_wizard__account_type__expense
#, python-format
msgid "Expense"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_product_category__property_account_expense_categ_id
#: model:ir.model.fields,field_description:account.field_product_product__property_account_expense_id
#: model:ir.model.fields,field_description:account.field_product_template__property_account_expense_id
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Expense Account"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_account_expense_id
msgid "Expense Account on Product Template"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_accrual_accounting_wizard__expense_accrual_account
#: model:ir.model.fields,field_description:account.field_res_company__expense_accrual_account_id
msgid "Expense Accrual Account"
msgstr ""

#. module: account
#: model:account.account.type,name:account.data_account_type_expenses
#: model_terms:ir.ui.view,arch_db:account.view_account_search
msgid "Expenses"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__reference
msgid "External Reference"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "External link"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_FAS
msgid "FREE ALONGSIDE SHIP"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_FCA
msgid "FREE CARRIER"
msgstr ""

#. module: account
#: model:account.incoterms,name:account.incoterm_FOB
msgid "FREE ON BOARD"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line__factor
msgid "Factor Ratio"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_repartition_line__factor
msgid ""
"Factor to apply on the account move lines generated from this repartition "
"line"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_repartition_line__factor_percent
#: model:ir.model.fields,help:account.field_account_tax_repartition_line_template__factor_percent
msgid ""
"Factor to apply on the account move lines generated from this repartition "
"line, in percents"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Favorites"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_company__fiscalyear_last_month__2
msgid "February"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__state_ids
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__state_ids
msgid "Federal States"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Filter on account, label, partner, amount,..."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_analytic_line__general_account_id
#: model_terms:ir.ui.view,arch_db:account.view_account_analytic_line_filter_inherit_account
msgid "Financial Account"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line_template__tag_ids
msgid "Financial Tags"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_hash_integrity
msgid "First Entry"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_hash_integrity
msgid "First Hash"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Fiscal Information"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Fiscal Localization"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__position_id
msgid "Fiscal Mapping"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Fiscal Periods"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_account_fiscal_position_template_form
#: model:ir.model,name:account.model_account_fiscal_position
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__name
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__position_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__position_id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__position_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__fiscal_position_id
#: model:ir.model.fields,field_description:account.field_account_move__fiscal_position_id
#: model:ir.model.fields,field_description:account.field_res_partner__property_account_position_id
#: model:ir.model.fields,field_description:account.field_res_users__property_account_position_id
#: model_terms:ir.ui.view,arch_db:account.view_account_position_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_position_form
#: model_terms:ir.ui.view,arch_db:account.view_account_position_template_search
#: model_terms:ir.ui.view,arch_db:account.view_account_position_template_tree
#: model_terms:ir.ui.view,arch_db:account.view_account_position_tree
msgid "Fiscal Position"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__name
#: model_terms:ir.ui.view,arch_db:account.view_account_position_template_form
#: model_terms:ir.ui.view,arch_db:account.view_account_position_template_search
msgid "Fiscal Position Template"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_account_fiscal_position_form
#: model:ir.ui.menu,name:account.menu_action_account_fiscal_position_form
msgid "Fiscal Positions"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_fiscal_year
msgid "Fiscal Year"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.action_account_fiscal_year_form
msgid "Fiscal Year 2018"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.setup_financial_year_opening_form
msgid "Fiscal Year End"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.actions_account_fiscal_year
#: model:ir.model.fields,field_description:account.field_res_config_settings__group_fiscal_year
#: model_terms:ir.ui.view,arch_db:account.setup_financial_year_opening_form
msgid "Fiscal Years"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move__fiscal_position_id
msgid ""
"Fiscal positions are used to adapt taxes and accounts for particular "
"customers or sales orders/invoices. The default value comes from the "
"customer."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__fiscalyear_last_day
#: model:ir.model.fields,field_description:account.field_res_company__fiscalyear_last_day
msgid "Fiscalyear Last Day"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__fiscalyear_last_month
#: model:ir.model.fields,field_description:account.field_res_company__fiscalyear_last_month
msgid "Fiscalyear Last Month"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__amount_type__fixed
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__second_amount_type__fixed
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__amount_type__fixed
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__second_amount_type__fixed
#: model:ir.model.fields.selection,name:account.selection__account_tax__amount_type__fixed
#: model:ir.model.fields.selection,name:account.selection__account_tax_template__amount_type__fixed
msgid "Fixed"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_payment_term_line__value__fixed
msgid "Fixed Amount"
msgstr ""

#. module: account
#: model:account.account.type,name:account.data_account_type_fixed_assets
msgid "Fixed Assets"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__amount
#: model:ir.model.fields,help:account.field_account_reconcile_model__second_amount
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__amount
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__second_amount
msgid ""
"Fixed amount will count as a debit if it is negative, as a credit if it is "
"positive."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_follower_ids
#: model:ir.model.fields,field_description:account.field_account_journal__message_follower_ids
#: model:ir.model.fields,field_description:account.field_account_move__message_follower_ids
#: model:ir.model.fields,field_description:account.field_account_payment__message_follower_ids
msgid "Followers"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_channel_ids
#: model:ir.model.fields,field_description:account.field_account_journal__message_channel_ids
#: model:ir.model.fields,field_description:account.field_account_move__message_channel_ids
#: model:ir.model.fields,field_description:account.field_account_payment__message_channel_ids
msgid "Followers (Channels)"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_partner_ids
#: model:ir.model.fields,field_description:account.field_account_journal__message_partner_ids
#: model:ir.model.fields,field_description:account.field_account_move__message_partner_ids
#: model:ir.model.fields,field_description:account.field_account_payment__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__inbound_payment_method_ids
msgid "For Incoming Payments"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__outbound_payment_method_ids
msgid "For Outgoing Payments"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term_line__value_amount
msgid "For percent enter a ratio between 0-100."
msgstr ""

#. module: account
#: model:ir.model.constraint,message:account.constraint_account_move_line_check_non_accountable_fields_null
msgid ""
"Forbidden unit price, account and quantity on non-accountable invoice line"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__force_second_tax_included
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__force_second_tax_included
msgid "Force the second tax to be managed as a price included tax."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__force_tax_included
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__force_tax_included
msgid "Force the tax to be managed as a price included tax."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_account__currency_id
msgid "Forces all moves for this account to have this account currency."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template__currency_id
msgid "Forces all moves for this account to have this secondary currency."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__always_set_currency_id
msgid "Foreign Currency"
msgstr ""

#. module: account
#: code:addons/account/report/account_aged_partner_balance.py:0
#: code:addons/account/report/account_journal.py:0
#, python-format
msgid "Form content is missing, this report cannot be printed."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_report_line__formula
msgid "Formula"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_journal__invoice_reference_type__none
msgid "Free"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__email_from
msgid "From"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "From Payable accounts"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "From Receivable accounts"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__amount_type__regex
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__second_amount_type__regex
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__amount_type__regex
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__second_amount_type__regex
msgid "From label"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "From now on, you may want to:"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_invoice_report_all_supp
msgid ""
"From this report, you can have an overview of the amount invoiced from your "
"vendors. The search tool can also be used to personalise your Invoices "
"reports and so, match this analysis to your needs."
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_invoice_report_all
msgid ""
"From this report, you can have an overview of the amount invoiced to your "
"customers. The search tool can also be used to personalise your Invoices "
"reports and so, match this analysis to your needs."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "From: "
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_full_reconcile
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__full_reconcile_id
msgid "Full Reconcile"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_move_reversal__refund_method__cancel
msgid "Full Refund"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_move_reversal__refund_method__modify
msgid "Full refund and new draft invoice"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Future Activities"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__income_currency_exchange_account_id
#: model:ir.model.fields,field_description:account.field_res_company__income_currency_exchange_account_id
msgid "Gain Exchange Rate Account"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_account_moves_ledger_general
#: model:ir.ui.menu,name:account.menu_action_account_moves_ledger_general
msgid "General Ledger"
msgstr ""

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_entries_generate_entries
msgid "Generate Entries"
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid "Generated Documents"
msgstr ""

#. module: account
#: code:addons/account/wizard/account_accrual_accounting.py:0
#, python-format
msgid "Generated Entries"
msgstr ""

#. module: account
#: model:ir.ui.menu,name:account.account_reports_legal_statements_menu
msgid "Generic Statements"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_report_account_report_hash_integrity
msgid "Get hash integrity result as PDF."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Get warnings when invoicing specific customers"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Get your bank statements automatically imported every 4 hours, or in one-"
"click, using Yodlee and Plaid services. Once installed, set “Bank Feeds” to "
"“Bank Synchronization” in bank account settings. Then, click “Configure” on "
"the online account to enter your bank credentials."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line__sequence
msgid ""
"Gives the sequence order when displaying a list of bank statement lines."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term_line__sequence
msgid ""
"Gives the sequence order when displaying a list of payment terms lines."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Go to bank statement(s)"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#: code:addons/account/models/company.py:0
#, python-format
msgid "Go to the configuration panel"
msgstr ""

#. module: account
#: code:addons/account/models/company.py:0
#, python-format
msgid "Go to the journal configuration"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_partner__trust__good
msgid "Good Debtor"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Good Job!"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__group_id
#: model:ir.model.fields,field_description:account.field_account_account_template__group_id
msgid "Group"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_tax_report_line_search
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_search
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
#: model_terms:ir.ui.view,arch_db:account.view_account_template_search
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Group By"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_register__group_payment
msgid "Group Payment"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_tax__amount_type__group
#: model:ir.model.fields.selection,name:account.selection__account_tax_template__amount_type__group
msgid "Group of Taxes"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Group payments into a single batch to ease the reconciliation process"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_cash_rounding__rounding_method__half-up
msgid "HALF-UP"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__has_accounting_entries
msgid "Has Accounting Entries"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__has_invoices
msgid "Has Invoices"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__has_reconciled_entries
msgid "Has Reconciled Entries"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__has_unreconciled_entries
#: model:ir.model.fields,field_description:account.field_res_users__has_unreconciled_entries
msgid "Has Unreconciled Entries"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__qr_code_valid
msgid "Has all required arguments"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_hash_integrity
msgid "Hash Integrity Result -"
msgstr ""

#. module: account
#: model:ir.actions.report,name:account.action_report_account_hash_integrity
msgid "Hash integrity result PDF"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__hide_payment_method
msgid "Hide Payment Method"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__hide_tax_exigibility
msgid "Hide Use Cash Basis Option"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_page
msgid "History"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "How total tax amount is computed in orders and invoices"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__id
#: model:ir.model.fields,field_description:account.field_account_account_tag__id
#: model:ir.model.fields,field_description:account.field_account_account_template__id
#: model:ir.model.fields,field_description:account.field_account_account_type__id
#: model:ir.model.fields,field_description:account.field_account_accrual_accounting_wizard__id
#: model:ir.model.fields,field_description:account.field_account_bank_statement__id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance__id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__id
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__id
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__id
#: model:ir.model.fields,field_description:account.field_account_chart_template__id
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__id
#: model:ir.model.fields,field_description:account.field_account_common_report__id
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__id
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__id
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__id
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__id
#: model:ir.model.fields,field_description:account.field_account_group__id
#: model:ir.model.fields,field_description:account.field_account_incoterms__id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__id
#: model:ir.model.fields,field_description:account.field_account_invoice_send__id
#: model:ir.model.fields,field_description:account.field_account_journal__id
#: model:ir.model.fields,field_description:account.field_account_journal_group__id
#: model:ir.model.fields,field_description:account.field_account_move__id
#: model:ir.model.fields,field_description:account.field_account_move_line__id
#: model:ir.model.fields,field_description:account.field_account_move_reversal__id
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__id
#: model:ir.model.fields,field_description:account.field_account_payment__id
#: model:ir.model.fields,field_description:account.field_account_payment_method__id
#: model:ir.model.fields,field_description:account.field_account_payment_register__id
#: model:ir.model.fields,field_description:account.field_account_payment_term__id
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__id
#: model:ir.model.fields,field_description:account.field_account_print_journal__id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__id
#: model:ir.model.fields,field_description:account.field_account_reconciliation_widget__id
#: model:ir.model.fields,field_description:account.field_account_root__id
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__id
#: model:ir.model.fields,field_description:account.field_account_tax__id
#: model:ir.model.fields,field_description:account.field_account_tax_group__id
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line__id
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line_template__id
#: model:ir.model.fields,field_description:account.field_account_tax_report_line__id
#: model:ir.model.fields,field_description:account.field_account_tax_template__id
#: model:ir.model.fields,field_description:account.field_account_unreconcile__id
#: model:ir.model.fields,field_description:account.field_cash_box_out__id
#: model:ir.model.fields,field_description:account.field_report_account_report_agedpartnerbalance__id
#: model:ir.model.fields,field_description:account.field_report_account_report_hash_integrity__id
#: model:ir.model.fields,field_description:account.field_report_account_report_invoice_with_payments__id
#: model:ir.model.fields,field_description:account.field_report_account_report_journal__id
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__id
#: model:ir.model.fields,field_description:account.field_validate_account_move__id
msgid "ID"
msgstr ""

#. module: account
#: code:addons/account/models/chart_template.py:0
#, python-format
msgid "INV"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__activity_exception_icon
#: model:ir.model.fields,field_description:account.field_account_move__activity_exception_icon
#: model:ir.model.fields,field_description:account.field_account_payment__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__activity_exception_icon
#: model:ir.model.fields,help:account.field_account_move__activity_exception_icon
#: model:ir.model.fields,help:account.field_account_payment__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid "If \"Amount Currency\" is specified, then \"Amount\" must be as well."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__message_needaction
#: model:ir.model.fields,help:account.field_account_bank_statement__message_unread
#: model:ir.model.fields,help:account.field_account_journal__message_needaction
#: model:ir.model.fields,help:account.field_account_journal__message_unread
#: model:ir.model.fields,help:account.field_account_move__message_needaction
#: model:ir.model.fields,help:account.field_account_move__message_unread
#: model:ir.model.fields,help:account.field_account_payment__message_needaction
#: model:ir.model.fields,help:account.field_account_payment__message_unread
msgid "If checked, new messages require your attention."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__message_has_error
#: model:ir.model.fields,help:account.field_account_bank_statement__message_has_sms_error
#: model:ir.model.fields,help:account.field_account_journal__message_has_error
#: model:ir.model.fields,help:account.field_account_journal__message_has_sms_error
#: model:ir.model.fields,help:account.field_account_move__message_has_error
#: model:ir.model.fields,help:account.field_account_move__message_has_sms_error
#: model:ir.model.fields,help:account.field_account_payment__message_has_error
#: model:ir.model.fields,help:account.field_account_payment__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template__nocreate
msgid ""
"If checked, the new chart of accounts will not contain this by default."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_reversal__journal_id
msgid "If empty, uses the journal of the journal entry to be reversed."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__include_base_amount
#: model:ir.model.fields,help:account.field_account_tax_template__include_base_amount
msgid ""
"If set, taxes which are computed after this one will be computed based on "
"the price tax included."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__accounting_date
msgid ""
"If set, the accounting entries created during the bank statement reconciliation process will be created at this date.\n"
"This is useful if the accounting period in which the entries should normally be booked is already closed."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__analytic
#: model:ir.model.fields,help:account.field_account_tax_template__analytic
msgid ""
"If set, the amount computed by this tax will be assigned to the same "
"analytic account as the invoice line (if any)"
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"If tags are defined for a tax report line, only two are allowed on it: a "
"positive and a negative one."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term__active
msgid ""
"If the active field is set to False, it will allow you to hide the payment "
"terms without removing it."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move__to_check
msgid ""
"If this checkbox is ticked, it means that the user was not sure of all the "
"related informations at the time of the creation of the move and that the "
"move needs to be checked again."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move__auto_post
msgid ""
"If this checkbox is ticked, this entry will be automatically posted at its "
"date."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__restrict_mode_hash_table
#: model:ir.model.fields,help:account.field_account_move__restrict_mode_hash_table
msgid ""
"If ticked, the accounting entry or invoice receives a hash as soon as it is "
"posted and cannot be modified anymore."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"If you check this box, you will be able to collect payments using SEPA "
"Direct Debit mandates."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"If you check this box, you will be able to register your payment using SEPA."
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.open_account_journal_dashboard_kanban
msgid ""
"If you have not installed a chart of account, please install one first.<br>"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_unreconcile_view
msgid ""
"If you unreconcile transactions, you must also verify all the actions that "
"are linked to those transactions because they will not be disabled"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"If you want to use \"Off-Balance Sheet\" accounts, all the accounts of the "
"journal entry must be of this type"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"If you're selling digital goods to customers in the EU, you must charge VAT "
"based on your customers' locations. This rule applies regardless of you are "
"located. Digital goods are defined in the legislation as broadcasting, "
"telecommunications, and services that are electronically supplied instead of"
" shipped. Gift cards sent online are not included in the definition."
msgstr ""

#. module: account
#: model:account.payment.term,name:account.account_payment_term_immediate
msgid "Immediate Payment"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_bank_statement_import_qif
msgid "Import .qif files"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_bank_statement_import_csv
msgid "Import in .csv format"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_bank_statement_import_ofx
msgid "Import in .ofx format"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_bank_statement_import_camt
msgid "Import in CAMT.053 format"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements automatically"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements in CAMT.053"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements in CSV"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements in OFX"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Import your bank statements in QIF"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_invoice_report__invoice_payment_state__in_payment
#: model:ir.model.fields.selection,name:account.selection__account_move__invoice_payment_state__in_payment
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "In Payment"
msgstr ""

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid ""
"In order to delete a bank statement line, you must first cancel it to delete"
" related journal items."
msgstr ""

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid ""
"In order to delete a bank statement, you must first cancel it to delete "
"related journal items."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Inactive"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__inalterable_hash
msgid "Inalterability Hash"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_hash_integrity
msgid "Inalterability check"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__secure_sequence_number
msgid "Inalteralbility No Gap Sequence #"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_payment_method__payment_type__inbound
msgid "Inbound"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__analytic
msgid "Include in Analytic Cost"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__price_include
#: model:ir.model.fields,field_description:account.field_account_tax_template__price_include
msgid "Included in Price"
msgstr ""

#. module: account
#. openerp-web
#: model:account.account.type,name:account.data_account_type_revenue
#: code:addons/account/static/src/js/account_selection.js:0
#: model:ir.model.fields.selection,name:account.selection__account_account_type__internal_group__income
#: model_terms:ir.ui.view,arch_db:account.view_account_search
#, python-format
msgid "Income"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_product_category__property_account_income_categ_id
#: model:ir.model.fields,field_description:account.field_product_product__property_account_income_id
#: model:ir.model.fields,field_description:account.field_product_template__property_account_income_id
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Income Account"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_account_income_id
msgid "Income Account on Product Template"
msgstr ""

#. module: account
#: code:addons/account/wizard/setup_wizards.py:0
#, python-format
msgid ""
"Incorrect fiscal year date: day is out of range for month. Month: %s; Day: "
"%s"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__invoice_incoterm_id
msgid "Incoterm"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_incoterms__code
msgid "Incoterm Standard Code"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_incoterms_tree
#: model:ir.model,name:account.model_account_incoterms
#: model:ir.ui.menu,name:account.menu_action_incoterm_open
#: model_terms:ir.ui.view,arch_db:account.account_incoterms_form
#: model_terms:ir.ui.view,arch_db:account.account_incoterms_view_search
#: model_terms:ir.ui.view,arch_db:account.view_incoterms_tree
msgid "Incoterms"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_incoterms__name
msgid ""
"Incoterms are series of sales terms. They are used to divide transaction "
"costs and responsibilities between buyer and seller and reflect state-of-"
"the-art transportation practices."
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_incoterms_tree
msgid ""
"Incoterms are used to divide transaction costs and responsibilities between "
"buyer and seller."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__tax_line_id
msgid "Indicates that this journal item is a tax line"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:0
#, python-format
msgid "Info"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "Information"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__parent_id
msgid "Initial thread message."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_stock_account_input_categ_id
#: model:ir.model.fields,field_description:account.field_res_company__property_stock_account_input_categ_id
msgid "Input Account for Stock Valuation"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Insert your terms & conditions here..."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Install More Packages"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__transfer_account_id
msgid "Inter-Banks Transfer Account"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_company__transfer_account_id
msgid ""
"Intermediary account used when moving money from a liquidity account to "
"another"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__internal_group
#: model:ir.model.fields,field_description:account.field_account_account_type__internal_group
msgid "Internal Group"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__note
msgid "Internal Notes"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_payment__payment_type__transfer
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Internal Transfer"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_account_payments_transfer
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Internal Transfers"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__internal_type
#: model:ir.model.fields,field_description:account.field_account_move_line__account_internal_type
msgid "Internal Type"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_template_form
msgid "Internal notes..."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move__invoice_incoterm_id
#: model:ir.model.fields,help:account.field_res_company__incoterm_id
#: model:ir.model.fields,help:account.field_res_config_settings__incoterm_id
msgid ""
"International Commercial Terms are a series of predefined commercial terms "
"used in international transactions."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_intrastat
msgid "Intrastat"
msgstr ""

#. module: account
#: code:addons/account/models/partner.py:0
#, python-format
msgid "Invalid \"Zip Range\", please configure it properly."
msgstr ""

#. module: account
#: code:addons/account/models/company.py:0
#, python-format
msgid "Invalid fiscal year last day"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#: model:ir.model.fields,field_description:account.field_res_partner__invoice_warn
#: model:ir.model.fields,field_description:account.field_res_users__invoice_warn
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#, python-format
msgid "Invoice"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__name
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
msgid "Invoice #"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#: model:mail.message.subtype,description:account.mt_invoice_created
#: model:mail.message.subtype,name:account.mt_invoice_created
#, python-format
msgid "Invoice Created"
msgstr ""

#. module: account
#: code:addons/account/controllers/portal.py:0
#: model:ir.model.fields,field_description:account.field_account_invoice_report__invoice_date
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_invoice_tree
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#, python-format
msgid "Invoice Date"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__invoice_filter_type_domain
msgid "Invoice Filter Type Domain"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__invoice_has_matching_suspense_amount
msgid "Invoice Has Matching Suspense Amount"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__invoice_has_outstanding
msgid "Invoice Has Outstanding"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_invoice_layout_step
msgid "Invoice Layout"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Invoice Lines"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_payment
msgid "Invoice Online Payment"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__invoice_outstanding_credits_debits_widget
msgid "Invoice Outstanding Credits Debits Widget"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__invoice_partner_display_name
msgid "Invoice Partner Display Name"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__invoice_partner_icon
msgid "Invoice Partner Icon"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__invoice_payments_widget
msgid "Invoice Payments Widget"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__invoice_sent
msgid "Invoice Sent"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__state
msgid "Invoice Status"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line__invoice_tax_id
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line_template__invoice_tax_id
msgid "Invoice Tax"
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"Invoice and credit note repartition should each contain exactly one line for"
" the base."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"Invoice and credit note repartition should have the same number of lines."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"Invoice and credit note repartitions should match (same percentages, in the "
"same order)."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__invoice_line_ids
msgid "Invoice lines"
msgstr ""

#. module: account
#: model:mail.message.subtype,description:account.mt_invoice_paid
msgid "Invoice paid"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid "Invoice send & Print"
msgstr ""

#. module: account
#: model:mail.message.subtype,description:account.mt_invoice_validated
msgid "Invoice validated"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__invoice_date
msgid "Invoice/Bill Date"
msgstr ""

#. module: account
#: model:mail.template,report_name:account.email_template_edi_invoice
msgid ""
"Invoice_${(object.name or '').replace('/','_')}${object.state == 'draft' and"
" '_draft' or ''}"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Invoiced"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_move_out_invoice_type
#: model:ir.actions.report,name:account.account_invoices
#: model:ir.model.fields,field_description:account.field_account_invoice_send__invoice_ids
#: model:ir.model.fields,field_description:account.field_account_payment__invoice_ids
#: model:ir.model.fields,field_description:account.field_account_payment_register__invoice_ids
#: model:ir.model.fields,field_description:account.field_res_partner__invoice_ids
#: model:ir.model.fields,field_description:account.field_res_users__invoice_ids
#: model:ir.ui.menu,name:account.menu_action_account_invoice_report_all
#: model:ir.ui.menu,name:account.menu_action_move_out_invoice_type
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_graph
#: model_terms:ir.ui.view,arch_db:account.view_invoice_tree
msgid "Invoices"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_my_home_invoice
#: model_terms:ir.ui.view,arch_db:account.portal_my_home_menu_invoice
msgid "Invoices &amp; Bills"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_account_invoice_report_all
#: model:ir.actions.act_window,name:account.action_account_invoice_report_all_supp
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_graph
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_pivot
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Invoices Analysis"
msgstr ""

#. module: account
#: code:addons/account/models/chart_template.py:0
#, python-format
msgid "Invoices Matching Rule"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_invoice_report
msgid "Invoices Statistics"
msgstr ""

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:0
#, python-format
msgid "Invoices owed to you"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Invoices to Validate"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_payment__reconciled_invoice_ids
msgid "Invoices whose journal items have been reconciled with these payments."
msgstr ""

#. module: account
#: model:ir.actions.report,name:account.account_invoices_without_payment
msgid "Invoices without Payment"
msgstr ""

#. module: account
#: model:ir.ui.menu,name:account.account_invoicing_menu
#: model:ir.ui.menu,name:account.menu_finance
#: model_terms:ir.ui.view,arch_db:account.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account.view_partner_property_form
msgid "Invoicing"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__match_amount__between
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__match_amount__between
msgid "Is Between"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_is_follower
#: model:ir.model.fields,field_description:account.field_account_journal__message_is_follower
#: model:ir.model.fields,field_description:account.field_account_move__message_is_follower
#: model:ir.model.fields,field_description:account.field_account_payment__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__match_amount__greater
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__match_amount__greater
msgid "Is Greater Than"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__match_amount__lower
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__match_amount__lower
msgid "Is Lower Than"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__printed
msgid "Is Printed"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__is_rounding_line
msgid "Is Rounding Line"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__is_difference_zero
msgid "Is zero"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__default_credit_account_id
#: model:ir.model.fields,help:account.field_res_company__income_currency_exchange_account_id
msgid "It acts as a default account for credit amount"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__default_debit_account_id
#: model:ir.model.fields,help:account.field_res_company__expense_currency_exchange_account_id
msgid "It acts as a default account for debit amount"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__alias_name
msgid "It creates draft invoices and bills by sending an email."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move__invoice_sent
msgid "It indicates that the invoice has been sent."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"It is mandatory to specify an account and a journal to create a write-off."
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:0
#, python-format
msgid ""
"It is not allowed to delete a payment that already created a journal entry "
"since it would create a gap in the numbering. You should create the journal "
"entry again and cancel it thanks to a regular revert."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"It seems that the taxes have been modified since the creation of the journal"
" entry. You should create the credit note manually instead."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Items"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_company__fiscalyear_last_month__1
msgid "January"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: model:ir.model,name:account.model_account_journal
#: model:ir.model.fields,field_description:account.field_account_bank_statement__journal_id
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__journal_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__journal_id
#: model:ir.model.fields,field_description:account.field_account_move__journal_id
#: model:ir.model.fields,field_description:account.field_account_move_line__journal_id
#: model:ir.model.fields,field_description:account.field_account_payment__journal_id
#: model:ir.model.fields,field_description:account.field_account_payment_register__journal_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__journal_id
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__linked_journal_id
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__journal_id
#: model_terms:ir.ui.view,arch_db:account.report_hash_integrity
#: model_terms:ir.ui.view,arch_db:account.report_journal
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_search
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_search
#, python-format
msgid "Journal"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/models/account_bank_statement.py:0
#: code:addons/account/static/src/js/mail_activity.js:0
#: model:ir.actions.act_window,name:account.action_move_journal_line
#: model:ir.model,name:account.model_account_move
#: model:ir.ui.menu,name:account.menu_action_move_journal_line_form
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_form
#: model_terms:ir.ui.view,arch_db:account.view_move_tree
#, python-format
msgid "Journal Entries"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Journal Entries by Date"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/mail_activity.js:0
#: model:ir.model.fields,field_description:account.field_account_move_line__move_id
#: model:ir.model.fields,field_description:account.field_account_move_reversal__move_id
#: model:ir.model.fields.selection,name:account.selection__account_move__type__entry
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
#, python-format
msgid "Journal Entry"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__move_name
#: model:ir.model.fields,field_description:account.field_account_payment__move_name
msgid "Journal Entry Name"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_print_journal__sort_selection__move_name
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "Journal Entry Number"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal_group__name
msgid "Journal Group"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_account_journal_group_list
#: model:ir.model.fields,field_description:account.field_account_journal__journal_group_ids
#: model:ir.ui.menu,name:account.menu_action_account_journal_group_list
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_group_form
msgid "Journal Groups"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_move_line
#: model:ir.model.fields,field_description:account.field_account_analytic_line__move_id
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "Journal Item"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__writeoff_label
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__label
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__label
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Journal Item Label"
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:0
#: code:addons/account/models/reconciliation_widget.py:0
#: model:ir.actions.act_window,name:account.action_account_moves_all
#: model:ir.actions.act_window,name:account.action_account_moves_all_a
#: model:ir.actions.act_window,name:account.action_account_moves_all_tree
#: model:ir.actions.act_window,name:account.action_move_line_graph
#: model:ir.actions.act_window,name:account.action_move_line_graph_posted
#: model:ir.actions.act_window,name:account.action_move_line_select
#: model:ir.actions.act_window,name:account.action_move_line_select_by_partner
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__journal_entry_ids
#: model:ir.model.fields,field_description:account.field_account_move__line_ids
#: model:ir.model.fields,field_description:account.field_res_partner__journal_item_count
#: model:ir.model.fields,field_description:account.field_res_users__journal_item_count
#: model:ir.ui.menu,name:account.menu_action_account_moves_all
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.view_account_form
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_form
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#: model_terms:ir.ui.view,arch_db:account.view_move_line_pivot
#: model_terms:ir.ui.view,arch_db:account.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:account.view_move_line_tree_grouped
#, python-format
msgid "Journal Items"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_move_line_select_tax_audit
msgid "Journal Items for Tax Audit"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_action.js:0
#: model:ir.actions.client,name:account.action_manual_reconcile
#, python-format
msgid "Journal Items to Reconcile"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__name
msgid "Journal Name"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_journal_group_list
msgid "Journal group are used in reporting to display relevant data together."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Journal items where matching number isn't set"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_accrual_accounting_wizard__journal_id
#: model:ir.model.fields,help:account.field_res_company__accrual_default_journal_id
msgid "Journal used by default for moving the period of an entry"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_company__account_opening_journal_id
msgid ""
"Journal where the opening entry of this company's accounting has been "
"posted."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__journal_currency_id
msgid "Journal's Currency"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_account_journal_form
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__journal_ids
#: model:ir.model.fields,field_description:account.field_account_common_report__journal_ids
#: model:ir.model.fields,field_description:account.field_account_print_journal__journal_ids
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_journal_ids
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_journal_ids
#: model:ir.ui.menu,name:account.menu_action_account_journal_form
#: model:ir.ui.menu,name:account.menu_finance_entries_accounting_journals
msgid "Journals"
msgstr ""

#. module: account
#: model:ir.actions.report,name:account.action_report_journal
msgid "Journals Audit"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__json_activity_data
msgid "Json Activity Data"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_company__fiscalyear_last_month__7
msgid "July"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_company__fiscalyear_last_month__6
msgid "June"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_company__account_dashboard_onboarding_state__just_done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_invoice_onboarding_state__just_done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_onboarding_invoice_layout_state__just_done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_onboarding_sale_tax_state__just_done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_onboarding_sample_invoice_state__just_done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_setup_bank_data_state__just_done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_setup_coa_state__just_done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_setup_fy_data_state__just_done
msgid "Just done"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__reason
msgid "Justification"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__kanban_dashboard
msgid "Kanban Dashboard"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__kanban_dashboard_graph
msgid "Kanban Dashboard Graph"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Keep empty for no control"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_payment__payment_difference_handling__open
msgid "Keep open"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_product_product__property_account_income_id
#: model:ir.model.fields,help:account.field_product_template__property_account_income_id
msgid ""
"Keep this field empty to use the default value from the product category."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_product_product__property_account_expense_id
#: model:ir.model.fields,help:account.field_product_template__property_account_expense_id
msgid ""
"Keep this field empty to use the default value from the product category. If"
" anglo-saxon accounting with automated valuation method is configured, the "
"expense account on the product category will be used."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_digest_digest__kpi_account_total_revenue_value
msgid "Kpi Account Total Revenue Value"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__name
#: model:ir.model.fields,field_description:account.field_account_move_line__name
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_label
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_label
#: model_terms:ir.ui.view,arch_db:account.report_journal
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_form
#, python-format
msgid "Label"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_label_param
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_label_param
msgid "Label Parameter"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__description
msgid "Label on Invoices"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_hash_integrity
msgid "Last Entry"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_hash_integrity
msgid "Last Hash"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account____last_update
#: model:ir.model.fields,field_description:account.field_account_account_tag____last_update
#: model:ir.model.fields,field_description:account.field_account_account_template____last_update
#: model:ir.model.fields,field_description:account.field_account_account_type____last_update
#: model:ir.model.fields,field_description:account.field_account_accrual_accounting_wizard____last_update
#: model:ir.model.fields,field_description:account.field_account_bank_statement____last_update
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox____last_update
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance____last_update
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line____last_update
#: model:ir.model.fields,field_description:account.field_account_cash_rounding____last_update
#: model:ir.model.fields,field_description:account.field_account_cashbox_line____last_update
#: model:ir.model.fields,field_description:account.field_account_chart_template____last_update
#: model:ir.model.fields,field_description:account.field_account_common_journal_report____last_update
#: model:ir.model.fields,field_description:account.field_account_common_report____last_update
#: model:ir.model.fields,field_description:account.field_account_financial_year_op____last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position____last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account____last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template____last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax____last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template____last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template____last_update
#: model:ir.model.fields,field_description:account.field_account_fiscal_year____last_update
#: model:ir.model.fields,field_description:account.field_account_full_reconcile____last_update
#: model:ir.model.fields,field_description:account.field_account_group____last_update
#: model:ir.model.fields,field_description:account.field_account_incoterms____last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_report____last_update
#: model:ir.model.fields,field_description:account.field_account_invoice_send____last_update
#: model:ir.model.fields,field_description:account.field_account_journal____last_update
#: model:ir.model.fields,field_description:account.field_account_journal_group____last_update
#: model:ir.model.fields,field_description:account.field_account_move____last_update
#: model:ir.model.fields,field_description:account.field_account_move_line____last_update
#: model:ir.model.fields,field_description:account.field_account_move_reversal____last_update
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile____last_update
#: model:ir.model.fields,field_description:account.field_account_payment____last_update
#: model:ir.model.fields,field_description:account.field_account_payment_method____last_update
#: model:ir.model.fields,field_description:account.field_account_payment_register____last_update
#: model:ir.model.fields,field_description:account.field_account_payment_term____last_update
#: model:ir.model.fields,field_description:account.field_account_payment_term_line____last_update
#: model:ir.model.fields,field_description:account.field_account_print_journal____last_update
#: model:ir.model.fields,field_description:account.field_account_reconcile_model____last_update
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template____last_update
#: model:ir.model.fields,field_description:account.field_account_reconciliation_widget____last_update
#: model:ir.model.fields,field_description:account.field_account_root____last_update
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config____last_update
#: model:ir.model.fields,field_description:account.field_account_tax____last_update
#: model:ir.model.fields,field_description:account.field_account_tax_group____last_update
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line____last_update
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line_template____last_update
#: model:ir.model.fields,field_description:account.field_account_tax_report_line____last_update
#: model:ir.model.fields,field_description:account.field_account_tax_template____last_update
#: model:ir.model.fields,field_description:account.field_account_unreconcile____last_update
#: model:ir.model.fields,field_description:account.field_cash_box_out____last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_agedpartnerbalance____last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_hash_integrity____last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_invoice_with_payments____last_update
#: model:ir.model.fields,field_description:account.field_report_account_report_journal____last_update
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard____last_update
#: model:ir.model.fields,field_description:account.field_validate_account_move____last_update
msgid "Last Modified on"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Last Reconciliation:"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__write_uid
#: model:ir.model.fields,field_description:account.field_account_account_tag__write_uid
#: model:ir.model.fields,field_description:account.field_account_account_template__write_uid
#: model:ir.model.fields,field_description:account.field_account_account_type__write_uid
#: model:ir.model.fields,field_description:account.field_account_accrual_accounting_wizard__write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement__write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance__write_uid
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__write_uid
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__write_uid
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__write_uid
#: model:ir.model.fields,field_description:account.field_account_chart_template__write_uid
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__write_uid
#: model:ir.model.fields,field_description:account.field_account_common_report__write_uid
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__write_uid
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__write_uid
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__write_uid
#: model:ir.model.fields,field_description:account.field_account_group__write_uid
#: model:ir.model.fields,field_description:account.field_account_incoterms__write_uid
#: model:ir.model.fields,field_description:account.field_account_invoice_send__write_uid
#: model:ir.model.fields,field_description:account.field_account_journal__write_uid
#: model:ir.model.fields,field_description:account.field_account_journal_group__write_uid
#: model:ir.model.fields,field_description:account.field_account_move__write_uid
#: model:ir.model.fields,field_description:account.field_account_move_line__write_uid
#: model:ir.model.fields,field_description:account.field_account_move_reversal__write_uid
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__write_uid
#: model:ir.model.fields,field_description:account.field_account_payment__write_uid
#: model:ir.model.fields,field_description:account.field_account_payment_method__write_uid
#: model:ir.model.fields,field_description:account.field_account_payment_register__write_uid
#: model:ir.model.fields,field_description:account.field_account_payment_term__write_uid
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__write_uid
#: model:ir.model.fields,field_description:account.field_account_print_journal__write_uid
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__write_uid
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__write_uid
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__write_uid
#: model:ir.model.fields,field_description:account.field_account_tax__write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_group__write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line__write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line_template__write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_report_line__write_uid
#: model:ir.model.fields,field_description:account.field_account_tax_template__write_uid
#: model:ir.model.fields,field_description:account.field_account_unreconcile__write_uid
#: model:ir.model.fields,field_description:account.field_cash_box_out__write_uid
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__write_uid
#: model:ir.model.fields,field_description:account.field_validate_account_move__write_uid
msgid "Last Updated by"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__write_date
#: model:ir.model.fields,field_description:account.field_account_account_tag__write_date
#: model:ir.model.fields,field_description:account.field_account_account_template__write_date
#: model:ir.model.fields,field_description:account.field_account_account_type__write_date
#: model:ir.model.fields,field_description:account.field_account_accrual_accounting_wizard__write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement__write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_closebalance__write_date
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__write_date
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__write_date
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__write_date
#: model:ir.model.fields,field_description:account.field_account_chart_template__write_date
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__write_date
#: model:ir.model.fields,field_description:account.field_account_common_report__write_date
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account__write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_account_template__write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__write_date
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__write_date
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__write_date
#: model:ir.model.fields,field_description:account.field_account_group__write_date
#: model:ir.model.fields,field_description:account.field_account_incoterms__write_date
#: model:ir.model.fields,field_description:account.field_account_invoice_send__write_date
#: model:ir.model.fields,field_description:account.field_account_journal__write_date
#: model:ir.model.fields,field_description:account.field_account_journal_group__write_date
#: model:ir.model.fields,field_description:account.field_account_move__write_date
#: model:ir.model.fields,field_description:account.field_account_move_line__write_date
#: model:ir.model.fields,field_description:account.field_account_move_reversal__write_date
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__write_date
#: model:ir.model.fields,field_description:account.field_account_payment__write_date
#: model:ir.model.fields,field_description:account.field_account_payment_method__write_date
#: model:ir.model.fields,field_description:account.field_account_payment_register__write_date
#: model:ir.model.fields,field_description:account.field_account_payment_term__write_date
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__write_date
#: model:ir.model.fields,field_description:account.field_account_print_journal__write_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__write_date
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__write_date
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__write_date
#: model:ir.model.fields,field_description:account.field_account_tax__write_date
#: model:ir.model.fields,field_description:account.field_account_tax_group__write_date
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line__write_date
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line_template__write_date
#: model:ir.model.fields,field_description:account.field_account_tax_report_line__write_date
#: model:ir.model.fields,field_description:account.field_account_tax_template__write_date
#: model:ir.model.fields,field_description:account.field_account_unreconcile__write_date
#: model:ir.model.fields,field_description:account.field_cash_box_out__write_date
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__write_date
#: model:ir.model.fields,field_description:account.field_validate_account_move__write_date
msgid "Last Updated on"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__last_time_entries_checked
#: model:ir.model.fields,help:account.field_res_users__last_time_entries_checked
msgid ""
"Last time the invoices & payments matching was performed for this partner. "
"It is set either if there's not at least an unreconciled debit and an "
"unreconciled credit or if you click the \"Done\" button."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Late Activities"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__last_time_entries_checked
#: model:ir.model.fields,field_description:account.field_res_users__last_time_entries_checked
msgid "Latest Invoices & Payments Matching Date"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__layout
msgid "Layout"
msgstr ""

#. module: account
#: model:ir.ui.menu,name:account.menu_finance_entries_accounting_ledgers
msgid "Ledgers"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_position_form
msgid "Legal Notes..."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_position__note
msgid "Legal mentions that have to be printed on the invoices."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Less Payment"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Let your customers pay their invoices online"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/account_selection.js:0
#, python-format
msgid "Liabilities"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_account_type__internal_group__liability
#: model_terms:ir.ui.view,arch_db:account.view_account_search
msgid "Liability"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__nbr_lines
msgid "Line Count"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__show_line_subtotals_tax_selection
msgid "Line Subtotals Tax Display"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Line subtotals tax display"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Lines from \"Off-Balance Sheet\" accounts cannot be reconciled"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_report_line__children_line_ids
msgid "Lines that should be rendered as children of this one"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_account_type__type__liquidity
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Liquidity"
msgstr ""

#. module: account
#: code:addons/account/models/chart_template.py:0
#, python-format
msgid "Liquidity Transfer"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template__tax_template_ids
msgid "List of all the taxes that have to be installed by the wizard"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Load more"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Load more... ("
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__fiscalyear_lock_date
msgid "Lock Date"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__period_lock_date
msgid "Lock Date for Non-Advisers"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__restrict_mode_hash_table
#: model:ir.model.fields,field_description:account.field_account_move__restrict_mode_hash_table
msgid "Lock Posted Entries with Hash"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__is_log
msgid "Log an Internal Note"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_invoice_layout_step
msgid "Looks great!"
msgstr ""

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid "Loss"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__loss_account_id
msgid "Loss Account"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__expense_currency_exchange_account_id
#: model:ir.model.fields,field_description:account.field_res_company__expense_currency_exchange_account_id
msgid "Loss Exchange Rate Account"
msgstr ""

#. module: account
#: code:addons/account/models/chart_template.py:0
#, python-format
msgid "MISC"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__mail_activity_type_id
msgid "Mail Activity Type"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__mailing_list_ids
msgid "Mailing List"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_main_attachment_id
#: model:ir.model.fields,field_description:account.field_account_journal__message_main_attachment_id
#: model:ir.model.fields,field_description:account.field_account_move__message_main_attachment_id
#: model:ir.model.fields,field_description:account.field_account_payment__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings__currency_id
msgid "Main currency of the company."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Main currency of your company"
msgstr ""

#. module: account
#: model:ir.ui.menu,name:account.account_management_menu
#: model:ir.ui.menu,name:account.account_reports_management_menu
#: model:ir.ui.menu,name:account.menu_finance_entries_management
msgid "Management"
msgstr ""

#. module: account
#: model:account.payment.method,name:account.account_payment_method_manual_in
#: model:account.payment.method,name:account.account_payment_method_manual_out
msgid "Manual"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Manual Operations"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__inbound_payment_method_ids
msgid ""
"Manual: Get paid by cash, check or any other method outside of Odoo.\n"
"Electronic: Get paid automatically through a payment acquirer by requesting a transaction on a card saved by the customer when buying or subscribing online (payment token).\n"
"Batch Deposit: Encase several customer checks at once by generating a batch deposit to submit to your bank. When encoding the bank statement in Odoo,you are suggested to reconcile the transaction with the batch deposit. Enable this option from the settings."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_payment__payment_method_id
#: model:ir.model.fields,help:account.field_account_payment_register__payment_method_id
msgid ""
"Manual: Get paid by cash, check or any other method outside of Odoo.\n"
"Electronic: Get paid automatically through a payment acquirer by requesting a transaction on a card saved by the customer when buying or subscribing online (payment token).\n"
"Check: Pay bill by check and print it from Odoo.\n"
"Batch Deposit: Encase several customer checks at once by generating a batch deposit to submit to your bank. When encoding the bank statement in Odoo, you are suggested to reconcile the transaction with the batch deposit.To enable batch deposit, module account_batch_payment must be installed.\n"
"SEPA Credit Transfer: Pay bill from a SEPA Credit Transfer file you submit to your bank. To enable sepa credit transfer, module account_sepa must be installed "
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__outbound_payment_method_ids
msgid ""
"Manual:Pay bill by cash or any other method outside of Odoo.\n"
"Check:Pay bill by check and print it from Odoo.\n"
"SEPA Credit Transfer: Pay bill from a SEPA Credit Transfer file you submit to your bank. Enable this option from the settings."
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__rule_type__writeoff_button
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__rule_type__writeoff_button
msgid "Manually create a write-off on clicked button."
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_company__fiscalyear_last_month__3
msgid "March"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Margin Analysis"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_payment__payment_difference_handling__reconcile
msgid "Mark invoice as fully paid"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__mass_mailing_id
msgid "Mass Mailing"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__campaign_id
msgid "Mass Mailing Campaign"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__mass_mailing_name
msgid "Mass Mailing Name"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__match_label__match_regex
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__match_note__match_regex
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__match_transaction_type__match_regex
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__match_label__match_regex
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__match_note__match_regex
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__match_transaction_type__match_regex
msgid "Match Regex"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__rule_type__invoice_matching
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__rule_type__invoice_matching
msgid "Match existing invoices/bills."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Match with entries that are not from receivable/payable accounts"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__matched_credit_ids
msgid "Matched Credits"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__matched_debit_ids
msgid "Matched Debits"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__reconciled_line_ids
#: model_terms:ir.ui.view,arch_db:account.view_full_reconcile_form
msgid "Matched Journal Items"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_full_reconcile_form
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
#: model_terms:ir.ui.view,arch_db:account.view_move_line_tree_grouped
msgid "Matching"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__full_reconcile_id
msgid "Matching #"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_partial_reconcile__max_date
msgid "Max Date of Matched Lines"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_company__fiscalyear_last_month__5
msgid "May"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__communication
msgid "Memo"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:0
#, python-format
msgid "Memo:"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_has_error
#: model:ir.model.fields,field_description:account.field_account_journal__message_has_error
#: model:ir.model.fields,field_description:account.field_account_move__message_has_error
#: model:ir.model.fields,field_description:account.field_account_payment__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__record_name
msgid "Message Record Name"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__invoice_warn_msg
#: model:ir.model.fields,field_description:account.field_res_users__invoice_warn_msg
msgid "Message for Invoice"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__message_type
msgid ""
"Message type: email for email message, notification for system message, "
"comment for other messages such as user replies"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_ids
#: model:ir.model.fields,field_description:account.field_account_journal__message_ids
#: model:ir.model.fields,field_description:account.field_account_move__message_ids
#: model:ir.model.fields,field_description:account.field_account_payment__message_ids
msgid "Messages"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line_template__minus_report_line_ids
msgid "Minus Report Lines"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_account_moves_journal_misc
#: model:ir.model.fields.selection,name:account.selection__account_journal__type__general
#: model:ir.ui.menu,name:account.menu_action_account_moves_journal_misc
#: model:ir.ui.menu,name:account.menu_finance_entries_accounting_miscellaneous
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_search
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Miscellaneous"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Miscellaneous Matching"
msgstr ""

#. module: account
#: code:addons/account/models/chart_template.py:0
#, python-format
msgid "Miscellaneous Operations"
msgstr ""

#. module: account
#: model:ir.model.constraint,message:account.constraint_account_move_line_check_accountable_required_fields
msgid "Missing required account on accountable invoice line."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Modify models"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_cash_rounding__strategy__biggest_tax
msgid "Modify tax amount"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Monitor your product margins from invoices"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__move_id
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "Move"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__move_line_ids
msgid "Move Line"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__move_line_count
msgid "Move Line Count"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__move_reconciled
msgid "Move Reconciled"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_reversal__move_type
msgid "Move Type"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Multi-Currencies"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "My Invoices"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__name
#: model:ir.model.fields,field_description:account.field_account_account_template__name
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__name
#: model:ir.model.fields,field_description:account.field_account_chart_template__name
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__name
#: model:ir.model.fields,field_description:account.field_account_group__name
#: model:ir.model.fields,field_description:account.field_account_incoterms__name
#: model:ir.model.fields,field_description:account.field_account_payment__name
#: model:ir.model.fields,field_description:account.field_account_payment_method__name
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__name
#: model:ir.model.fields,field_description:account.field_account_root__name
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__bank_name
#: model:ir.model.fields,field_description:account.field_account_tax_group__name
#: model:ir.model.fields,field_description:account.field_account_tax_report_line__name
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "Name"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__record_name
msgid "Name get of the related document."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Navigate easily through reports and see what is behind the numbers"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag__tax_negate
msgid "Negate Tax Balance"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: model:ir.model.fields.selection,name:account.selection__account_bank_statement__state__open
#, python-format
msgid "New"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__new_journal_name
msgid "New Journal Name"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "New Transactions"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__activity_date_deadline
#: model:ir.model.fields,field_description:account.field_account_move__activity_date_deadline
#: model:ir.model.fields,field_description:account.field_account_payment__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__activity_summary
#: model:ir.model.fields,field_description:account.field_account_move__activity_summary
#: model:ir.model.fields,field_description:account.field_account_payment__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__activity_type_id
#: model:ir.model.fields,field_description:account.field_account_move__activity_type_id
#: model:ir.model.fields,field_description:account.field_account_payment__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__sequence_number_next
#: model:ir.model.fields,field_description:account.field_account_move__invoice_sequence_number_next
msgid "Next Number"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__invoice_sequence_number_next_prefix
msgid "Next Number Prefix"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__blocked
msgid "No Follow-up"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_partner__invoice_warn__no-message
msgid "No Message"
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid "No attachment was provided"
msgstr ""

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid "No default debit and credit account defined on journal %s (ids: %s)."
msgstr ""

#. module: account
#: code:addons/account/models/account_reconcile_model.py:0
#, python-format
msgid "No move from this reconciliation model"
msgstr ""

#. module: account
#: code:addons/account/models/ir_actions_report.py:0
#, python-format
msgid ""
"No original vendor bills could be found for any of the selected vendor "
"bills."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__no_auto_thread
msgid "No threading for answers"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_company__tax_lock_date
msgid ""
"No users can edit journal entries related to a tax prior and inclusive of "
"this date."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_company__fiscalyear_lock_date
msgid ""
"No users, including Advisers, can edit accounts prior to and inclusive of "
"this date. Use it for fiscal year locking for example."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template__code_digits
msgid "No. of Digits to use for account code"
msgstr ""

#. module: account
#: model:account.account.type,name:account.data_account_type_non_current_assets
msgid "Non-current Assets"
msgstr ""

#. module: account
#: model:account.account.type,name:account.data_account_type_non_current_liabilities
msgid "Non-current Liabilities"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_tax__type_tax_use__none
#: model:ir.model.fields.selection,name:account.selection__account_tax_template__type_tax_use__none
msgid "None"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_partner__trust__normal
msgid "Normal Debtor"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__match_label__not_contains
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__match_note__not_contains
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__match_transaction_type__not_contains
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__match_label__not_contains
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__match_note__not_contains
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__match_transaction_type__not_contains
msgid "Not Contains"
msgstr ""

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:0
#, python-format
msgid "Not Due"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_invoice_report__invoice_payment_state__not_paid
#: model:ir.model.fields.selection,name:account.selection__account_move__invoice_payment_state__not_paid
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Not Paid"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_company__account_dashboard_onboarding_state__not_done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_invoice_onboarding_state__not_done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_onboarding_invoice_layout_state__not_done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_onboarding_sale_tax_state__not_done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_onboarding_sample_invoice_state__not_done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_setup_bank_data_state__not_done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_setup_coa_state__not_done
#: model:ir.model.fields.selection,name:account.selection__res_company__account_setup_fy_data_state__not_done
msgid "Not done"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: model:ir.model.fields,field_description:account.field_account_account_template__note
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_note
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_note
#: model:ir.model.fields.selection,name:account.selection__account_move_line__display_type__line_note
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#, python-format
msgid "Note"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_note_param
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_note_param
msgid "Note Parameter"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_move_out_refund_type
msgid ""
"Note that the easiest way to create a credit note is to do it directly\n"
"                from the customer invoice."
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_move_in_refund_type
msgid ""
"Note that the easiest way to create a vendor credit note it to do it "
"directly from the vendor bill."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__note
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__note
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__note
#: model_terms:ir.ui.view,arch_db:account.view_account_template_form
msgid "Notes"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Nothing to do!"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__notify
msgid "Notify followers"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__notify
msgid "Notify followers of the document (mass post only)"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_company__fiscalyear_last_month__11
msgid "November"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__num_journals_without_account
msgid "Num Journals Without Account"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__name
#: model:ir.model.fields,field_description:account.field_account_move__name
#: model:ir.model.fields,field_description:account.field_account_move_line__move_name
msgid "Number"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_needaction_counter
#: model:ir.model.fields,field_description:account.field_account_journal__message_needaction_counter
#: model:ir.model.fields,field_description:account.field_account_move__message_needaction_counter
#: model:ir.model.fields,field_description:account.field_account_payment__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__days
msgid "Number of Days"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__number_entries
msgid "Number of entries related to this model"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_has_error_counter
#: model:ir.model.fields,field_description:account.field_account_journal__message_has_error_counter
#: model:ir.model.fields,field_description:account.field_account_move__message_has_error_counter
#: model:ir.model.fields,field_description:account.field_account_payment__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__message_needaction_counter
#: model:ir.model.fields,help:account.field_account_journal__message_needaction_counter
#: model:ir.model.fields,help:account.field_account_move__message_needaction_counter
#: model:ir.model.fields,help:account.field_account_payment__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__message_has_error_counter
#: model:ir.model.fields,help:account.field_account_journal__message_has_error_counter
#: model:ir.model.fields,help:account.field_account_move__message_has_error_counter
#: model:ir.model.fields,help:account.field_account_payment__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__message_unread_counter
#: model:ir.model.fields,help:account.field_account_journal__message_unread_counter
#: model:ir.model.fields,help:account.field_account_move__message_unread_counter
#: model:ir.model.fields,help:account.field_account_payment__message_unread_counter
msgid "Number of unread messages"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "OFX Import"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_company__fiscalyear_last_month__10
msgid "October"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_journal__invoice_reference_model__odoo
msgid "Odoo"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_bank_statement_line
msgid ""
"Odoo allows you to reconcile a statement line directly with\n"
"                the related sale or puchase invoice(s)."
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_bank_statement_tree
msgid ""
"Odoo allows you to reconcile a statement line directly with\n"
"                the related sale or puchase invoices."
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.res_partner_action_customer
msgid "Odoo helps you easily track all activities related to a customer."
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.res_partner_action_supplier
msgid "Odoo helps you easily track all activities related to a supplier."
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_account_type__internal_group__off_balance
msgid "Off Balance"
msgstr ""

#. module: account
#: model:account.account.type,name:account.data_account_off_sheet
msgid "Off-Balance Sheet"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_form
msgid "On the"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Once installed, set 'Bank Feeds' to 'File Import' in bank account "
"settings.This adds a button to import from the Accounting dashboard."
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:0
#, python-format
msgid "Only a draft payment can be posted."
msgstr ""

#. module: account
#: code:addons/account/models/chart_template.py:0
#, python-format
msgid "Only administrators can load a chart of accounts"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Only invoices could be printed."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_register__group_payment
msgid "Only one payment will be created by partner (bank)/ currency."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_company__period_lock_date
msgid ""
"Only users with the 'Adviser' role can edit accounts prior to and inclusive "
"of this date. Use it for period locking inside an open fiscal year, for "
"example."
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_invoice_report__state__posted
#: model_terms:ir.ui.view,arch_db:account.init_accounts_tree
msgid "Open"
msgstr ""

#. module: account
#: code:addons/account/models/account_reconcile_model.py:0
#, python-format
msgid "Open Balance"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_model.js:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Open balance"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_financial_year_op
msgid "Opening Balance of Financial Year"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__opening_date
#: model:ir.model.fields,field_description:account.field_res_company__account_opening_date
msgid "Opening Date"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_opening_journal_id
msgid "Opening Journal"
msgstr ""

#. module: account
#: code:addons/account/models/company.py:0
#: model:ir.model.fields,field_description:account.field_res_company__account_opening_move_id
#, python-format
msgid "Opening Journal Entry"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_financial_year_op__opening_move_posted
msgid "Opening Move Posted"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_cashbox_line__number
msgid "Opening Unit Numbers"
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#: code:addons/account/models/account.py:0
#, python-format
msgid "Opening balance"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__opening_credit
msgid "Opening credit"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_account__opening_credit
msgid "Opening credit value for this account."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__opening_debit
msgid "Opening debit"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_account__opening_debit
msgid "Opening debit value for this account."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Operation Templates"
msgstr ""

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid ""
"Operation not allowed. Since your statement line already received a number "
"(%s), you cannot reconcile it entirely with existing journal entries "
"otherwise it would make a gap in the numbering. You should book an entry and"
" make a regular revert of it in case you want to cancel it."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid "Operation not supported"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Operations"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_template__nocreate
msgid "Optional Create"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_account__tag_ids
#: model:ir.model.fields,help:account.field_account_account_template__tag_ids
msgid "Optional tags you may want to assign for custom reporting"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_report_line__code
msgid "Optional unique code to refer to this line in total formulas"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__option
#: model_terms:ir.ui.view,arch_db:account.tax_adjustments_wizard
msgid "Options"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__invoice_origin
msgid "Origin"
msgstr ""

#. module: account
#: model:ir.actions.report,name:account.action_account_original_vendor_bill
msgid "Original Bills"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__payment_id
msgid "Originator Payment"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__tax_line_id
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Originator Tax"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__tax_repartition_line_id
msgid "Originator Tax Repartition Line"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__tax_group_id
msgid "Originator tax group"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/account_selection.js:0
#, python-format
msgid "Other"
msgstr ""

#. module: account
#: model:account.account.type,name:account.data_account_type_other_income
msgid "Other Income"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Other Info"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_payment_method__payment_type__outbound
msgid "Outbound"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__mail_server_id
msgid "Outgoing mail server"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_stock_account_output_categ_id
#: model:ir.model.fields,field_description:account.field_res_company__property_stock_account_output_categ_id
msgid "Output Account for Stock Valuation"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Outstanding credits"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Outstanding debits"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Overdue"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Overdue invoices, maturity date passed"
msgstr ""

#. module: account
#: model:ir.ui.menu,name:account.menu_board_journal_1
msgid "Overview"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Package"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_move__invoice_payment_state__paid
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#: model:mail.message.subtype,name:account.mt_invoice_paid
msgid "Paid"
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:0
#, python-format
msgid "Paid Invoices"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:0
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document_with_payments
#, python-format
msgid "Paid on"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_group__parent_id
#: model:ir.model.fields,field_description:account.field_account_root__parent_id
msgid "Parent"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__parent_id
msgid "Parent Chart Template"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_report_line__parent_id
msgid "Parent Line"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__parent_id
msgid "Parent Message"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_group__parent_path
#: model:ir.model.fields,field_description:account.field_account_tax_report_line__parent_path
msgid "Parent Path"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_partial_reconcile
msgid "Partial Reconcile"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_move_reversal__refund_method__refund
msgid "Partial Refund"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__partner_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__partner_id
#: model:ir.model.fields,field_description:account.field_account_move__partner_id
#: model:ir.model.fields,field_description:account.field_account_move_line__partner_id
#: model:ir.model.fields,field_description:account.field_account_payment__partner_id
#: model_terms:ir.ui.view,arch_db:account.report_journal
#: model_terms:ir.ui.view,arch_db:account.view_account_analytic_line_filter_inherit_account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
#, python-format
msgid "Partner"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__commercial_partner_id
msgid "Partner Company"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__contract_ids
#: model:ir.model.fields,field_description:account.field_res_users__contract_ids
msgid "Partner Contracts"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_partner
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_partner
msgid "Partner Is Set"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "Partner Is Set & Matches"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_account_moves_ledger_partner
#: model:ir.ui.menu,name:account.menu_action_account_moves_ledger_partner
msgid "Partner Ledger"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__partner_name
msgid "Partner Name"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__partner_type
msgid "Partner Type"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Pay your"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Pay your bills in one-click using Euro SEPA Service"
msgstr ""

#. module: account
#: model:account.account.type,name:account.data_account_type_payable
#: model:ir.model.fields.selection,name:account.selection__account_account_type__type__payable
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Payable"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_account_payable_id
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Payable Account"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_search
msgid "Payable Accounts"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__debit_limit
#: model:ir.model.fields,field_description:account.field_res_users__debit_limit
msgid "Payable Limit"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.product_template_form_view
msgid "Payables"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__invoice_payment_state
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Payment"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_register__payment_date
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Payment Date"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__payment_difference
msgid "Payment Difference"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__payment_difference_handling
msgid "Payment Difference Handling"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:0
#, python-format
msgid "Payment Info"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:0
#, python-format
msgid "Payment Journal:"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_form
msgid "Payment Matching"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__payment_method_id
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Payment Method"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_register__payment_method_id
msgid "Payment Method Type"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_bank_journal_form
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Payment Method Types"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_payment_method
msgid "Payment Methods"
msgstr ""

#. module: account
#: model:ir.actions.report,name:account.action_report_payment_receipt
msgid "Payment Receipt"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_payment_receipt_document
msgid "Payment Receipt:"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__invoice_payment_ref
#: model:ir.model.fields,field_description:account.field_account_payment__payment_reference
msgid "Payment Reference"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Payment References"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__invoice_payment_state
msgid "Payment Status"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_payment_term_form
#: model:ir.model,name:account.model_account_payment_term
#: model:ir.model.fields,field_description:account.field_account_invoice_report__invoice_payment_term_id
#: model:ir.model.fields,field_description:account.field_account_move__invoice_payment_term_id
#: model:ir.model.fields,field_description:account.field_account_payment_term__name
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__payment_id
#: model:ir.ui.menu,name:account.menu_action_payment_term_form
#: model_terms:ir.ui.view,arch_db:account.view_partner_property_form
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_form
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_form
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_tree
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_search
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_tree
msgid "Payment Terms"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_payment_term_line
msgid "Payment Terms Line"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__payment_type
#: model:ir.model.fields,field_description:account.field_account_payment_method__payment_type
msgid "Payment Type"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_journal__post_at__pay_val
msgid "Payment Validation"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_form
msgid "Payment term explanation for the customer..."
msgstr ""

#. module: account
#: model:account.payment.term,note:account.account_payment_term_15days
msgid "Payment terms: 15 Days"
msgstr ""

#. module: account
#: model:account.payment.term,note:account.account_payment_term_2months
msgid "Payment terms: 2 Months"
msgstr ""

#. module: account
#: model:account.payment.term,note:account.account_payment_term_21days
msgid "Payment terms: 21 Days"
msgstr ""

#. module: account
#: model:account.payment.term,note:account.account_payment_term_30days
msgid "Payment terms: 30 Days"
msgstr ""

#. module: account
#: model:account.payment.term,note:account.account_payment_term_advance
msgid "Payment terms: 30% Advance End of Following Month"
msgstr ""

#. module: account
#: model:account.payment.term,note:account.account_payment_term_advance_60days
msgid "Payment terms: 30% Now, Balance 60 Days"
msgstr ""

#. module: account
#: model:account.payment.term,note:account.account_payment_term_45days
msgid "Payment terms: 45 Days"
msgstr ""

#. module: account
#: model:account.payment.term,note:account.account_payment_term_end_following_month
msgid "Payment terms: End of Following Month"
msgstr ""

#. module: account
#: model:account.payment.term,note:account.account_payment_term_immediate
msgid "Payment terms: Immediate Payment"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__payment_id
msgid "Payment that created this entry"
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:0
#: model:ir.actions.act_window,name:account.action_account_payments
#: model:ir.actions.act_window,name:account.action_account_payments_payable
#: model:ir.model,name:account.model_account_payment
#: model:ir.ui.menu,name:account.menu_action_account_payments_payable
#: model:ir.ui.menu,name:account.menu_action_account_payments_receivable
#: model:ir.ui.menu,name:account.root_payment_menu
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#, python-format
msgid "Payments"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Payments Matching"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_payments
#: model_terms:ir.actions.act_window,help:account.action_account_payments_payable
#: model_terms:ir.actions.act_window,help:account.action_account_payments_transfer
msgid ""
"Payments are used to register liquidity movements. You can process those "
"payments by your own means or by using installed facilities."
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:0
#, python-format
msgid "Payments without a customer can't be matched"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_payment_term_line__value__percent
msgid "Percent"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_accrual_accounting_wizard_form_view
msgid "Percent (%)"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_accrual_accounting_wizard__percentage
msgid "Percentage"
msgstr ""

#. module: account
#: code:addons/account/wizard/account_accrual_accounting.py:0
#, python-format
msgid "Percentage must be between 0 and 100"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_tax__amount_type__percent
#: model:ir.model.fields.selection,name:account.selection__account_tax_template__amount_type__percent
msgid "Percentage of Price"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_tax__amount_type__division
#: model:ir.model.fields.selection,name:account.selection__account_tax_template__amount_type__division
msgid "Percentage of Price Tax Included"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__second_amount_type__percentage
msgid "Percentage of amount"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__amount_type__percentage
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__second_amount_type__percentage
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__amount_type__percentage
msgid "Percentage of balance"
msgstr ""

#. module: account
#: code:addons/account/models/account_payment_term.py:0
#, python-format
msgid "Percentages on the Payment Terms lines must be between 0 and 100."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Period"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_plaid
msgid "Plaid Connector"
msgstr ""

#. module: account
#: code:addons/account/wizard/pos_box.py:0
#, python-format
msgid "Please check that the field 'Journal' is set on the Bank Statement"
msgstr ""

#. module: account
#: code:addons/account/wizard/pos_box.py:0
#, python-format
msgid "Please check that the field 'Transfer Account' is set on the company."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Please define a sequence on your journal."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Please define an accounting miscellaneous journal in your company"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Please define an accounting purchase journal in your company"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Please define an accounting sale journal in your company"
msgstr ""

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid ""
"Please go on the %s journal and define a %s Account. This account will be "
"used to record cash difference."
msgstr ""

#. module: account
#: code:addons/account/models/company.py:0
#, python-format
msgid ""
"Please install a chart of accounts or create a miscellaneous journal before "
"proceeding."
msgstr ""

#. module: account
#: code:addons/account/models/account_cash_rounding.py:0
#, python-format
msgid "Please set a positive rounding value."
msgstr ""

#. module: account
#: code:addons/account/models/account_cash_rounding.py:33
#, python-format
msgid "Please set a strictly positive rounding value."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "Please use the following communication for your payment :"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line_template__plus_report_line_ids
msgid "Plus Tax Report Lines"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__default_pos_receivable_account_id
msgid "PoS receivable account"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__access_url
msgid "Portal Access URL"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#: model_terms:ir.ui.view,arch_db:account.view_move_line_tree_grouped
msgid "Post"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Post All Entries"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__post_at
msgid "Post At"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__auto_post
msgid "Post Automatically"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_form
msgid "Post Difference In"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.validate_account_move_view
msgid "Post Journal Entries"
msgstr ""

#. module: account
#: model:ir.actions.server,name:account.action_account_confirm_payments
msgid "Post Payments"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_validate_account_move
msgid "Post entries"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_move__state__posted
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Posted"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Posted Journal Entries"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Posted Journal Items"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Posted journal entry must have an unique sequence number per company."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_form
msgid "Posting"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__bank_account_code_prefix
#: model:ir.model.fields,field_description:account.field_res_company__bank_account_code_prefix
msgid "Prefix of the bank accounts"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__cash_account_code_prefix
msgid "Prefix of the cash accounts"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__cash_account_code_prefix
msgid "Prefix of the main cash accounts"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__transfer_account_code_prefix
msgid "Prefix of the main transfer accounts"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__transfer_account_code_prefix
msgid "Prefix of the transfer accounts"
msgstr ""

#. module: account
#: model:account.account.type,name:account.data_account_type_prepayments
msgid "Prepayments"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_reconcile_model
msgid ""
"Preset to create journal entries during a invoices and payments matching"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Presets config"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Preview"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid "Preview as a PDF"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Price"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__is_print
#: model:ir.model.fields,field_description:account.field_res_config_settings__invoice_is_print
#: model_terms:ir.ui.view,arch_db:account.account_common_report_view
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_page
msgid "Print"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_common_journal_report__amount_currency
#: model:ir.model.fields,help:account.field_account_print_journal__amount_currency
msgid ""
"Print Report with the currency column if the currency differs from the "
"company currency."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__invoice_is_print
msgid "Print by default"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Print checks to pay your vendors"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_analytic_line__product_id
#: model:ir.model.fields,field_description:account.field_account_invoice_report__product_id
#: model:ir.model.fields,field_description:account.field_account_move_line__product_id
#: model_terms:ir.ui.view,arch_db:account.view_account_analytic_line_filter_inherit_account
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "Product"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_product_category
#: model:ir.model.fields,field_description:account.field_account_invoice_report__product_categ_id
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Product Category"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__quantity
msgid "Product Quantity"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_product_template
msgid "Product Template"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.product_product_action_purchasable
#: model:ir.actions.act_window,name:account.product_product_action_sellable
#: model:ir.ui.menu,name:account.product_product_menu_purchasable
#: model:ir.ui.menu,name:account.product_product_menu_sellable
#: model_terms:ir.ui.view,arch_db:account.product_template_view_tree
msgid "Products"
msgstr ""

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid "Profit"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/account_selection.js:0
#, python-format
msgid "Profit & Loss"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__profit_account_id
msgid "Profit Account"
msgstr ""

#. module: account
#: code:addons/account/models/account_reconcile_model.py:0
#, python-format
msgid ""
"Programmation Error: Can't call _get_invoice_matching_query() for different "
"rules than 'invoice_matching'"
msgstr ""

#. module: account
#: code:addons/account/models/account_reconcile_model.py:0
#, python-format
msgid ""
"Programmation Error: Can't call _get_wo_suggestion_query() for different "
"rules than 'writeoff_suggestion'"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_form
msgid "Properties"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_journal__type__purchase
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Purchase"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_move__type__in_receipt
msgid "Purchase Receipt"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Purchase Receipt Created"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Purchase Tax"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_account_moves_journal_purchase
#: model:ir.model.fields.selection,name:account.selection__account_tax__type_tax_use__purchase
#: model:ir.model.fields.selection,name:account.selection__account_tax_template__type_tax_use__purchase
#: model:ir.ui.menu,name:account.menu_action_account_moves_journal_purchase
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_search
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Purchases"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_report_line__formula
msgid ""
"Python expression used to compute the value of a total line. This field is "
"mutually exclusive with tag_name, setting it turns the line to a total line."
" Tax report line codes can be used as variables in this expression to refer "
"to the balance of the corresponding lines in the report. A formula cannot "
"refer to another line using a formula."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "QIF Import"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__quantity
msgid "Quantity"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Quantity:"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_reversal__reason
#: model:ir.model.fields,field_description:account.field_cash_box_out__name
msgid "Reason"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.tax_adjustments_wizard
msgid "Reason..."
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_move_in_receipt_type
#: model:ir.actions.act_window,name:account.action_move_out_receipt_type
#: model:ir.ui.menu,name:account.menu_action_move_in_receipt_type
#: model:ir.ui.menu,name:account.menu_action_move_out_receipt_type
msgid "Receipts"
msgstr ""

#. module: account
#: model:account.account.type,name:account.data_account_type_receivable
#: model:ir.model.fields.selection,name:account.selection__account_account_type__type__receivable
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Receivable"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_account_receivable_id
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Receivable Account"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_search
msgid "Receivable Accounts"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.product_template_form_view
msgid "Receivables"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_payment__payment_type__inbound
msgid "Receive Money"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__partner_bank_account_id
msgid "Recipient Bank Account"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid "Recipients"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_accrual_accounting_wizard_form_view
msgid "Recognize on"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__recompute_tax_line
msgid "Recompute Tax Line"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: model:ir.actions.client,name:account.action_view_account_move_line_reconcile
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_form
#, python-format
msgid "Reconcile"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_reconcile_model_template
msgid "Reconcile Model Template"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__reconciled
#: model:ir.model.fields.selection,name:account.selection__account_payment__state__reconciled
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Reconciled"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Reconciled Entries"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__reconciled_invoice_ids
msgid "Reconciled Invoices"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__reconciled_invoices_count
msgid "Reconciled Invoices Count"
msgstr ""

#. module: account
#: model:ir.actions.client,name:account.action_manual_reconciliation
#: model:ir.ui.menu,name:account.menu_action_manual_reconciliation
msgid "Reconciliation"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__reconcile_model_id
msgid "Reconciliation Model"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_account_reconcile_model
#: model:ir.ui.menu,name:account.action_account_reconcile_model_menu
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Reconciliation Models"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_full_reconcile__partial_reconcile_ids
msgid "Reconciliation Parts"
msgstr ""

#. module: account
#: model:ir.actions.client,name:account.action_bank_reconcile
msgid "Reconciliation on Bank Statements"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Record transactions in foreign currencies"
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid "Recursion found for tax '%s'."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Ref"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_analytic_line__ref
msgid "Ref."
msgstr ""

#. module: account
#: code:addons/account/controllers/portal.py:0
#: model:ir.model.fields,field_description:account.field_account_bank_statement__name
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__ref
#: model:ir.model.fields,field_description:account.field_account_move__ref
#: model:ir.model.fields,field_description:account.field_account_move_line__ref
#, python-format
msgid "Reference"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_payment__payment_reference
msgid ""
"Reference of the document used to issue this payment. Eg. check number, file"
" name, etc."
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_move_in_refund_type
#: model:ir.ui.menu,name:account.menu_action_move_in_refund_type
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Refund"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Refund Created"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_reversal
msgid "Refund Date"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line__refund_tax_id
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line_template__refund_tax_id
msgid "Refund Tax"
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:0
#: model:ir.actions.server,name:account.action_account_invoice_from_list
#: model:ir.model,name:account.model_account_payment_register
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_form
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_form_multi
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#, python-format
msgid "Register Payment"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_bank_statement_tree
msgid "Register a bank statement"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_move_in_receipt_type
msgid "Register a new purchase receipt"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_payments
#: model_terms:ir.actions.act_window,help:account.action_account_payments_payable
#: model_terms:ir.actions.act_window,help:account.action_account_payments_transfer
msgid "Register a payment"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_account_type__type__other
msgid "Regular"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__res_id
msgid "Related Document ID"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__model
msgid "Related Document Model"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_tax_form
msgid "Repartition for Credit Notes"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__invoice_repartition_line_ids
#: model:ir.model.fields,field_description:account.field_account_tax_template__invoice_repartition_line_ids
#: model_terms:ir.ui.view,arch_db:account.view_tax_form
msgid "Repartition for Invoices"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__refund_repartition_line_ids
#: model:ir.model.fields,field_description:account.field_account_tax_template__refund_repartition_line_ids
msgid "Repartition for Refund Invoices"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__refund_repartition_line_ids
#: model:ir.model.fields,help:account.field_account_tax_template__refund_repartition_line_ids
msgid "Repartition when the tax is used on a refund"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__invoice_repartition_line_ids
#: model:ir.model.fields,help:account.field_account_tax_template__invoice_repartition_line_ids
msgid "Repartition when the tax is used on an invoice"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__tax_dest_id
msgid "Replacement Tax"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__reply_to
msgid ""
"Reply email address. Setting the reply_to bypasses the automatic thread "
"creation."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__reply_to
msgid "Reply-To"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_ir_actions_report
#: model:ir.model.fields,field_description:account.field_account_tax_report_line__report_action_id
msgid "Report Action"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_tax_adjustments_wizard__tax_report_line_id
msgid "Report Line"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_common_report_view
msgid "Report Options"
msgstr ""

#. module: account
#: model:ir.ui.menu,name:account.account_report_folder
#: model:ir.ui.menu,name:account.menu_finance_reports
msgid "Reporting"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_cash_rounding__rounding
msgid "Represent the non-zero value smallest coinage (for example, 0.05)."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__require_partner_bank_account
msgid "Require Partner Bank Account"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__res_partner_bank_id
msgid "Res Partner Bank"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_form
msgid "Reset To Draft"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Reset to Draft"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_form
msgid "Reset to New"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: model:ir.model.fields,field_description:account.field_account_move_reversal__residual
#, python-format
msgid "Residual"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__amount_residual
msgid "Residual Amount"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__amount_residual_currency
msgid "Residual Amount in Currency"
msgstr ""

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:0
#, python-format
msgid "Residual amount"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__user_id
msgid "Responsible"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__activity_user_id
#: model:ir.model.fields,field_description:account.field_account_move__activity_user_id
#: model:ir.model.fields,field_description:account.field_account_payment__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_partner_category_ids
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_partner_category_ids
msgid "Restrict Partner Categories to"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_partner_ids
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_partner_ids
msgid "Restrict Partners to"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_same_currency
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_same_currency
msgid ""
"Restrict to propositions having the same currency as the statement line."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_digest_digest__kpi_account_total_revenue
#: model:ir.model.fields.selection,name:account.selection__account_accrual_accounting_wizard__account_type__income
msgid "Revenue"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_accrual_accounting_wizard__revenue_accrual_account
#: model:ir.model.fields,field_description:account.field_res_company__revenue_accrual_account_id
msgid "Revenue Accrual Account"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__account_id
msgid "Revenue/Expense Account"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__reversal_move_id
msgid "Reversal Move"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_reversal__date
msgid "Reversal date"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__reversed_entry_id
msgid "Reversal of"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Reversal of %s"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#: code:addons/account/wizard/account_move_reversal.py:0
#, python-format
msgid "Reversal of: %s"
msgstr ""

#. module: account
#: code:addons/account/wizard/account_move_reversal.py:0
#, python-format
msgid "Reversal of: %s, %s"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_view_account_move_reversal
#: model_terms:ir.ui.view,arch_db:account.view_account_move_reversal
#: model_terms:ir.ui.view,arch_db:account.view_move_line_tree_grouped
msgid "Reverse"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Reverse Entry"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_reversal
msgid "Reverse Journal Entry"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#: code:addons/account/wizard/account_move_reversal.py:0
#, python-format
msgid "Reverse Moves"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_form
msgid "Revert reconciliation"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_chart_of_account_step
msgid "Review"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__root_id
#: model:ir.model.fields,field_description:account.field_account_account_template__root_id
msgid "Root"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_company__tax_calculation_rounding_method__round_globally
msgid "Round Globally"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_company__tax_calculation_rounding_method__round_per_line
msgid "Round per Line"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.rounding_form_view
msgid "Rounding Form"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__rounding_method
msgid "Rounding Method"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__rounding
msgid "Rounding Precision"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cash_rounding__strategy
msgid "Rounding Strategy"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.rounding_tree_view
msgid "Rounding Tree"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_sepa
msgid "SEPA Credit Transfer (SCT)"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "SEPA Direct Debit (SDD)"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "SEPA QR Code"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_has_sms_error
#: model:ir.model.fields,field_description:account.field_account_journal__message_has_sms_error
#: model:ir.model.fields,field_description:account.field_account_move__message_has_sms_error
#: model:ir.model.fields,field_description:account.field_account_payment__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Sale"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_account_moves_journal_sales
#: model:ir.model.fields.selection,name:account.selection__account_journal__type__sale
#: model:ir.model.fields.selection,name:account.selection__account_tax__type_tax_use__sale
#: model:ir.model.fields.selection,name:account.selection__account_tax_template__type_tax_use__sale
#: model:ir.ui.menu,name:account.menu_action_account_moves_journal_sales
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_search
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Sales"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_invoice_tree
msgid "Sales Person"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_move__type__out_receipt
msgid "Sales Receipt"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Sales Receipt Created"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_onboarding_sale_tax_form
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Sales Tax"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_open_account_onboarding_sale_tax
msgid "Sales tax"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__invoice_user_id
#: model:ir.model.fields,field_description:account.field_account_move__invoice_user_id
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Salesperson"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_same_currency
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_same_currency
msgid "Same Currency Matching"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_sample_invoice_step
msgid "Sample Invoice"
msgstr ""

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:0
#, python-format
msgid "Sample data"
msgstr ""

#. module: account
#: code:addons/account/models/company.py:0
#, python-format
msgid "Sample invoice"
msgstr ""

#. module: account
#: code:addons/account/models/company.py:0
#, python-format
msgid "Sample invoice line name"
msgstr ""

#. module: account
#: code:addons/account/models/company.py:0
#, python-format
msgid "Sample invoice line name 2"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_sample_invoice_step
msgid "Sample invoice sent!"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__sanitized_acc_number
msgid "Sanitized Account Number"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Save and New"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid "Save as a new template"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid "Save as new template"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Save this page and come back here to set up the feature."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_search
msgid "Search Account Journal"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_template_search
msgid "Search Account Templates"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_search
msgid "Search Bank Statements"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_line_search
msgid "Search Bank Statements Line"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_chart_template_seacrh
msgid "Search Chart of Account Templates"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_position_filter
msgid "Search Fiscal Positions"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
msgid "Search Invoice"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Search Journal Items"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Search Move"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Search Tax Templates"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_tax_view_search
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Search Taxes"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__second_account_id
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__second_account_id
msgid "Second Account"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__second_amount_from_label_regex
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__second_amount_from_label_regex
msgid "Second Amount from Label (regex)"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__second_amount_type
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__second_amount_type
msgid "Second Amount type"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__second_analytic_account_id
msgid "Second Analytic Account"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__second_analytic_tag_ids
msgid "Second Analytic Tags"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__second_journal_id
msgid "Second Journal"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__second_label
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__second_label
msgid "Second Journal Item Label"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__force_second_tax_included
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__force_second_tax_included
msgid "Second Tax Included in Price"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__second_tax_ids
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__second_tax_ids
msgid "Second Taxes"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__second_amount
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__second_amount
msgid "Second Write-off Amount"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_move_line__display_type__line_section
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Section"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__secure_sequence_id
msgid "Secure Sequence"
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid "Securisation of %s - %s"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__access_token
msgid "Security Token"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_journal_activity.xml:0
#, python-format
msgid "See all activities"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__type
msgid ""
"Select 'Sale' for customer invoices journals.\n"
"Select 'Purchase' for vendor bills journals.\n"
"Select 'Cash' or 'Bank' for journals that are used in customer or vendor payments.\n"
"Select 'General' for miscellaneous operations journals."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:0
#, python-format
msgid "Select Partner"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Select a partner or choose a counterpart"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Select an old vendor bill"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_payment_term_line__value
msgid "Select here the kind of valuation related to this payment terms line."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Select this if the taxes should use cash basis, which will create an entry "
"for such taxes on a given account during reconciliation."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__invoice_warn
#: model:ir.model.fields,help:account.field_res_users__invoice_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid "Send"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Send & Print"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.invoice_send
msgid "Send & print"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__invoice_is_email
msgid "Send Email"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#: code:addons/account/wizard/account_invoice_send.py:0
#, python-format
msgid "Send Invoice"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_payment__payment_type__outbound
msgid "Send Money"
msgstr ""

#. module: account
#: code:addons/account/wizard/account_payment_send.py:0
#, python-format
msgid "Send Payment"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_open_account_onboarding_sample_invoice
msgid "Send a sample invoice"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_sample_invoice_step
msgid "Send an invoice to test the customer portal."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Send invoices and payment follow-ups by post"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.account_send_payment_receipt_by_email_action
msgid "Send receipt by email"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.account_send_payment_receipt_by_email_action_multi
msgid "Send receipts by email"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_sample_invoice_step
msgid "Send sample"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_payment__state__sent
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Sent"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_company__fiscalyear_last_month__9
msgid "September"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__sequence
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__sequence
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__sequence
#: model:ir.model.fields,field_description:account.field_account_journal__sequence
#: model:ir.model.fields,field_description:account.field_account_journal_group__sequence
#: model:ir.model.fields,field_description:account.field_account_move_line__sequence
#: model:ir.model.fields,field_description:account.field_account_payment_term__sequence
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__sequence
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__sequence
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__sequence
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__sequence
#: model:ir.model.fields,field_description:account.field_account_tax__sequence
#: model:ir.model.fields,field_description:account.field_account_tax_group__sequence
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line__sequence
#: model:ir.model.fields,field_description:account.field_account_tax_report_line__sequence
#: model:ir.model.fields,field_description:account.field_account_tax_template__sequence
msgid "Sequence"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_report_line__sequence
msgid ""
"Sequence determining the order of the lines in the report (smaller ones come"
" first). This order is applied locally per section (so, children of the same"
" line are always rendered one after the other)."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__secure_sequence_id
msgid "Sequence to use to ensure the securisation of data"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_account_tag__active
msgid "Set active to false to hide the Account Tag without removing it."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__active
msgid "Set active to false to hide the Journal without removing it."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__active
#: model:ir.model.fields,help:account.field_account_tax_template__active
msgid "Set active to false to hide the tax without removing it."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_sale_tax_step
msgid "Set taxes"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template__visible
msgid ""
"Set this to False if you don't want this template to be used actively in the"
" wizard that generate Chart of Accounts from templates, this is useful when "
"you want to generate accounts of this template only when loading its child "
"template."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: model:ir.actions.act_window,name:account.action_account_config
#: model:ir.actions.act_window,name:account.action_open_settings
#: model:ir.ui.menu,name:account.menu_account_config
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#, python-format
msgid "Settings"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_bank_account_step
msgid "Setup your bank account to sync bank feeds."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_chart_of_account_step
msgid "Setup your chart of accounts and record initial balances."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.dashboard_onboarding_company_step
msgid "Setup your company's data for reports headers."
msgstr ""

#. module: account
#: model:ir.actions.server,name:account.model_account_move_action_share
msgid "Share"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__code
msgid "Short Code"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_report_line__tag_name
msgid ""
"Short name for the tax grid corresponding to this report line. Leave empty "
"if this report line should not correspond to any such grid."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__show_force_tax_included
msgid "Show Force Tax Included"
msgstr ""

#. module: account
#: model:res.groups,name:account.group_account_user
msgid "Show Full Accounting Features"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__show_partner_bank_account
msgid "Show Partner Bank Account"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__show_second_force_tax_included
msgid "Show Second Force Tax Included"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Show active taxes"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Show all records which has next action date is before today"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Show inactive taxes"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_journal__show_on_dashboard
msgid "Show journal on dashboard"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__group_show_line_subtotals_tax_included
msgid "Show line subtotals with taxes (B2C)"
msgstr ""

#. module: account
#: model:res.groups,comment:account.group_show_line_subtotals_tax_included
msgid "Show line subtotals with taxes included (B2C)"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__group_show_line_subtotals_tax_excluded
#: model:res.groups,comment:account.group_show_line_subtotals_tax_excluded
msgid "Show line subtotals without taxes (B2B)"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Show standard terms &amp; conditions on invoices/orders"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Skip"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_snailmail_account
msgid "Snailmail"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_renderer.js:0
#, python-format
msgid "Some fields are undefined"
msgstr ""

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid ""
"Some selected statement line were not already reconciled with an account "
"move."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_setup_bank_manual_config__bank_bic
msgid "Sometimes called BIC or Swift."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_invoice_tree
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Source Document"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__invoice_source_email
msgid "Source Email"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_cash_rounding__strategy
msgid ""
"Specify which way will be used to round the invoice amount to the rounding "
"precision"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__start_bank_stmt_ids
msgid "Start Bank Stmt"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__date_from
#: model:ir.model.fields,field_description:account.field_account_common_report__date_from
#: model:ir.model.fields,field_description:account.field_account_fiscal_year__date_from
#: model:ir.model.fields,field_description:account.field_account_print_journal__date_from
msgid "Start Date"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_fiscal_year__date_from
msgid "Start Date, included in the fiscal year."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__balance_start
msgid "Starting Balance"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__cashbox_start_id
msgid "Starting Cashbox"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_dashboard_onboarding_state
msgid "State of the account dashboard onboarding panel"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_invoice_onboarding_state
msgid "State of the account invoice onboarding panel"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_setup_bank_data_state
msgid "State of the onboarding bank data step"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_setup_coa_state
msgid "State of the onboarding charts of account step"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_setup_fy_data_state
msgid "State of the onboarding fiscal year step"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_onboarding_invoice_layout_state
msgid "State of the onboarding invoice layout step"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_onboarding_sale_tax_state
msgid "State of the onboarding sale tax step"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__account_onboarding_sample_invoice_state
msgid "State of the onboarding sample invoice step"
msgstr ""

#. module: account
#: model:ir.actions.report,name:account.action_report_account_statement
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__statement_id
#: model:ir.model.fields,field_description:account.field_account_move_line__statement_id
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Statement"
msgstr ""

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid "Statement %s confirmed, journal items were created."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_line_form
msgid "Statement Line"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__line_ids
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_form
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_line_tree
msgid "Statement lines"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_tree
msgid "Statements"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
msgid "States"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__states_count
msgid "States Count"
msgstr ""

#. module: account
#: code:addons/account/controllers/portal.py:0
#: model:ir.model.fields,field_description:account.field_account_bank_statement__state
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__state
#: model:ir.model.fields,field_description:account.field_account_move__state
#: model:ir.model.fields,field_description:account.field_account_move_line__parent_state
#: model:ir.model.fields,field_description:account.field_account_payment__state
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_search
#, python-format
msgid "Status"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__activity_state
#: model:ir.model.fields,help:account.field_account_move__activity_state
#: model:ir.model.fields,help:account.field_account_payment__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.onboarding_bank_account_step
#: model_terms:ir.ui.view,arch_db:account.onboarding_fiscal_year_step
#: model_terms:ir.ui.view,arch_db:account.onboarding_sale_tax_step
msgid "Step Completed!"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__string_to_hash
msgid "String To Hash"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__subject
msgid "Subject"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_invoice_send_wizard_form
msgid "Subject..."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_tax_report_line_form
msgid "Sublines"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_cashbox_line__subtotal
#: model:ir.model.fields,field_description:account.field_account_move_line__price_subtotal
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Subtotal"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__subtype_id
msgid "Subtype"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model_template__rule_type__writeoff_suggestion
msgid "Suggest a write-off."
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_reconcile_model__rule_type__writeoff_suggestion
msgid "Suggest counterpart values."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__supplier_rank
#: model:ir.model.fields,field_description:account.field_res_users__supplier_rank
msgid "Supplier Rank"
msgstr ""

#. module: account
#: model:ir.actions.server,name:account.action_move_switch_invoice_to_credit_note
msgid "Switch into refund/credit note"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag__name
#: model:ir.model.fields,field_description:account.field_account_tax_report_line__tag_name
msgid "Tag Name"
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"Tag name %(tag)s is used by more than one tax report line in %(country)s. "
"Each tag name should only be used once per country."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"Tag name and formula are mutually exclusive, they should not be set together"
" on the same tax report line."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__tag_ids
#: model:ir.model.fields,field_description:account.field_account_move_line__tag_ids
#: model:ir.model.fields,field_description:account.field_account_tax_report_line__tag_ids
#: model_terms:ir.ui.view,arch_db:account.account_tag_view_form
#: model_terms:ir.ui.view,arch_db:account.account_tag_view_tree
msgid "Tags"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__tag_ids
msgid ""
"Tags assigned to this line by the tax creating it, if any. It determines its"
" impact on financial reports."
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_cash_box_out
#: model_terms:ir.ui.view,arch_db:account.cash_box_out_form
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_form
msgid "Take Money In/Out"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__target_move
#: model:ir.model.fields,field_description:account.field_account_common_report__target_move
#: model:ir.model.fields,field_description:account.field_account_print_journal__target_move
msgid "Target Moves"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_tax
#: model:ir.model.fields,field_description:account.field_account_move__amount_tax
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line__tax_id
#: model_terms:ir.ui.view,arch_db:account.account_tax_view_search
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
#: model_terms:ir.ui.view,arch_db:account.view_invoice_tree
#: model_terms:ir.ui.view,arch_db:account.view_move_line_tax_audit_tree
msgid "Tax"
msgstr ""

#. module: account
#: code:addons/account/models/chart_template.py:0
#: code:addons/account/models/chart_template.py:0
#, python-format
msgid "Tax %.2f%%"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.tax_adjustments_form
#: model:ir.ui.menu,name:account.menu_action_tax_adjustment
msgid "Tax Adjustments"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_tax_adjustments_wizard
msgid "Tax Adjustments Wizard"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "Tax Amount"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_search
msgid "Tax Application"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__tax_audit
msgid "Tax Audit String"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__tax_calculation_rounding_method
msgid "Tax Calculation Rounding Method"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__tax_cash_basis_rec_id
msgid "Tax Cash Basis Entry of"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__tax_cash_basis_journal_id
msgid "Tax Cash Basis Journal"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__amount_type
#: model:ir.model.fields,field_description:account.field_account_tax_template__amount_type
msgid "Tax Computation"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_journal
msgid "Tax Declaration"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__tax_exigibility
#: model:ir.model.fields,field_description:account.field_account_tax_template__tax_exigibility
msgid "Tax Due"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_invoice_tree
msgid "Tax Excluded"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax_repartition_line__tag_ids
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#: model_terms:ir.ui.view,arch_db:account.view_move_line_tree_grouped
msgid "Tax Grids"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_tax_group
#: model:ir.model.fields,field_description:account.field_account_tax__tax_group_id
#: model:ir.model.fields,field_description:account.field_account_tax_template__tax_group_id
msgid "Tax Group"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_tax_group
msgid "Tax Groups"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "Tax ID"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__force_tax_included
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__force_tax_included
#, python-format
msgid "Tax Included in Price"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__tax_lock_date
msgid "Tax Lock Date"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__tax_lock_date_message
msgid "Tax Lock Date Message"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__tax_ids
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__tax_ids
#: model_terms:ir.ui.view,arch_db:account.view_account_position_form
msgid "Tax Mapping"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_tax_template
msgid "Tax Mapping Template of Fiscal Position"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_tax
msgid "Tax Mapping of Fiscal Position"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__name
#: model:ir.model.fields,field_description:account.field_account_tax_template__name
msgid "Tax Name"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_tax_repartition_line
msgid "Tax Repartition Line"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_tax_repartition_line_template
msgid "Tax Repartition Line Template"
msgstr ""

#. module: account
#: model:ir.ui.menu,name:account.menu_configure_tax_report
msgid "Tax Report"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account_tag__tax_report_line_ids
msgid "Tax Report Lines"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_tax__type_tax_use
#: model:ir.model.fields,field_description:account.field_account_tax_template__type_tax_use
msgid "Tax Scope"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__amount_tax_signed
msgid "Tax Signed"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax_template__tax_src_id
msgid "Tax Source"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Tax Template"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__tax_template_ids
msgid "Tax Template List"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.action_account_tax_template_form
msgid "Tax Templates"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__amount_by_group
msgid "Tax amount by group"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__tax_calculation_rounding_method
msgid "Tax calculation rounding method"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_tax_payable_account_id
#: model:ir.model.fields,field_description:account.field_account_tax_group__property_tax_payable_account_id
msgid "Tax current account (payable)"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__property_tax_receivable_account_id
#: model:ir.model.fields,field_description:account.field_account_tax_group__property_tax_receivable_account_id
msgid "Tax current account (receivable)"
msgstr ""

#. module: account
#: model:res.groups,name:account.group_show_line_subtotals_tax_excluded
msgid "Tax display B2B"
msgstr ""

#. module: account
#: model:res.groups,name:account.group_show_line_subtotals_tax_included
msgid "Tax display B2C"
msgstr ""

#. module: account
#: model:ir.model.constraint,message:account.constraint_account_tax_name_company_uniq
#: model:ir.model.constraint,message:account.constraint_account_tax_template_name_company_uniq
msgid "Tax names must be unique !"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__tax_src_id
msgid "Tax on Product"
msgstr ""

#. module: account
#: code:addons/account/models/chart_template.py:0
#, python-format
msgid ""
"Tax repartition line templates should apply to either invoices or refunds, "
"not both at the same time. invoice_tax_id and refund_tax_id should not be "
"set together."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__tax_repartition_line_id
msgid ""
"Tax repartition line that caused the creation of this move line, if any"
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"Tax repartition lines should apply to either invoices or refunds, not both "
"at the same time. invoice_tax_id and refund_tax_id should not be set "
"together."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_repartition_line_template__plus_report_line_ids
msgid ""
"Tax report lines whose '+' tag will be assigned to move lines by this "
"repartition line"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_repartition_line_template__minus_report_line_ids
msgid ""
"Tax report lines whose '-' tag will be assigned to move lines by this "
"repartition line"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_report_line__tag_ids
msgid "Tax tags populating this line"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_tax__tax_dest_id
msgid "Tax to Apply"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_config_settings__show_line_subtotals_tax_selection__tax_excluded
msgid "Tax-Excluded"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_config_settings__show_line_subtotals_tax_selection__tax_included
msgid "Tax-Included"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "TaxCloud"
msgstr ""

#. module: account
#. openerp-web
#: model:account.tax.group,name:account.tax_group_taxes
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: model:ir.actions.act_window,name:account.action_tax_form
#: model:ir.model.fields,field_description:account.field_account_move_line__tax_ids
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__tax_ids
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__tax_ids
#: model:ir.model.fields.selection,name:account.selection__account_account_tag__applicability__taxes
#: model:ir.ui.menu,name:account.menu_action_tax_form
#: model_terms:ir.ui.view,arch_db:account.onboarding_sale_tax_step
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
#: model_terms:ir.ui.view,arch_db:account.view_move_line_form
#, python-format
msgid "Taxes"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Taxes Applied"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_position_template_form
msgid "Taxes Mapping"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__tax_ids
msgid "Taxes that apply on the base amount"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Taxes used in Purchases"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_tax_template_search
msgid "Taxes used in Sales"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Taxes, fiscal positions, chart of accounts &amp; legal statements for your "
"country"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_payment__invoice_ids
msgid ""
"Technical field containing the invoice for which the payment has been generated.\n"
"                                   This does not especially correspond to the invoices reconciled with the payment,\n"
"                                   as it can have been generated first, and reconciled later"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__display_type
msgid "Technical field for UX purpose."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__balance
msgid ""
"Technical field holding the debit - credit in order to open meaningful graph"
" views from reports"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line__move_name
#: model:ir.model.fields,help:account.field_account_payment__move_name
msgid ""
"Technical field holding the number given to the journal entry, automatically"
" set when the statement line is reconciled then stored to set the same "
"number again if the line is cancelled, set to draft and re-processed again."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move__bank_partner_id
msgid "Technical field to get the domain on the bank"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__journal_type
#: model:ir.model.fields,help:account.field_account_payment__has_invoices
msgid "Technical field used for usability purposes"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_payment__payment_method_code
msgid ""
"Technical field used to adapt the interface to the payment type selected."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__always_set_currency_id
msgid ""
"Technical field used to compute the monetary field. As currency_id is not a "
"required field, we need to use either the foreign currency, either the "
"company one."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_partial_reconcile__max_date
msgid ""
"Technical field used to determine at which date this reconciliation needs to"
" be shown on the aged receivable/payable reports."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move__tax_lock_date_message
msgid ""
"Technical field used to display a message when the invoice's accounting date"
" is prior of the tax lock date."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move__invoice_has_matching_suspense_amount
msgid ""
"Technical field used to display an alert on invoices if there is at least a "
"matching amount in any supsense account."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__exclude_from_invoice_tab
msgid ""
"Technical field used to exclude some lines from the invoice_line_ids tab in "
"the form view."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move__user_id
msgid "Technical field used to fit the generic behavior in mail templates."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move__invoice_filter_type_domain
msgid ""
"Technical field used to have a dynamic domain on journal / taxes in the form"
" view."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_payment__hide_payment_method
msgid ""
"Technical field used to hide the payment method if the selected journal has "
"only one available which is 'manual'"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move__tax_cash_basis_rec_id
msgid ""
"Technical field used to keep track of the tax cash basis reconciliation. "
"This is needed when cancelling the source: it will post the inverse journal "
"entry to cancel that part too."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__recompute_tax_line
msgid ""
"Technical field used to know on which lines the taxes must be recomputed."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_payment__show_partner_bank_account
msgid ""
"Technical field used to know whether the field `partner_bank_account_id` "
"needs to be displayed or not in the payments form views"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_payment__require_partner_bank_account
msgid ""
"Technical field used to know whether the field `partner_bank_account_id` "
"needs to be required or not in the payments form views"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__tax_exigible
msgid ""
"Technical field used to mark a tax line as exigible in the vat report or not"
" (only exigible journal items are displayed). By default all new journal "
"items are directly exigible, but with the feature cash_basis on taxes, some "
"will become exigible only when the payment is recorded."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_repartition_line__country_id
msgid "Technical field used to restrict tags domain in form view."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__country_id
msgid ""
"Technical field used to restrict the domain of account tags for tax "
"repartition lines created for this tax."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__is_rounding_line
msgid "Technical field used to retrieve the cash rounding line."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__show_force_tax_included
#: model:ir.model.fields,help:account.field_account_reconcile_model__show_second_force_tax_included
msgid "Technical field used to show the force tax included button"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line__account_number
msgid ""
"Technical field used to store the bank account number before its creation, "
"upon the line's processing"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__chart_template_id
msgid "Template"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_fiscal_position_template
msgid "Template for Fiscal Position"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_account_template
msgid "Templates for Accounts"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_account_tax_template
msgid "Templates for Taxes"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_line_form
msgid "Term Type"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term__line_ids
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_form
msgid "Terms"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__invoice_terms
msgid "Terms & Conditions"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__narration
msgid "Terms and Conditions"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "That's on average"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_account__internal_group
#: model:ir.model.fields,help:account.field_account_account_type__internal_group
msgid ""
"The 'Internal Group' is used to filter accounts based on the internal group "
"set on the account type."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_account__internal_type
#: model:ir.model.fields,help:account.field_account_account_type__type
#: model:ir.model.fields,help:account.field_account_move_line__account_internal_type
msgid ""
"The 'Internal Type' is used for features available on different types of "
"accounts: liquidity type is for cash or bank accounts, payable/receivable is"
" for vendor/customer accounts."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "The account %s (%s) is deprecated."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"The account is already in use in a 'sale' or 'purchase' journal. This means "
"that the account's type couldn't be 'receivable' or 'payable'."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"The account selected on your journal entry forces to provide a secondary "
"currency. You should remove the secondary currency on the account."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"The accounting date is prior to the tax lock date which is set on %s. Then, "
"this will be moved to the next available one during the invoice validation."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_setup_bank_manual_config__journal_id
#: model:ir.model.fields,help:account.field_res_partner_bank__journal_id
msgid "The accounting journal corresponding to this bank account."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings__currency_exchange_journal_id
msgid ""
"The accounting journal where automatic exchange differences will be "
"registered"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/js/reconciliation/reconciliation_model.js:0
#, python-format
msgid "The amount %s is not a valid partial amount"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line__amount_currency
#: model:ir.model.fields,help:account.field_account_move_line__amount_currency
msgid ""
"The amount expressed in an optional other currency if it is a multi-currency"
" entry."
msgstr ""

#. module: account
#: model:ir.model.constraint,message:account.constraint_account_move_line_check_amount_currency_balance_sign
msgid ""
"The amount expressed in the secondary currency must be positive when account"
" is debited and negative when account is credited. Moreover, the currency "
"field has to be left empty when the amount is expressed in the company "
"currency."
msgstr ""

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid "The amount of a cash transaction cannot be 0."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"The application scope of taxes in a group must be either the same as the "
"group or left empty."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"The bank account of a bank journal must belong to the same company (%s)."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_company__account_bank_reconciliation_start
msgid ""
"The bank reconciliation widget won't ask to reconcile payments older than this date.\n"
"                                                                                                       This is useful if you install accounting after having used invoicing for some time and\n"
"                                                                                                       don't want to reconcile all the past payments with bank statements."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings__account_bank_reconciliation_start
msgid ""
"The bank reconciliation widget won't ask to reconcile payments older than this date.\n"
"               This is useful if you install accounting after having used invoicing for some time and\n"
"               don't want to reconcile all the past payments with bank statements."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__statement_id
msgid "The bank statement used for bank reconciliation"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_company__chart_template_id
msgid "The chart template for the company (if any)"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_bnk_stmt_check
msgid "The closing balance is different than the computed one!"
msgstr ""

#. module: account
#: model:ir.model.constraint,message:account.constraint_account_journal_code_company_uniq
msgid "The code and name of the journal must be unique per company !"
msgstr ""

#. module: account
#: model:ir.model.constraint,message:account.constraint_account_account_code_company_uniq
msgid "The code of the account must be unique per company !"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"The combination of reference model and reference type on the journal is not "
"implemented"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_repartition_line__company_id
msgid "The company this repartition line belongs to."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_reversal
msgid "The credit note is auto-validated and reconciled with the invoice."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_reversal
msgid ""
"The credit note is auto-validated and reconciled with the invoice.\n"
"                               The original invoice is duplicated as a new draft."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_reversal
msgid ""
"The credit note is created in draft and can be edited before being issued."
msgstr ""

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid ""
"The currency of the bank statement line must be different than the statement"
" currency."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"The currency of the journal should be the same than the default credit "
"account."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"The currency of the journal should be the same than the default debit "
"account."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__currency_id
msgid "The currency used to enter statement"
msgstr ""

#. module: account
#: code:addons/account/models/account_payment_term.py:0
#, python-format
msgid "The day of the month used for this term must be strictly positive."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move__invoice_origin
msgid "The document(s) that generated the invoice."
msgstr ""

#. module: account
#: code:addons/account/models/account_bank_statement.py:0
#, python-format
msgid ""
"The ending balance is incorrect !\n"
"The expected balance (%s) is different from the computed one. (%s)"
msgstr ""

#. module: account
#: code:addons/account/models/account_fiscal_year.py:0
#, python-format
msgid "The ending date must not be prior to the starting date."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_product_category__property_account_expense_categ_id
msgid ""
"The expense is accounted for when a vendor bill is validated, except in "
"anglo-saxon accounting with perpetual inventory valuation in which case the "
"expense (Cost of Goods Sold account) is recognized at the customer invoice "
"validation."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"The field 'Customer' is required, please complete it to validate the "
"Customer Invoice."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"The field 'Vendor' is required, please complete it to validate the Vendor "
"Bill."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__property_account_position_id
#: model:ir.model.fields,help:account.field_res_users__property_account_position_id
msgid ""
"The fiscal position determines the taxes/accounts used for this contact."
msgstr ""

#. module: account
#: code:addons/account/wizard/account_invoice_send.py:0
#, python-format
msgid ""
"The following invoice(s) will not be sent by email, because the customers "
"don't have email address."
msgstr ""

#. module: account
#: code:addons/account/wizard/account_payment_send.py:0
#, python-format
msgid ""
"The following payment(s) will not be sent by email, because the customers "
"don't have email address."
msgstr ""

#. module: account
#: code:addons/account/models/chart_template.py:0
#, python-format
msgid ""
"The following tax report lines are used in some tax repartition template "
"though they don't generate any tag: %s . This probably means you forgot to "
"set a tag_name on these lines."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_hash_integrity
msgid ""
"The hash chain is compliant: it is not possible to alter the\n"
"                                            data without breaking the hash chain for subsequent parts."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid "The holder of a journal's bank account must be the company (%s)."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__code
msgid "The journal entries of this journal will be named using this prefix."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_company__account_opening_move_id
msgid ""
"The journal entry containing the initial balance of all this company's "
"accounts."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_financial_year_op__fiscalyear_last_day
#: model:ir.model.fields,help:account.field_account_financial_year_op__fiscalyear_last_month
msgid ""
"The last day of the month will be used if the chosen day doesn't exist."
msgstr ""

#. module: account
#: code:addons/account/models/account_payment_term.py:0
#, python-format
msgid "The last line of a Payment Term should have the Balance type."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_payment_term_form
msgid ""
"The last line's computation type should be \"Balance\" to ensure that the "
"whole amount will be allocated."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__move_id
msgid "The move of this entry line."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__refund_sequence_number_next
msgid "The next sequence number will be used for the next credit note."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__sequence_number_next
msgid "The next sequence number will be used for the next invoice."
msgstr ""

#. module: account
#: code:addons/account/models/account_payment_term.py:0
#, python-format
msgid "The number of days used for a payment term cannot be negative."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"The operation is refused as it would impact an already issued tax statement."
" Please change the journal entry date or the tax lock date set in the "
"settings (%s) to proceed."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_report_line__report_action_id
msgid ""
"The optional action to call when clicking on this line in accounting "
"reports."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line__currency_id
msgid "The optional other currency if it is a multi-currency entry."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__quantity
msgid ""
"The optional quantity expressed by this line, eg: number of product sold. "
"The quantity is not a legal requirement but is very useful for some reports."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_repartition_line__sequence
msgid ""
"The order in which display and match repartition lines. For refunds to work "
"properly, invoice repartition lines should be arranged in the same order as "
"the credit note repartition lines they correspond to."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__has_unreconciled_entries
#: model:ir.model.fields,help:account.field_res_users__has_unreconciled_entries
msgid ""
"The partner has at least one unreconciled debit and credit since last time "
"the invoices & payments matching was performed."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"The partners of the journal's company and the related bank account mismatch."
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:0
#, python-format
msgid "The payment amount cannot be negative."
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:0
#, python-format
msgid "The payment cannot be processed because the invoice is not open!"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move__invoice_payment_ref
msgid "The payment reference to set on journal items."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"The payments which have not been matched with a bank statement will not be "
"shown in bank reconciliation data if they were made before this date"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_partner_category_ids
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_partner_category_ids
msgid ""
"The reconciliation model will only be applied to the selected "
"customer/vendor categories."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_partner_ids
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_partner_ids
msgid ""
"The reconciliation model will only be applied to the selected "
"customers/vendors."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_nature
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_nature
msgid ""
"The reconciliation model will only be applied to the selected transaction type:\n"
"        * Amount Received: Only applied when receiving an amount.\n"
"        * Amount Paid: Only applied when paying an amount.\n"
"        * Amount Paid/Received: Applied in both cases."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_partner
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_partner
msgid ""
"The reconciliation model will only be applied when a customer/vendor is set."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_amount
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_amount
msgid ""
"The reconciliation model will only be applied when the amount being lower "
"than, greater than or between specified amount(s)."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_label
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_label
msgid ""
"The reconciliation model will only be applied when the label:\n"
"        * Contains: The proposition label must contains this string (case insensitive).\n"
"        * Not Contains: Negation of \"Contains\".\n"
"        * Match Regex: Define your own regular expression."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_note
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_note
msgid ""
"The reconciliation model will only be applied when the note:\n"
"        * Contains: The proposition note must contains this string (case insensitive).\n"
"        * Not Contains: Negation of \"Contains\".\n"
"        * Match Regex: Define your own regular expression."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_transaction_type
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_transaction_type
msgid ""
"The reconciliation model will only be applied when the transaction type:\n"
"        * Contains: The proposition transaction type must contains this string (case insensitive).\n"
"        * Not Contains: Negation of \"Contains\".\n"
"        * Match Regex: Define your own regular expression."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_journal_ids
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_journal_ids
msgid ""
"The reconciliation model will only be available from the selected journals."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_tax_adjustments_wizard__tax_report_line_id
msgid "The report line to make an adjustment for."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__amount_residual_currency
msgid ""
"The residual amount on a journal item expressed in its currency (possibly "
"not the company currency)."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__amount_residual
msgid ""
"The residual amount on a journal item expressed in the company currency."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax__sequence
#: model:ir.model.fields,help:account.field_account_tax_template__sequence
msgid ""
"The sequence field is used to define order in which the tax lines are "
"applied."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_total_amount_param
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_total_amount_param
msgid ""
"The sum of total residual amount propositions matches the statement line "
"amount under this percentage."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__match_total_amount
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__match_total_amount
msgid ""
"The sum of total residual amount propositions matches the statement line "
"amount."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_account_tag__tax_report_line_ids
msgid "The tax report lines using this tag"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_repartition_line__invoice_tax_id
#: model:ir.model.fields,help:account.field_account_tax_repartition_line_template__invoice_tax_id
msgid ""
"The tax set to apply this repartition on invoices. Mutually exclusive with "
"refund_tax_id"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_tax_repartition_line__refund_tax_id
#: model:ir.model.fields,help:account.field_account_tax_repartition_line_template__refund_tax_id
msgid ""
"The tax set to apply this repartition on refund invoices. Mutually exclusive"
" with invoice_tax_id"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_cash_rounding__rounding_method
msgid "The tie-breaking rule used for float rounding operations"
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"The type of the journal's default credit/debit account shouldn't be "
"'receivable' or 'payable'."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_my_invoices
msgid "There are currently no invoices and payments for your account."
msgstr ""

#. module: account
#: code:addons/account/wizard/account_validate_account_move.py:0
#, python-format
msgid "There are no journal items in the draft state to post."
msgstr ""

#. module: account
#: code:addons/account/models/company.py:0
#, python-format
msgid ""
"There are still unposted entries in the period you want to lock. You should "
"either post or delete them."
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:0
#, python-format
msgid ""
"There is more than one receivable/payable account in the concerned invoices."
" You cannot group payments in that case."
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:0
#, python-format
msgid ""
"There is no Transfer Account defined in the accounting settings. Please "
"define one to be able to confirm this transfer."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__amount_from_label_regex
msgid ""
"There is no need for regex delimiter, only the regex is needed. For instance if you want to extract the amount from\n"
"R:9672938 10/07 AX ********** T:5L:NA BRT: 3358,07 C:\n"
"You could enter\n"
"BRT: ([\\d,]+)"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"There is no tax cash basis journal defined for this company: \"%s\" \n"
"Configure it in Accounting/Configuration/Settings"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "There is nothing to reconcile."
msgstr ""

#. module: account
#: code:addons/account/models/company.py:0
#, python-format
msgid ""
"There isn't any journal entry flagged for data inalterability yet for this "
"journal."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_error
msgid "There was an error processing this page."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "These taxes are set in any new product created."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template__user_type_id
msgid ""
"These types are defined according to your country. The type contains more "
"information about the account and its specificities."
msgstr ""

#. module: account
#: code:addons/account/models/account_journal_dashboard.py:0
#, python-format
msgid "This Week"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__property_account_payable_id
#: model:ir.model.fields,help:account.field_res_users__property_account_payable_id
msgid ""
"This account will be used instead of the default one as the payable account "
"for the current partner"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__property_account_receivable_id
#: model:ir.model.fields,help:account.field_res_users__property_account_receivable_id
msgid ""
"This account will be used instead of the default one as the receivable "
"account for the current partner"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_product_category__property_account_income_categ_id
msgid "This account will be used when validating a customer invoice."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "This action isn't available for this document."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"This allows accountants to manage analytic and crossovered budgets. Once the"
" master budgets and the budgets are defined, the project managers can set "
"the planned amount on each analytic account."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_config_settings__module_account_batch_payment
msgid ""
"This allows you grouping payments into a single batch and eases the reconciliation process.\n"
"-This installs the account_batch_payment module."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_chart_template__complete_tax_set
msgid ""
"This boolean helps you to choose if you want to propose to the user to "
"encode the sale and purchase rates or choose from list of taxes. This last "
"choice assumes that the set of tax defined on this template is complete"
msgstr ""

#. module: account
#: code:addons/account/wizard/account_accrual_accounting.py:0
#, python-format
msgid "This can only be used on journal items"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__refund_sequence_id
msgid ""
"This field contains the information related to the numbering of the credit "
"note entries of this journal."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__sequence_id
msgid ""
"This field contains the information related to the numbering of the journal "
"entries of this journal."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__journal_id
#: model:ir.model.fields,help:account.field_account_reconcile_model__second_journal_id
msgid "This field is ignored in a bank statement reconciliation."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__date_maturity
msgid ""
"This field is used for payable and receivable journal entries. You can put "
"the limit date for the payment of this line."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line__partner_name
msgid ""
"This field is used to record the third party name when importing bank "
"statement in electronic format, when the partner doesn't exist yet in the "
"database (or cannot be found)."
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.open_account_journal_dashboard_kanban
msgid "This is the accounting dashboard"
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"This journal already contains items, therefore you cannot modify its "
"company."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"This journal already contains items, therefore you cannot modify its short "
"name."
msgstr ""

#. module: account
#: code:addons/account/models/company.py:0
#, python-format
msgid "This journal is not in strict mode."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__to_check
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__to_check
msgid ""
"This matching rule is used when the user is not certain of all the "
"informations of the counterpart."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "This move is configured to be auto-posted on %s"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "This move is configured to be auto-posted on {}"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_account_template__chart_template_id
msgid ""
"This optional field allow you to link an account template to a specific "
"chart template that may differ from the one its root parent belongs to. This"
" allow you to define chart templates that extend another and complete it "
"with few new accounts (You don't need to define the whole structure that is "
"common to both several times)."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid ""
"This page displays all the bank transactions that are to be reconciled and "
"provides with a neat interface to do so."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid ""
"This parameter will be bypassed in case of a statement line communication "
"matching exactly existing entries"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "This payment is registered but not reconciled."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__property_supplier_payment_term_id
#: model:ir.model.fields,help:account.field_res_users__property_supplier_payment_term_id
msgid ""
"This payment term will be used instead of the default one for purchase "
"orders and vendor bills"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__property_payment_term_id
#: model:ir.model.fields,help:account.field_res_users__property_payment_term_id
msgid ""
"This payment term will be used instead of the default one for sales orders "
"and customer invoices"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line__account_id
msgid ""
"This technical field can be used at the statement line creation/import time "
"in order to avoid the reconciliation process on it later on. The statement "
"line will simply create a counterpart on this account"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_validate_account_move
msgid ""
"This wizard will validate all journal entries selected. Once journal entries"
" are validated, you can not update them anymore."
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_account_reconcile_model
msgid ""
"Those can be used to quickly create a journal items when reconciling\n"
"                a bank statement or an account."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid ""
"Those options will be selected by default when clicking \"Send &amp; Print\""
" on invoices"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: model:ir.model.fields,field_description:account.field_account_move__to_check
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__to_check
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__to_check
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
#, python-format
msgid "To Check"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "To Invoice"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "To reconcile the entries company should be the same for all entries."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "To speed up reconciliation, define"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Today Activities"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_cashbox__total
#: model:ir.model.fields,field_description:account.field_account_invoice_report__amount_total
#: model:ir.model.fields,field_description:account.field_account_move__amount_total
#: model:ir.model.fields,field_description:account.field_account_move_line__price_total
#: model_terms:ir.ui.view,arch_db:account.view_invoice_tree
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#: model_terms:ir.ui.view,arch_db:account.view_move_tree
msgid "Total"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_accrual_accounting_wizard__total_amount
#: model_terms:ir.ui.view,arch_db:account.view_move_tree
msgid "Total Amount"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_line_tax_audit_tree
msgid "Total Base Amount"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#: model_terms:ir.ui.view,arch_db:account.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:account.view_move_line_tree_grouped
msgid "Total Credit"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
#: model_terms:ir.ui.view,arch_db:account.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:account.view_move_line_tree_grouped
msgid "Total Debit"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__total_invoiced
#: model:ir.model.fields,field_description:account.field_res_users__total_invoiced
msgid "Total Invoiced"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__debit
#: model:ir.model.fields,field_description:account.field_res_users__debit
msgid "Total Payable"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__credit
#: model:ir.model.fields,field_description:account.field_res_users__credit
msgid "Total Receivable"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__amount_total_signed
msgid "Total Signed"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__credit
#: model:ir.model.fields,help:account.field_res_users__credit
msgid "Total amount this customer owes you."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_res_partner__debit
#: model:ir.model.fields,help:account.field_res_users__debit
msgid "Total amount you have to pay to this vendor."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__total_entry_encoding
msgid "Total of transaction lines."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Track costs &amp; revenues by project, department, etc"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Transaction"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement_line__transaction_type
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_transaction_type
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_transaction_type
msgid "Transaction Type"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__match_transaction_type_param
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__match_transaction_type_param
msgid "Transaction Type Parameter"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_form
msgid "Transactions"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__total_entry_encoding
msgid "Transactions Subtotal"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment__destination_journal_id
msgid "Transfer To"
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:0
#, python-format
msgid "Transfer from %s"
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:0
#, python-format
msgid "Transfer to %s"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Transfers"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__user_type_id
#: model:ir.model.fields,field_description:account.field_account_account_template__user_type_id
#: model:ir.model.fields,field_description:account.field_account_account_type__type
#: model:ir.model.fields,field_description:account.field_account_bank_statement__journal_type
#: model:ir.model.fields,field_description:account.field_account_invoice_report__type
#: model:ir.model.fields,field_description:account.field_account_invoice_send__message_type
#: model:ir.model.fields,field_description:account.field_account_journal__type
#: model:ir.model.fields,field_description:account.field_account_move__type
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__value
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__rule_type
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__rule_type
#: model:ir.model.fields,field_description:account.field_account_setup_bank_manual_config__acc_type
msgid "Type"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__type_name
msgid "Type Name"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__activity_exception_decoration
#: model:ir.model.fields,help:account.field_account_move__activity_exception_decoration
#: model:ir.model.fields,help:account.field_account_payment__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_cash_rounding__rounding_method__up
msgid "UP"
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"Unable to mix any taxes being price included with taxes affecting the base "
"amount but not included in price."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid "Undefined Yet"
msgstr ""

#. module: account
#: code:addons/account/models/company.py:0
#, python-format
msgid "Undistributed Profits/Losses"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_line__price_unit
msgid "Unit Price"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Unit Price:"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__product_uom_id
#: model:ir.model.fields,field_description:account.field_account_move_line__product_uom_id
msgid "Unit of Measure"
msgstr ""

#. module: account
#: code:addons/account/report/account_aged_partner_balance.py:0
#, python-format
msgid "Unknown Partner"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "Unpaid Invoices"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Unposted"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_filter
msgid "Unposted Journal Entries"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Unposted Journal Items"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_unread
#: model:ir.model.fields,field_description:account.field_account_journal__message_unread
#: model:ir.model.fields,field_description:account.field_account_move__message_unread
#: model:ir.model.fields,field_description:account.field_account_payment__message_unread
msgid "Unread Messages"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__message_unread_counter
#: model:ir.model.fields,field_description:account.field_account_journal__message_unread_counter
#: model:ir.model.fields,field_description:account.field_account_move__message_unread_counter
#: model:ir.model.fields,field_description:account.field_account_payment__message_unread_counter
msgid "Unread Messages Counter"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:0
#: model:ir.actions.act_window,name:account.action_account_unreconcile
#: model_terms:ir.ui.view,arch_db:account.account_unreconcile_view
#, python-format
msgid "Unreconcile"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_unreconcile_view
msgid "Unreconcile Transactions"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_move_line_filter
msgid "Unreconciled"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__amount_untaxed
msgid "Untaxed Amount"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__amount_untaxed_signed
msgid "Untaxed Amount Signed"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_report__price_subtotal
msgid "Untaxed Total"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "UoM"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Update exchange rates automatically"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/bills_tree_upload_views.xml:0
#, python-format
msgid "Upload"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_chart_template__use_anglo_saxon
msgid "Use Anglo-Saxon accounting"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__tax_exigibility
msgid "Use Cash Basis"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_sepa_direct_debit
msgid "Use SEPA Direct Debit"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move_reversal__journal_id
msgid "Use Specific Journal"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__use_active_domain
msgid "Use active domain"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_company__anglo_saxon_accounting
msgid "Use anglo-saxon accounting"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__module_account_batch_payment
msgid "Use batch payments"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Use budgets to compare actual with expected revenues and costs"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__template_id
msgid "Use template"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_account__used
msgid "Used"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_account_type__include_initial_balance
msgid ""
"Used in reports to know if we should consider journal items from the "
"beginning of time instead of from the fiscal year only. Account types that "
"should be reset to zero at each new fiscal year (like expenses, revenue..) "
"should not have this option set."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__reference
msgid ""
"Used to hold the reference of the external mean that created this statement "
"(name of imported file, reference of online synchronization...)"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__sequence
msgid "Used to order Journals in the dashboard view"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__loss_account_id
msgid ""
"Used to register a loss when the ending balance of a cash register differs "
"from what the system computes"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__profit_account_id
msgid ""
"Used to register a profit when the ending balance of a cash register differs"
" from what the system computes"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__user_id
msgid "User"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_res_users
msgid "Users"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement_line__journal_currency_id
#: model:ir.model.fields,help:account.field_account_move_line__company_currency_id
#: model:ir.model.fields,help:account.field_account_partial_reconcile__company_currency_id
#: model:ir.model.fields,help:account.field_res_partner__currency_id
#: model:ir.model.fields,help:account.field_res_users__currency_id
msgid "Utility field to express amount currency"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__vat_required
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__vat_required
msgid "VAT required"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_invoice_form
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_form
#, python-format
msgid "Validate"
msgstr ""

#. module: account
#: model:ir.model,name:account.model_validate_account_move
msgid "Validate Account Move"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_reconcile_model__auto_reconcile
#: model:ir.model.fields,help:account.field_account_reconcile_model_template__auto_reconcile
msgid ""
"Validate the statement line automatically (reconciliation based on your "
"rule)."
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_bank_statement__state__confirm
#: model:ir.model.fields.selection,name:account.selection__account_payment__state__posted
#: model:mail.message.subtype,name:account.mt_invoice_validated
msgid "Validated"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_payment_term_line__value_amount
msgid "Value"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_payment__partner_type__supplier
#: model_terms:ir.ui.view,arch_db:account.view_account_supplier_payment_tree
#: model_terms:ir.ui.view,arch_db:account.view_invoice_tree
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "Vendor"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_move__invoice_vendor_bill_id
#: model:ir.model.fields.selection,name:account.selection__account_invoice_report__type__in_invoice
#: model:ir.model.fields.selection,name:account.selection__account_move__type__in_invoice
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "Vendor Bill"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Vendor Bill Created"
msgstr ""

#. module: account
#: code:addons/account/models/chart_template.py:0
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
#, python-format
msgid "Vendor Bills"
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:0
#: model:ir.model.fields.selection,name:account.selection__account_invoice_report__type__in_refund
#: model:ir.model.fields.selection,name:account.selection__account_move__type__in_refund
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
#, python-format
msgid "Vendor Credit Note"
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:0
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#, python-format
msgid "Vendor Payment"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_partner__property_supplier_payment_term_id
#: model:ir.model.fields,field_description:account.field_res_users__property_supplier_payment_term_id
msgid "Vendor Payment Terms"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account.view_account_payment_search
msgid "Vendor Payments"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_product_product__supplier_taxes_id
#: model:ir.model.fields,field_description:account.field_product_template__supplier_taxes_id
msgid "Vendor Taxes"
msgstr ""

#. module: account
#: model:ir.actions.act_window,name:account.res_partner_action_supplier
#: model:ir.ui.menu,name:account.menu_account_supplier
#: model:ir.ui.menu,name:account.menu_finance_payables
#: model_terms:ir.ui.view,arch_db:account.res_partner_view_search
#: model_terms:ir.ui.view,arch_db:account.view_account_invoice_report_search
msgid "Vendors"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Verify"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:0
#, python-format
msgid "View"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_partner_property_form
msgid "View accounts detail"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__res_partner__invoice_warn__warning
msgid "Warning"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Warning for %s"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.partner_view_buttons
msgid "Warning on the Invoice"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.res_config_settings_view_form
msgid "Warnings"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_res_config_settings__group_warning_account
msgid "Warnings in Invoices"
msgstr ""

#. module: account
#: code:addons/account/models/company.py:0
#, python-format
msgid ""
"We cannot find a chart of accounts for this company, you should configure it. \n"
"Please go to Account Configuration and select or install a fiscal localization."
msgstr ""

#. module: account
#: code:addons/account/models/company.py:0
#, python-format
msgid ""
"We cannot find any journal for this company. You should create one.\n"
"Please go to Configuration > Journals."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_bank_statement__website_message_ids
#: model:ir.model.fields,field_description:account.field_account_journal__website_message_ids
#: model:ir.model.fields,field_description:account.field_account_move__website_message_ids
#: model:ir.model.fields,field_description:account.field_account_payment__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_bank_statement__website_message_ids
#: model:ir.model.fields,help:account.field_account_journal__website_message_ids
#: model:ir.model.fields,help:account.field_account_move__website_message_ids
#: model:ir.model.fields,help:account.field_account_payment__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_move_in_receipt_type
msgid ""
"When the purchase receipt is confirmed, you can record the\n"
"                vendor payment related to this purchase receipt."
msgstr ""

#. module: account
#: model_terms:ir.actions.act_window,help:account.action_move_out_receipt_type
msgid ""
"When the sale receipt is confirmed, you can record the customer\n"
"                payment related to this sales receipt."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_invoice_send__is_log
msgid "Whether the message is an internal note (comment mode only)"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__show_on_dashboard
msgid "Whether this journal should be displayed on the dashboard or not"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_setup_bank_manual_config__new_journal_name
msgid "Will be used to name the Journal related to this bank account"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_common_journal_report__amount_currency
#: model:ir.model.fields,field_description:account.field_account_print_journal__amount_currency
msgid "With Currency"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_search
msgid "With tax"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "Write-Off"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_reconcile_model__amount
#: model:ir.model.fields,field_description:account.field_account_reconcile_model_template__amount
msgid "Write-off Amount"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Writeoff Date"
msgstr ""

#. module: account
#: model:ir.model.constraint,message:account.constraint_account_move_line_check_credit_debit
msgid "Wrong credit or debit value in accounting entry !"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "You are trying to reconcile some entries that are already reconciled."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__blocked
msgid ""
"You can check this box to mark this journal item as a litigation with the "
"associated partner"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__invoice_reference_model
msgid ""
"You can choose different models for each type of reference. The default one "
"is the Odoo reference."
msgstr ""

#. module: account
#: code:addons/account/models/account_payment_term.py:0
#, python-format
msgid ""
"You can not delete payment terms as other records still reference it. "
"However, you can archive it."
msgstr ""

#. module: account
#: code:addons/account/models/account_fiscal_year.py:0
#, python-format
msgid ""
"You can not have an overlap between two fiscal years, please correct the "
"start and/or end dates of your fiscal years."
msgstr ""

#. module: account
#: code:addons/account/wizard/account_accrual_accounting.py:0
#, python-format
msgid "You can only change the period for items in these types of accounts: "
msgstr ""

#. module: account
#: code:addons/account/wizard/account_accrual_accounting.py:0
#, python-format
msgid "You can only change the period for items that are not yet reconciled."
msgstr ""

#. module: account
#: code:addons/account/wizard/account_accrual_accounting.py:0
#, python-format
msgid "You can only change the period for posted journal items."
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:0
#, python-format
msgid ""
"You can only register at the same time for payment that are all from the "
"same company"
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:0
#, python-format
msgid ""
"You can only register at the same time for payment that are all inbound or "
"all outbound"
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:0
#: code:addons/account/models/account_payment.py:0
#, python-format
msgid "You can only register payments for open invoices"
msgstr ""

#. module: account
#: code:addons/account/wizard/account_invoice_send.py:0
#, python-format
msgid "You can only send invoices."
msgstr ""

#. module: account
#: code:addons/account/wizard/account_payment_send.py:0
#, python-format
msgid "You can only send payments."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"You can only set an account having the payable type on payment terms lines "
"for vendor bill."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"You can only set an account having the receivable type on payment terms "
"lines for customer invoice."
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_journal__invoice_reference_type
msgid ""
"You can set here the default communication that will appear on customer "
"invoices, once validated, to help the customer to refer to that particular "
"invoice when making the payment."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"You can't change the company of your account since there are some journal "
"items linked to it."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"You can't change the company of your journal since there are some journal "
"entries linked to it."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"You can't change the company of your tax since there are some journal items "
"linked to it."
msgstr ""

#. module: account
#: code:addons/account/models/account_analytic_line.py:0
#, python-format
msgid ""
"You can't set a different company on your analytic account since there are "
"some journal items linked to it."
msgstr ""

#. module: account
#: code:addons/account/models/account_analytic_line.py:0
#, python-format
msgid ""
"You can't set a different company on your analytic tags since there are some"
" journal items linked to it."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"You cannot add/modify entries prior to and inclusive of the lock date %s."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"You cannot add/modify entries prior to and inclusive of the lock date %s. "
"Check the company settings or ask someone with the 'Adviser' role"
msgstr ""

#. module: account
#: code:addons/account/models/company.py:0
#, python-format
msgid ""
"You cannot change the currency of the company since some journal items "
"already exist"
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"You cannot change the owner company of an account that already contains "
"journal items."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"You cannot create a move already in the posted state. Please create a draft "
"move and post it after."
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:0
#, python-format
msgid "You cannot delete a payment that is already posted."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "You cannot delete an entry which has been posted once."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "You cannot delete an item linked to a posted entry."
msgstr ""

#. module: account
#: code:addons/account/models/res_config_settings.py:0
#, python-format
msgid ""
"You cannot disable this setting because some of your taxes are cash basis. "
"Modify your taxes first before disabling this setting."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"You cannot do this modification on a reconciled journal entry. You can just change some non legal fields or you must unreconcile first.\n"
"Journal Entry (id): %s (%s)"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"You cannot edit the following fields due to restrict mode being activated on"
" the journal: %s."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"You cannot edit the journal of an account move if it has been posted once."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"You cannot have a receivable/payable account that is not reconcilable. "
"(account code: %s)"
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"You cannot have more than one account with \"Current Year Earnings\" as "
"type. (accounts: %s)"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "You cannot modify a journal entry linked to a posted payment."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"You cannot modify a posted entry of this journal because it is in strict "
"mode."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"You cannot modify the field %s of a journal that already has accounting "
"entries."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"You cannot modify the taxes related to a posted journal item, you should "
"reset the journal entry to draft to do so."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"You cannot overwrite the values ensuring the inalterability of the "
"accounting."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"You cannot perform this action on an account that contains journal items."
msgstr ""

#. module: account
#: code:addons/account/wizard/pos_box.py:0
#, python-format
msgid "You cannot put/take money in/out for a bank statement which is closed."
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:0
#, python-format
msgid ""
"You cannot register payments for customer invoices and credit notes at the "
"same time."
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:0
#, python-format
msgid ""
"You cannot register payments for vendor bills and supplier refunds at the "
"same time."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid "You cannot remove the bank account from the journal once set."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"You cannot remove/deactivate an account which is set on a customer or "
"vendor."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "You cannot reset to draft a tax cash basis journal entry."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "You cannot reset to draft an exchange difference journal entry."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"You cannot set a currency on this account as it already has some journal "
"entries having a different foreign currency."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid ""
"You cannot switch an account to prevent the reconciliation if some partial "
"reconciliations are still pending."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "You cannot use a deprecated account."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "You cannot use taxes on lines with an Off-Balance account"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"You cannot use this general account in this journal, check the tab 'Entry "
"Controls' on the related journal."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"You cannot validate an invoice with a negative total amount. You should "
"create a credit note instead. Use the action menu to transform it into a "
"credit note or refund."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "You don't have the access rights to post an invoice."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "You have"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "You have suspense account moves that match this invoice."
msgstr ""

#. module: account
#: code:addons/account/models/account_payment.py:0
#, python-format
msgid "You have to define a sequence for %s in your company."
msgstr ""

#. module: account
#: code:addons/account/wizard/pos_box.py:0
#, python-format
msgid ""
"You have to define an 'Internal Transfer Account' in your cash register's "
"journal."
msgstr ""

#. module: account
#: code:addons/account/models/account.py:0
#, python-format
msgid "You must first define an opening move."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid "You need to add a line before posting."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "You reconciled"
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"You should configure the 'Exchange Rate Journal' in the accounting settings,"
" to manage automatically the booking of accounting entries related to "
"differences between exchange rates."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"You should configure the 'Gain Exchange Rate Account' in the accounting "
"settings, to manage automatically the booking of accounting entries related "
"to differences between exchange rates."
msgstr ""

#. module: account
#: code:addons/account/models/account_move.py:0
#, python-format
msgid ""
"You should configure the 'Loss Exchange Rate Account' in the accounting "
"settings, to manage automatically the booking of accounting entries related "
"to differences between exchange rates."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_position_form
msgid "Zip Range"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__zip_from
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__zip_from
msgid "Zip Range From"
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_fiscal_position__zip_to
#: model:ir.model.fields,field_description:account.field_account_fiscal_position_template__zip_to
msgid "Zip Range To"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "and follow-up customers"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_payment.xml:0
#, python-format
msgid "assign to invoice"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_error
#: model_terms:ir.ui.view,arch_db:account.portal_invoice_success
msgid "close"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_form
msgid "code"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_payment_term_line__option__day_after_invoice_date
msgid "days after the invoice date"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.setup_bank_account_wizard
msgid "e.g ****************"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.setup_bank_account_wizard
msgid "e.g Bank of America"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.setup_bank_account_wizard
msgid "e.g Checking account"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_reconcile_model_form
msgid "e.g. Bank Fees"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_account_journal_group_form
msgid "e.g. GAAP, IFRS, ..."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "for this customer. You can allocate them to mark this invoice as paid."
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "for this supplier. You can allocate them to mark this bill as paid."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "have been reconciled automatically."
msgstr ""

#. module: account
#: model:ir.model.fields,field_description:account.field_account_invoice_send__invoice_without_email
msgid "invoice(s) that will not be sent"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_tax_view_tree
msgid "name"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_tax_repartition_line__repartition_type__tax
#: model:ir.model.fields.selection,name:account.selection__account_tax_repartition_line_template__repartition_type__tax
msgid "of tax"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_payment_term_line__option__day_current_month
msgid "of the current month"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_payment_term_line__option__day_following_month
msgid "of the following month"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.report_invoice_document
msgid "on"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "outstanding debits"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "outstanding payments"
msgstr ""

#. module: account
#: model:ir.model.fields.selection,name:account.selection__account_invoice_report__invoice_payment_state__paid
msgid "paid"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "reconcile"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "reconciliation models"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "remaining)"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "seconds per transaction."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "statement lines"
msgstr ""

#. module: account
#: model:ir.model.fields,help:account.field_account_move_line__tax_group_id
msgid "technical field for widget tax-group-custom-field"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_partner_property_form
msgid "the parent company"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.account_journal_dashboard_kanban_view
msgid "to check"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_move_form
msgid "to mark this invoice as paid."
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "transactions in"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "unpaid invoices"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "unreconciled entries"
msgstr ""

#. module: account
#. openerp-web
#: code:addons/account/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "vendor bills"
msgstr ""

#. module: account
#: model_terms:ir.ui.view,arch_db:account.view_bank_statement_form
msgid "→ Count"
msgstr ""
