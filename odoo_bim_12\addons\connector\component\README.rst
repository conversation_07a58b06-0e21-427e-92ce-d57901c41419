==========
Components
==========

.. !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Production%2FStable-green.png
    :target: https://odoo-community.org/page/development-status
    :alt: Production/Stable
.. |badge2| image:: https://img.shields.io/badge/licence-LGPL--3-blue.png
    :target: http://www.gnu.org/licenses/lgpl-3.0-standalone.html
    :alt: License: LGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Fconnector-lightgray.png?logo=github
    :target: https://github.com/OCA/connector/tree/12.0/component
    :alt: OCA/connector
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/connector-12-0/connector-12-0-component
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runbot-Try%20me-875A7B.png
    :target: https://runbot.odoo-community.org/runbot/102/12.0
    :alt: Try me on Runbot

|badge1| |badge2| |badge3| |badge4| |badge5| 

This module implements a component system and is a base block for the Connector
Framework. It can be used without using the full Connector though.

Documentation: http://odoo-connector.com/

**Table of contents**

.. contents::
   :local:

Usage
=====

As a developer, you have access to a component system. You can find the
documentation in the code or on http://odoo-connector.com

In a nutshell, you can create components::


  from odoo.addons.component.core import Component

  class MagentoPartnerAdapter(Component):
      _name = 'magento.partner.adapter'
      _inherit = 'magento.adapter'

      _usage = 'backend.adapter'
      _collection = 'magento.backend'
      _apply_on = ['res.partner']

And later, find the component you need at runtime (dynamic dispatch at
component level)::

  def run(self, external_id):
      backend_adapter = self.component(usage='backend.adapter')
      external_data = backend_adapter.read(external_id)


Changelog
=========

.. [ The change log. The goal of this file is to help readers
    understand changes between version. The primary audience is
    end users and integrators. Purely technical changes such as
    code refactoring must not be mentioned here.
    
    This file may contain ONE level of section titles, underlined
    with the ~ (tilde) character. Other section markers are
    forbidden and will likely break the structure of the README.rst
    or other documents where this fragment is included. ]

Next
~~~~

12.0.1.0.0 (2018-10-02)
~~~~~~~~~~~~~~~~~~~~~~~

* [MIGRATION] from 11.0 branched at rev. 324e006

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/connector/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us smashing it by providing a detailed and welcomed
`feedback <https://github.com/OCA/connector/issues/new?body=module:%20component%0Aversion:%2012.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
~~~~~~~

* Camptocamp

Contributors
~~~~~~~~~~~~

* Guewen Baconnier <<EMAIL>>
* Laurent Mignon <<EMAIL>>

Maintainers
~~~~~~~~~~~

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

.. |maintainer-guewen| image:: https://github.com/guewen.png?size=40px
    :target: https://github.com/guewen
    :alt: guewen

Current `maintainer <https://odoo-community.org/page/maintainer-role>`__:

|maintainer-guewen| 

This module is part of the `OCA/connector <https://github.com/OCA/connector/tree/12.0/component>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
