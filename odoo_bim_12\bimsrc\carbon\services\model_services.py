from odoo.addons.base_rest import restapi
from odoo.addons.component.core import Component
from odoo.http import request
from odoo import http
import datetime
import time
import calendar
import logging
import os
import json
_logger = logging.getLogger(__name__)

def get_one_page_data(page, page_size , data):
    """

    :param self: 
    :param page_size: 每页数量
    :param page: 第几页
    :return: 第几页的数据
    """
    totalPages = int(len(data) / page_size) if len(data) % page_size == 0 else int(len(data) / page_size) + 1
    if page == totalPages:
        res = data[(page - 1) * page_size:]
    else:
        res = data[(page - 1) * page_size:page * page_size]
    return res,totalPages


def res_success(parent, data):
    """
    RESTAPI正确返回值
    """
    res = parent(partial=True, data=data)
    res.code = 0
    res.message = 'success'
    return res


class CarbonProjectServices(Component):
    _inherit = 'base.rest.service'
    _name = 'carbon.project'
    _usage = 'carbon_project'
    _collection = 'carbon.project.services'
    _description = ""


    # @restapi.method(
    # [
    #     (['/users/register'], 'POST')
    # ],
    # input_param=restapi.Datamodel("carbon.project.users.register.param"),
    # output_param=restapi.Datamodel("carbon.project.users.register.response"),auth='public')
    # def users_register(self, param):
    #     """
    #     自主注册
    #     """
    #     username = param.username
    #     password = param.password

    #     ResUsers = self.env['res.users'].sudo()
    #     IrModelData = self.env['ir.model.data'].sudo()

    #     user = ResUsers.search([('name','=',username)])
    #     if user:
    #         res = {
    #             'result': 'User Name Already Exists'
    #         }
    #     else:
    #         user = ResUsers.sudo().create({
    #             'name':username,
    #             'login':username,
    #             'password_text':password,
    #         })
    #         user.write({
    #             'password': password,
    #             'security_role_ids': [IrModelData.xmlid_to_res_id('carbon.role_manager'), IrModelData.xmlid_to_res_id('carbon.role_project_manager'), IrModelData.xmlid_to_res_id('carbon.role_database_manager')]
    #         })
    #         res = {
    #             'result': 'Register Success'
    #         }
            
    #     Parent = self.env.datamodels["carbon.project.users.register.response"]
    #     return res_success(Parent, res)

    # @restapi.method(
    # [
    #     (['/users/login'], 'POST')
    # ],
    # input_param=restapi.Datamodel("carbon.project.users.register.param"),
    # output_param=restapi.Datamodel("carbon.project.users.login.response"),auth='public')
    # def users_login(self, param):
    #     """
    #     登录
    #     """
    #     username = param.username
    #     password = param.password

    #     ResUsers = self.env['res.users'].sudo()
    #     LoginRecord = self.env['login.record'].sudo()

    #     user = ResUsers.search([('name','=',username)])
    #     if user:
    #         try:
    #             request.session.authenticate(request.db, user.login, password)
    #             request.httprequest.session.rotate = False
    #             # 获取客户端IP地址
    #             ip_address = request.httprequest.environ.get('REMOTE_ADDR')
                
    #             # 或者获取经过代理后的真实IP（如果有X-Forwarded-For头）
    #             if 'HTTP_X_FORWARDED_FOR' in request.httprequest.environ:
    #                 ip_address = request.httprequest.environ['HTTP_X_FORWARDED_FOR'].split(',')[0].strip()

    #             this_sid = request.httprequest.session.sid

    #             LoginRecord.create({
    #                 'user_id': user.id,
    #                 'sid': this_sid,
    #                 'login_ip': ip_address,
    #                 'type': 'password',
    #                 'login_time': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
    #             })

    #             #判断最大会话数量
    #             max_session = self.env["ir.config_parameter"].sudo().get_param("max.session")
    #             if max_session:
    #                 max_session = int(max_session)
    #             else:
    #                 max_session = 1
    #             #获取用户session
    #             records = LoginRecord.search([('user_id','=',user.id)])
    #             session_list = []
    #             for r in records:
    #                 _logger.info(r.login_time)
    #                 if r.sid and r.sid != this_sid:
    #                     session_list.append(r.sid)
    #             session_list = list(set(session_list))
    #             _logger.info(session_list)

    #             #筛选未过期的session,判断是否在data中的sessions目录，如：werkzeug_a341f6c9273894327d0dbb0c588ef6e957f580bb.sess
    #             # 获取 session_store
    #             active_session_list = []
    #             session_store = http.root.session_store
    #             _logger.info('self.session_store')
    #             _logger.info(session_store.path)
    #             # httprequest.session = self.session_store.get(sid)
    #             # last_week = time.time() - 60 * 60 * 24 * 7
    #             for fname in os.listdir(session_store.path):
    #                 path = os.path.join(session_store.path, fname)
    #                 _logger.info(path)
    #                 sid = path.split('werkzeug_')[1].split('.')[0]
    #                 if sid in session_list:
    #                     active_session_list.append(sid)
    #             _logger.info(active_session_list)

           

    #             #需删除sid的数量
    #             del_len = len(active_session_list)  + 1 -  max_session
    #             for i in range(0,del_len):
    #                 sid = active_session_list.pop()
    #                 path = f'{session_store.path}/werkzeug_{sid}.sess'
    #                 os.unlink(path)

    #                 # try:
    #                 #     if os.path.getmtime(path) < last_week:
    #                 #         os.unlink(path)
    #                 # except OSError:
    #                 #     pass


    #             res = {
    #                 'result': 'Login Success',
    #                 'session': this_sid
    #             }
    #         except:
    #             res = {
    #                 'result': 'Password Error'
    #             }
    #     else:
    #         res = {
    #             'result': 'User Name Does Not Exist'
    #         }

    #     Parent = self.env.datamodels["carbon.project.users.login.response"]
    #     return res_success(Parent, res)

    # @restapi.method(
    # [
    #     (['/users/password'], 'POST')
    # ],
    # input_param=restapi.Datamodel("carbon.project.users.password.param"),
    # output_param=restapi.Datamodel("carbon.project.users.password.response"),auth='user')
    # def users_password(self, param):
    #     """
    #     修改密码
    #     """
    #     user = request.env.user
    #     old_password = param.old_password
    #     new_password = param.new_password

    #     try:
    #         user.change_password(old_password, new_password)
    #         res = {
    #             'result': 'Changed Success'
    #         }
    #     except:
    #         res = {
    #             'result': 'Old Password Verification Error'
    #         }
            
    #     Parent = self.env.datamodels["carbon.project.users.password.response"]
    #     return res_success(Parent, res)

    @restapi.method(
    [
        (['/users/info'], 'GET'),
        (['/users/info'], 'PUT')
    ],
    input_param=restapi.Datamodel("carbon.project.users.info.param"),
    output_param=restapi.Datamodel("carbon.project.users.info.response"),auth='user')
    def users_info(self, param):
        """
        获取已登录用户基本信息(GET)
        修改已登录用户基本信息(PUT)
        """
        user = request.env.user
        method = request.httprequest.method
        ResUsers = self.env['res.users'].sudo()
            
        if method == 'GET':

            res = {
                'success': True,
                'data': {
                    'id': user.id,
                    'is_master': False if user.parent_id else True,
                    'name': user.name,
                    'vip_level': user.vip_level,
                    'expire_date': user.expire_date and user.expire_date.strftime('%Y-%m-%d') or '',
                    'phone': user.phone or '',
                    'email': user.email or '',
                }
            }

        if method == 'PUT':
            vals = param.vals
            _logger.info(vals)
            other_user = ResUsers.search([('phone','=',vals.get('phone')),('name','!=',vals.get('name'))])
            if other_user:
                res = {
                    'success': False,
                    'message': '该手机号已被其他用户绑定'
                }
            else:
                user.name = vals.get('name')
                user.phone = vals.get('phone')
                user.email = vals.get('email')
                res = {
                    'success': True,
                    'message': '修改成功'
                }

        Parent = self.env.datamodels["carbon.project.users.info.response"]

        return res_success(Parent, res)
    
    @restapi.method(
    [
        (['/users/roles'], 'GET')
    ],
    output_param=restapi.Datamodel("carbon.project.users.roles.response"),auth='user')
    def users_roles(self):
        """
        获取已登录用户所有角色
        """
        user = request.env.user

        res = [{
            'role_id': role.id,
            'role_name': role.name,
        } for role in user.security_role_ids]
        Parent = self.env.datamodels["carbon.project.users.roles.response"]

        return res_success(Parent, res)

    @restapi.method(
    [
        (['/roles'], 'GET')
    ],
    output_param=restapi.Datamodel("carbon.project.users.roles.response"),auth='public')
    def roles(self):
        """
        获取可选角色列表
        """
        # user = request.env.user

        # res = [{
        #     'role_id': role.id,
        #     'role_name': role.name,
        # } for role in user.security_role_ids]
        Parent = self.env.datamodels["carbon.project.users.roles.response"]

        return res_success(Parent, res)

    @restapi.method(
    [
        (['/users/projects'], 'GET'),
        (['/users/projects'], 'POST'),
        (['/users/projects'], 'PUT'),
        (['/users/projects'], 'DELETE')
    ],
    input_param=restapi.Datamodel("carbon.project.users.projects.param"),
    output_param=restapi.Datamodel("carbon.project.users.projects.response"),auth='user')
    def users_projects(self, param):
        """
        已登录用户获取项目(GET)
        已登录用户创建项目(POST)
        已登录用户修改项目(PUT)
        已登录用户删除项目(DELETE)
        """
        user = request.env.user
        method = request.httprequest.method
        
        CarbonStage = self.env['carbon.stage'].sudo()

        if method == 'GET':

            user_ids = [user.id]
            user_ids.extend(user.child_ids.ids)
            keyword = param.keyword
            curPage = param.curPage or 0
            pageSize = param.pageSize or 0
            totalPages = 0
            if keyword:
                projects = self.env['carbon.project'].search([('user_id','in',user_ids),('name','ilike',keyword)])
            else:
                projects = self.env['carbon.project'].search([('user_id','in',user_ids)])
            res_projects = projects
            if curPage and pageSize:
                res_projects, totalPages = get_one_page_data(curPage, pageSize, projects)
            res = {
                'success': True,
                'curPage': curPage,
                'pageSize': pageSize,
                'total': len(projects),
                'data':[{
                    'sequence': (curPage-1)*pageSize + idx + 1,
                    'id': project.id,
                    'name': project.name,
                    'location': project.city_id.name,
                    'city_id': [project.city_id.state_id.id, project.city_id.id],
                    'checked_stages': [s.name for s in project.stage_ids],
                    'fine_checked_stages': [s.name for s in project.fine_stage_ids],
                    'is_completed': project.is_completed,
                    'calc_stage': project.calc_stage,
                    'mode': project.mode,
                    'life': project.life,
                    'area': project.area,
                    'type': project.type,
                    'has_rough_scheme': len(project.scheme_ids.filtered(lambda x:x.mode == 'rough'))>0,
                    'has_fine_scheme': len(project.scheme_ids.filtered(lambda x:x.mode == 'fine')),
                    'fine_report_id': str(project.scheme_ids.filtered(lambda x:x.mode == 'fine').id) if project.scheme_ids.filtered(lambda x:x.mode == 'fine').id else '0',
                    'can_fine_report': project.scheme_ids.filtered(lambda x:x.mode == 'fine').is_completed,
                    'can_compare_report': len(project.scheme_ids.filtered(lambda x:x.mode == 'rough')) > 1 and all([sc.is_completed for sc in project.scheme_ids.filtered(lambda x:x.mode == 'rough')]) ,
                    # 'can_compare_fine_report': project.scheme_ids.filtered(lambda x:x.mode == 'fine').is_completed and project.scheme_ids.filtered(lambda x:x.mode == 'rough' and x.select == True).is_completed,
                    'schemes': [{
                        'sequence': f'{idx+1}-{idx2+1}',
                        'id': sc.id,
                        'name': sc.name,
                        'is_completed': sc.is_completed,
                        'res_all': '%.5f'% float(sc.res_all),
                        'res_area': '%.5f'% float(sc.res_area),
                        'res_year': '%.5f'% float(sc.res_year),
                        'res_area_year': '%.5f'% float(sc.res_area_year),
                        'select': sc.select,
                    } for idx2, sc in enumerate(project.scheme_ids.filtered(lambda x:x.mode == 'rough'))]
                } for idx, project in enumerate(res_projects)]
            }
        if method == 'POST':
            vals = param.vals
            stages = vals.pop('stages')
            stage_ids = [CarbonStage.search([('name', '=', i)],limit=1).id for i in stages]
            vals['stage_ids'] = [(6, 0, stage_ids)]
            vals['fine_stage_ids'] = [(6, 0, stage_ids)]
            vals['user_id'] = user.id
            _logger.info(vals)
            # project = self.env['carbon.project'].create(vals)
            try:
                project = self.env['carbon.project'].create(vals)
                res = {
                    'success': True,
                    'data': [{
                        'sequence': 1,
                        'id': project.id,
                        'name': project.name,
                        'location': project.city_id.name,
                        'city_id': [project.city_id.state_id.id, project.city_id.id],
                        'checked_stages': [s.name for s in project.stage_ids],
                        'fine_checked_stages': [s.name for s in project.fine_stage_ids],
                        'is_completed': project.is_completed,
                        'mode': project.mode,
                        'life': project.life,
                        'area': project.area,
                        'type': project.type
                    }]
                }
            except:
                res = {
                    'success': False
                }

        if method == 'PUT':
            # try:
                vals = param.vals
                project = self.env['carbon.project'].search([('id','=',vals.get('id'))])
                _logger.info(vals)
              
                if 'change' in vals.keys():
                    change = vals.pop('change')
                    if 'fine_stages' in vals.keys():
                        fine_stages = vals.pop('fine_stages')
                        if change.get('isfineCheckedStages'):
                            stage_ids = [CarbonStage.search([('name', '=', i)],limit=1).id for i in fine_stages]
                            _logger.info(stage_ids)
                            if stage_ids:
                                vals['fine_stage_ids'] = [(6, 0, stage_ids)]
                            _logger.info(vals)
                            # 删除核算计算结果
                            project.scheme_ids.filtered(lambda x:x.mode == 'fine').mapped('result_ids').unlink()
                    if 'rough_stages' in vals.keys():
                        rough_stages = vals.pop('rough_stages')
                        if change.get('ischeckedStages'):
                            stage_ids = [CarbonStage.search([('name', '=', i)],limit=1).id for i in rough_stages]
                            _logger.info(stage_ids)
                            if stage_ids:
                                vals['stage_ids'] = [(6, 0, stage_ids)]
                            _logger.info(vals)
                            # 删除估算计算结果
                            project.scheme_ids.filtered(lambda x:x.mode == 'rough').mapped('result_ids').unlink()
                    if change.get('isChangeLife'):
                        # 删除核算计算结果
                        project.scheme_ids.filtered(lambda x:x.mode == 'fine').mapped('result_ids').unlink()
                    if change.get('isChangeArea'):
                        # 删除核算计算结果、删除估算计算结果
                        project.scheme_ids.mapped('result_ids').unlink()

               
                if 'schemeData' in vals.keys():
                    schemeData = vals.pop('schemeData')
                    _logger.info(schemeData)
                    if schemeData:
                        _logger.info('wwwwwwwww')
                        sc_id = schemeData.get('id')
                        if sc_id:
                            sc = self.env['carbon.project.scheme'].search([('id','=',sc_id)])
                            sc.data = json.dumps(schemeData.get('data'), ensure_ascii=False)
                            sc.name = schemeData.get('name')
                            sc.mode = schemeData.get('mode')
                        else:
                            sc = self.env['carbon.project.scheme'].create({
                                'project_id': project.id,
                                'data': json.dumps(schemeData.get('data'), ensure_ascii=False),
                                'name': schemeData.get('name'),
                                'mode': schemeData.get('mode')
                            })
                
                if 'fineSchemeData' in vals.keys():
                    fineSchemeData = vals.pop('fineSchemeData')
                    if fineSchemeData:
                        sc = self.env['carbon.project.scheme'].search([('project_id','=',project.id),('mode','=','fine')],limit=1)
                        if sc:
                            sc.data = fineSchemeData
                        else:
                            sc = self.env['carbon.project.scheme'].create({
                                'project_id': project.id,
                                'data': fineSchemeData,
                                'mode': 'fine'
                            })

                project.write(vals)
                res = {
                    'success': True,
                    'data': [{
                        'sequence': 1,
                        'id': project.id,
                        'name': project.name,
                        'location': project.city_id.name,
                        'city_id': [project.city_id.state_id.id, project.city_id.id],
                        'checked_stages': [s.name for s in project.stage_ids],
                        'fine_checked_stages': [s.name for s in project.fine_stage_ids],
                        'is_completed': project.is_completed,
                        'mode': project.mode,
                        'life': project.life,
                        'area': project.area,
                        'type': project.type
                    }]
                }
            # except:
            #     res = {
            #         'success': False
            #     }

        if method == 'DELETE':
            try:
                vals = param.vals
                project = self.env['carbon.project'].search([('id','=',vals.get('id'))])
                project.unlink()
                res = {
                    'success': True
                }
            except:
                res = {
                    'success': False
                }


        Parent = self.env.datamodels["carbon.project.users.projects.response"]

        return res_success(Parent, res)

    @restapi.method(
    [
        (['/users/projects/<int:project_id>/schemes'], 'PUT'),
        (['/users/projects/<int:project_id>/schemes'], 'DELETE')
    ],
    input_param=restapi.Datamodel("carbon.project.users.projects.id.schemes.param"),
    output_param=restapi.Datamodel("carbon.project.users.projects.id.schemes.response"),auth='user')
    def users_projects_id_schemes(self, project_id, param):
        """
        已登录用户修改项目方案(PUT)
        已登录用户删除项目方案(DELETE)
        """
        _logger.info(param)
        method = request.httprequest.method

        CarbonProject = self.env['carbon.project']
        CarbonProjectScheme = self.env['carbon.project.scheme']
        project = CarbonProject.browse(project_id)
        if method == 'PUT':
            scheme = CarbonProjectScheme.browse(param.id)
            schemes = project.scheme_ids.filtered(lambda x:x.mode == 'rough')
            if param.select:
                scheme.select = True
                (schemes - scheme).select = False
            res = {
                'success': True
            }
        if method == 'DELETE':
            scheme = CarbonProjectScheme.browse(param.id)
            scheme.unlink()
            res = {
                'success': True
            }

        Parent = self.env.datamodels["carbon.project.users.projects.id.schemes.response"]
        return res_success(Parent, res)

    
    @restapi.method(
    [
        (['/users/projects/<int:id>/result'], 'GET'),
        (['/users/projects/<int:id>/result'], 'POST')
    ],
    input_param=restapi.Datamodel("carbon.project.users.projects.id.result.param"),
    output_param=restapi.Datamodel("carbon.project.users.projects.id.result.response"),auth='user')
    def users_projects_id_result(self, id, param):
        """
        已登录用户获取项目计算结果(GET)
        已登录用户计算项目(POST)
        """
        user = request.env.user
        method = request.httprequest.method

        

        CarbonStage = self.env['carbon.stage']
        CarbonProject = self.env['carbon.project']
        CarbonProjectScheme = self.env['carbon.project.scheme']
        CarbonProjectResult = self.env['carbon.project.result']

        def deal_float(res):
            if 0< abs(float(res)) <= 0.00001:
                return '0'
            if 0.00001< abs(float(res)) <= 999:
                return '%.5f'% float(res)
            if 999< abs(float(res)) <= 9999:
                return '%.4f'% float(res)
            if 9999< abs(float(res)) <= 99999:
                return '%.2f'% float(res)
            return '%.0f'% float(res)
            # return '%.0f'% float(res) if float(res)>1000000 else '%.5f'% float(res)

        if method == 'GET':
            scheme = CarbonProjectScheme.browse(id)
            project = scheme.project_id
            scheme_result_ids = scheme.result_ids.sorted(lambda x:x.stage_id.sequence)

            stage_result = {}
            stage_category = {}
            for r in scheme_result_ids:
                stage_category[r.stage_id.name] = json.loads(r.category_result) if r.category_result else []
                stage_result[r.stage_id.id] = {
                    'res_all': {
                        'label': '碳排放量',
                        'unit': 'tCO₂e',
                        'value': deal_float(r.res_all),
                    },
                    'res_area': {
                        'label': '单位面积碳排放强度',
                        'unit': 'kgCO₂e/m²',
                        'value': deal_float(r.res_area),
                    },
                    'res_year': {
                        'label': '平均每年碳排放强度',
                        'unit': 'tCO₂e/a',
                        'value': deal_float(r.res_year),
                    },
                    'res_area_year': {
                        'label': '单位面积年均碳排放强度',
                        'unit': 'kgCO₂e/(a·m²)',
                        'value': deal_float(r.res_area_year),
                    }
                }

            res = {
                'success': True,
                'project_info': {
                    'name': project.name,
                    'life': project.life if scheme.mode == 'fine' else json.loads(scheme.data).get('A-Year'),
                    'location': project.city_id.name,
                    'area': project.area,
                    'mode': project.mode,
                    'scheme_mode': scheme.mode,
                },
                'res_all': {
                    'value': deal_float(scheme.res_all),
                    'label': '碳排放量',
                    'unit': 'tCO₂e',
                    'stage_result': [{
                        'stage_id': r.stage_id.id,
                        'stage_name': r.stage_id.name,
                        'label': r.stage_id.name,
                        'unit': 'tCO₂e',
                        'value': deal_float(r.res_all),
                    } for r in scheme_result_ids],
                },
                'res_area': {
                    'value':  deal_float(scheme.res_area),
                    'label': '单位面积碳排放强度',
                    'unit': 'kgCO₂e/m²',
                    'stage_result': [{
                        'stage_id': r.stage_id.id,
                        'stage_name': r.stage_id.name,
                        'label': r.stage_id.name,
                        'unit': 'kgCO₂e/m²',
                        'value': deal_float(r.res_area),
                    } for r in scheme_result_ids],
                },
                'res_year': {
                    'value': deal_float(scheme.res_year),
                    'label': '平均每年碳排放强度',
                    'unit': 'tCO₂e/a',
                    'stage_result': [{
                        'stage_id': r.stage_id.id,
                        'stage_name': r.stage_id.name,
                        'label': r.stage_id.name,
                        'unit': 'tCO₂e/a',
                        'value': deal_float(r.res_year),
                    } for r in scheme_result_ids],
                },
                'res_area_year': {
                    'value': deal_float(scheme.res_area_year),
                    'label': '单位面积年均碳排放强度',
                    'unit': 'kgCO₂e/(a·m²)',
                    'stage_result': [{
                        'stage_id': r.stage_id.id,
                        'stage_name': r.stage_id.name,
                        'label': r.stage_id.name,
                        'unit': 'kgCO₂e/(a·m²)',
                        'value': deal_float(r.res_area_year),
                    } for r in scheme_result_ids],
                },
                'stage_result':stage_result,
                'stage_category':stage_category
            }
        if method == 'POST':
            project = CarbonProject.browse(id)
            mode = project.mode
            scheme_id = param.scheme_id
            _logger.info(scheme_id)
            scheme = CarbonProjectScheme.browse(scheme_id)
            if scheme.mode == 'fine':
                if scheme.data:
                    data = json.loads(scheme.data)
                    stage_id = param.stage_id
                    if stage_id: #计算一个阶段
                        result = CarbonProjectResult.search([('scheme_id','=',scheme_id),('stage_id','=',stage_id)])
                        if not result:
                            result = CarbonProjectResult.create({
                                'scheme_id':scheme_id,
                                'stage_id':stage_id,
                            })

                        stage = CarbonStage.browse(stage_id)
                        model_dic = {
                            '原材料': 'material',
                            '运输': 'mechanical',
                            '施工': 'mechanical',
                            '养护': 'maintenance',
                            '拆除': 'mechanical',
                            '碳汇': 'carbon',
                        }
                        model = f'{model_dic.get(stage.name)}.life.cycle.inventory'

               
                    
                        sum_list = []
                        category_list = {

                        }
                        for d in data.get(str(stage_id)):
                            factor_number = float(d.get('factor_number'))
                            number = float(d.get('number'))
                            category = d.get('category')
                            
                            s = factor_number*number
                            if stage.name == '运输':
                                qualityFactor = float(d.get('qualityFactor'))
                                distance = float(d.get('distance'))
                                s = factor_number*number*qualityFactor*distance
                            if stage.name == '碳汇' and category == '绿化':
                                year = float(d.get('year'))
                                s = factor_number*number*year


                            sum_list.append(s)

                            
                            if category not in category_list.keys():
                                category_list[category] = [s]
                            else:
                                category_list[category].append(s)
                        
                        
                            

                        
                                
                        A_Year = float(project.life)
                        A_Area = float(project.area)

         
                        
                  
                        res_all = sum(sum_list)/1000
                        res_area = sum(sum_list)/A_Area
                        res_year = sum(sum_list)/(A_Year * 1000)
                        res_area_year = sum(sum_list)/(A_Year * A_Area)
                        
                        category_result = []
                        for k,v in category_list.items():
                            category_result.append({
                                'category': k,
                                'res_all': sum(v)/1000,
                            })
                        # _logger.info(res_all)
                     
                        result.res_all = res_all
                        result.res_area = res_area
                        result.res_year = res_year
                        result.res_area_year = res_area_year
                        result.category_result = json.dumps(category_result, ensure_ascii=False)
                    
                    else:
                        # 此处需要改stage_ids
                        #计算所有阶段
                        for stage_id in project.fine_stage_ids.ids:
                            result = CarbonProjectResult.search([('scheme_id','=',scheme_id),('stage_id','=',stage_id)])
                            if not result:
                                result = CarbonProjectResult.create({
                                    'scheme_id':scheme_id,
                                    'stage_id':stage_id,
                                })

                            stage = CarbonStage.browse(stage_id)
                            model_dic = {
                                '原材料': 'material',
                                '运输': 'mechanical',
                                '施工': 'mechanical',
                                '养护': 'maintenance',
                                '拆除': 'mechanical',
                                '碳汇': 'carbon',
                            }
                            model = f'{model_dic.get(stage.name)}.life.cycle.inventory'

                
                        
                            sum_list = []
                            category_list = {

                            }
                            for d in data.get(str(stage_id)):
                                factor_number = float(d.get('factor_number'))
                                number = float(d.get('number'))
                                category = d.get('category')
                                
                                s = factor_number*number
                                if stage.name == '运输':
                                    qualityFactor = float(d.get('qualityFactor'))
                                    distance = float(d.get('distance'))
                                    s = factor_number*number*qualityFactor*distance
                                if stage.name == '碳汇' and category == '绿化':
                                    year = float(d.get('year'))
                                    s = factor_number*number*year


                                sum_list.append(s)

                                
                                if category not in category_list.keys():
                                    category_list[category] = [s]
                                else:
                                    category_list[category].append(s)
                            
                            
                                

                            
                                    
                            A_Year = float(project.life)
                            A_Area = float(project.area)

            
                            
                    
                            res_all = sum(sum_list)/1000
                            res_area = sum(sum_list)/A_Area
                            res_year = sum(sum_list)/(A_Year * 1000)
                            res_area_year = sum(sum_list)/(A_Year * A_Area)
                            
                            category_result = []
                            for k,v in category_list.items():
                                category_result.append({
                                    'category': k,
                                    'res_all': sum(v)/1000,
                                })
                            # _logger.info(res_all)
                        
                            result.res_all = res_all
                            result.res_area = res_area
                            result.res_year = res_year
                            result.res_area_year = res_area_year
                            result.category_result = json.dumps(category_result, ensure_ascii=False)
                        
                    # project.com_res()

            if scheme.mode == 'rough':
                scheme.calc_rough()

            res = {
                        'success': True
                    }


        Parent = self.env.datamodels["carbon.project.users.projects.id.result.response"]

        return res_success(Parent, res)


    @restapi.method(
    [
        (['/users/projects/<int:id>'], 'GET')
    ],
    output_param=restapi.Datamodel("carbon.project.users.projects.id.response"),auth='user')
    def users_projects_id(self, id):
        """
        获取项目详情
        """
        project = self.env['carbon.project'].search([('id','=',id)])
        index_dic = {
            '原材料': '0',
            '运输': '1',
            '施工': '2',
            '养护': '3',
            '拆除': '4',
            '碳汇': '5',
        }
        # 此处需要改stage_ids
        res = {
            'project_info':{
                'id': project.id,
                'name': project.name,
                'life': project.life or '',
                'city_id': [project.city_id.state_id.id, project.city_id.id],
                'has_rough_scheme': len(project.scheme_ids.filtered(lambda x:x.mode == 'rough'))>0,
                'location': project.city_id.name,
                'area': project.area
            },
            'inventory_id':project.inventory_id.id,
            'inventory_data':{
                'name': project.inventory_id.name or '',
                '0': [{
                    'id': typex.id,
                    'name': typex.name,
                    'inventories': [{
                        'id': inv.id,
                        'name': inv.name,
                        'unit':inv.unit_id.name.split('/')[1],
                        'carbon_factor': inv.carbon_factor,
                        'carbon_unit': inv.unit_id.name,
                    } for inv in self.env['material.life.cycle.inventory'].search([('type_id','=',typex.id)])]
                } for typex in self.env['life.cycle.inventory.type'].search([('category','=','material'),('inventory_id','=',project.inventory_id.id)])],
                '1': [{
                    'id': typex.id,
                    'name': typex.name,
                    'inventories': [{
                        'id': inv.id,
                        'name': inv.name,
                        'unit':inv.unit_id.name.split('/')[1],
                        'carbon_factor': inv.carbon_factor,
                        'carbon_unit': inv.unit_id.name,
                    } for inv in self.env['mechanical.life.cycle.inventory'].search([('type_id','=',typex.id)])]
                } for typex in self.env['life.cycle.inventory.type'].search([('category','=','ys_mechanical'),('inventory_id','=',project.inventory_id.id)])],
                '2': [{
                    'id': typex.id,
                    'name': typex.name,
                    'inventories': [{
                        'id': inv.id,
                        'name': inv.name,
                        'unit':inv.unit_id.name.split('/')[1],
                        'carbon_factor': inv.carbon_factor,
                        'carbon_unit': inv.unit_id.name,
                    } for inv in self.env['mechanical.life.cycle.inventory'].search([('type_id','=',typex.id)])]
                } for typex in self.env['life.cycle.inventory.type'].search([('category','=','sg_mechanical'),('inventory_id','=',project.inventory_id.id)])],
                '3': [{
                    'id': typex.id,
                    'name': typex.name,
                    'inventories': [{
                        'id': inv.id,
                        'name': inv.name,
                        'unit':inv.unit_id.name.split('/')[1],
                        'carbon_factor': inv.carbon_factor,
                        'carbon_unit': inv.unit_id.name,
                    } for inv in self.env['maintenance.life.cycle.inventory'].search([('type_id','=',typex.id)])]
                } for typex in self.env['life.cycle.inventory.type'].search([('category','=','maintenance'),('inventory_id','=',project.inventory_id.id)])],
                '4': [{
                    'id': typex.id,
                    'name': typex.name,
                    'inventories': [{
                        'id': inv.id,
                        'name': inv.name,
                        'unit':inv.unit_id.name.split('/')[1],
                        'carbon_factor': inv.carbon_factor,
                        'carbon_unit': inv.unit_id.name,
                    } for inv in self.env['mechanical.life.cycle.inventory'].search([('type_id','=',typex.id)])]
                } for typex in self.env['life.cycle.inventory.type'].search([('category','=','cc_mechanical'),('inventory_id','=',project.inventory_id.id)])],
                '5': [{
                    'id': typex.id,
                    'name': typex.name,
                    'inventories': [{
                        'id': inv.id,
                        'name': inv.name,
                        'unit':inv.unit_id.name.split('/')[1],
                        'carbon_factor': inv.carbon_factor,
                        'carbon_unit': inv.unit_id.name,
                    } for inv in self.env['carbon.life.cycle.inventory'].search([('type_id','=',typex.id)])]
                } for typex in self.env['life.cycle.inventory.type'].search([('category','=','carbon'),('inventory_id','=',project.inventory_id.id)])],
            },
            'data':json.loads(project.data) if project.data else None,
            'schemes':[{
                'id': sc.id,
                'name': sc.name,
                'mode': sc.mode,
                'data': json.loads(sc.data) if sc.data else None,
            } for sc in project.scheme_ids],
            'stages':[{
                'id': s.id,
                'name': s.name
            } for s in sorted(project.stage_ids, key=lambda x:x.sequence)],
            'fine_stages':[{
                'id': s.id,
                'name': s.name,
                'index': index_dic.get(s.name, '')
            } for s in sorted(project.fine_stage_ids, key=lambda x:x.sequence)]
        }

        Parent = self.env.datamodels["carbon.project.users.projects.id.response"]

        return res_success(Parent, res)


    @restapi.method(
    [
        (['/users/inventories'], 'GET'),
        (['/users/inventories'], 'POST'),
        (['/users/inventories'], 'PUT'),
        (['/users/inventories'], 'DELETE')
    ],
    input_param=restapi.Datamodel("carbon.project.users.inventories.param"),
    output_param=restapi.Datamodel("carbon.project.users.inventories.response"),auth='user')
    def users_inventories(self, param):
        """
        获取已登录用户所有清单(GET)
        已登录用户创建清单(POST)
        已登录用户修改清单(PUT)
        已登录用户删除清单(DELETE)
        """
        user = request.env.user
        method = request.httprequest.method

        if method == 'GET':
            keyword = param.keyword
            curPage = param.curPage or 0
            pageSize = param.pageSize or 0
            totalPages = 0
            if keyword:
                inventories = self.env['life.cycle.inventory'].search([('user_id','=',user.parent_id.id or user.id),('name','ilike',keyword)])
            else:
                inventories = self.env['life.cycle.inventory'].search([('user_id','=',user.parent_id.id or user.id)])
            res_inventories = inventories
            if curPage and pageSize:
                res_inventories, totalPages = get_one_page_data(curPage, pageSize, inventories)
            res = {
                'success': True,
                'curPage': curPage,
                'pageSize': pageSize,
                'total': len(inventories),
                'data':[{
                    'sequence': idx + 1,
                    'inventory_id': inventory.id,
                    'inventory_name': inventory.name,
                    'remark': inventory.remark or '',
                    'is_active': inventory.is_active,
                } for idx, inventory in enumerate(res_inventories)]
            }

        if method == 'POST':
            inventory_name = param.inventory_name
            remark = param.remark

            try:
                inventory_objs = self.env['life.cycle.inventory'].search([('user_id', '=', user.parent_id.id or user.id),('name','=',inventory_name)])
                if len(inventory_objs):
                    res = {
                        'success': False,
                        'message': '该清单名称已存在,请修改名称'
                    }
                else:
                    inventory = self.env['life.cycle.inventory'].create({
                        'user_id': user.parent_id.id or user.id,
                        'name': inventory_name,
                        'remark': remark
                    })
                    res = {
                        'success': True
                    }
            except:
                res = {
                    'success': False
                }
        if method == 'PUT':
            inventory_id = param.inventory_id
            inventory_name = param.inventory_name
            remark = param.remark
            is_active = param.is_active
            _logger.info(remark)

            try:
                inventory_objs = self.env['life.cycle.inventory'].search([('user_id', '=', user.parent_id.id or user.id),('name','=',inventory_name)])
                if len(inventory_objs):
                    res = {
                        'success': False,
                        'message': '该清单名称已存在,请修改名称'
                    }
                else:
                    inventory = self.env['life.cycle.inventory'].search([('id','=',inventory_id)])
                    if inventory_name:
                        inventory.name = inventory_name
                    if remark:
                        inventory.remark = remark
                    if is_active:
                        inventory.is_active = True
                        inventories = self.env['life.cycle.inventory'].search([('user_id','=',user.parent_id.id or user.id)])
                    
                        (inventories - inventory).is_active = False
                    res = {
                        'success': True
                    }
            except:
                res = {
                    'success': False
                }
        if method == 'DELETE':
            del_all = param.del_all
            inventory_id = param.inventory_id

            try:
                if del_all:
                    inventory = self.env['life.cycle.inventory'].search([('user_id','=',user.parent_id.id or user.id)])
                else:
                    inventory = self.env['life.cycle.inventory'].search([('id','=',inventory_id)])
                inventory.unlink()
                res = {
                    'success': True
                }
            except:
                res = {
                    'success': False
                }


        Parent = self.env.datamodels["carbon.project.users.inventories.response"]

        return res_success(Parent, res)



    @restapi.method(
    [
        (['/users/inventories/details'], 'GET'),
        (['/users/inventories/details'], 'POST'),
        (['/users/inventories/details'], 'PUT'),
        (['/users/inventories/details'], 'DELETE')
    ],
    input_param=restapi.Datamodel("carbon.project.users.inventories.details.param"),
    output_param=restapi.Datamodel("carbon.project.users.inventories.details.response"),auth='user')
    def users_inventories_details(self, param):
        """
        已登录用户获取清单详情(GET)
        已登录用户创建清单详情(POST)
        已登录用户修改清单详情(PUT)
        已登录用户删除清单详情(DELETE)
        """
        method = request.httprequest.method
        user = request.env.user

        parent_id = param.parent_id
        vals = param.vals
        type = param.type
        
        model = f'{type}.life.cycle.inventory'
        if method == 'GET':
            inventory = self.env['life.cycle.inventory'].search([('user_id','=',user.parent_id.id or user.id),('id','=',parent_id)])
            if type in ['material', 'maintenance', 'carbon']:
                inventories = self.env[model].search([('inventory_id','=',parent_id), ('type_id.category','=',type)])
            else:
                inventories = self.env['mechanical.life.cycle.inventory'].search([('inventory_id','=',parent_id), ('type_id.category','=',type)])
            details = []
            for idx, i in enumerate(inventories):
                _d = {
                        'sequence': idx + 1,
                        'id': i.id,
                        'name': i.name,
                        'category': i.type_id.name,
                        'category_id': i.type_id.id,
                        'carbon_factor': i.carbon_factor,
                        'carbon_unit': i.unit_id.name,
                        'unit_id': i.unit_id.id,
                }
                if type == 'material':
                    _d['composition_id'] = i.composition_id.id
                if type == 'carbon':
                    _d['material_id'] = i.material_id.id
                if type == 'maintenance':
                    _d['remark'] = i.remark
                details.append(_d)
            res = {
                'success': True,
                'data':{
                    'inventory_id': inventory.id,
                    'inventory_name': inventory.name,
                    'details': details
                }
            }
        if method == 'POST':
            try:
                vals['inventory_id'] = parent_id
                if type in ['material', 'maintenance', 'carbon']:
                    inventory = self.env[model].create(vals)
                else:
                    inventory = self.env['mechanical.life.cycle.inventory'].create(vals)
                res = {
                    'success': True
                }
            except:
                res = {
                    'success': False
                }
        
        if method == 'PUT':
            try:
                if type in ['material', 'maintenance', 'carbon']:
                    inventory = self.env[model].search([('id','=',vals.get('id'))])
                else:
                    inventory = self.env['mechanical.life.cycle.inventory'].search([('id','=',vals.get('id'))])
                inventory.write(vals)
                res = {
                    'success': True
                }
            except:
                res = {
                    'success': False
                }

        if method == 'DELETE':
            del_all = param.del_all
            try:
                if del_all:
                    if type in ['material', 'maintenance', 'carbon']:
                        inventory = self.env[model].search([('inventory_id','=',parent_id)])
                    else:
                        inventory = self.env['mechanical.life.cycle.inventory'].search([('inventory_id','=',parent_id)])
                else:
                    if type in ['material', 'maintenance', 'carbon']:
                        inventory = self.env[model].search([('id','in',vals.get('ids'))])
                    else:
                        inventory = self.env['mechanical.life.cycle.inventory'].search([('id','in',vals.get('ids'))])
                inventory.unlink()
                res = {
                    'success': True
                }
            except:
                res = {
                    'success': False
                }


        Parent = self.env.datamodels["carbon.project.users.inventories.details.response"]

        return res_success(Parent, res)




    @restapi.method(
    [
        (['/units'], 'GET')
    ],
    input_param=restapi.Datamodel("carbon.project.units.param"),
    output_param=restapi.Datamodel("carbon.project.units.response"),auth='public')
    def units(self, param):
        """
        获取单位
        """
        type = param.type

        LifeCycleInventoryType = self.env['life.cycle.inventory.type'].sudo()
        # CarbonUnit = self.env['carbon.unit'].sudo()

        types = LifeCycleInventoryType.search([('category','=',type)])
        # domian = [('type','=',type)] if type else []
        # units = CarbonUnit.search(domian)
        res = [{
            'name':ty.name,
            'id':ty.id,
            'units': [{
                'id':unit.id,
                'name':unit.name,
            } for unit in ty.unit_ids]
        } for ty in types]
            
        Parent = self.env.datamodels["carbon.project.units.response"]
        return res_success(Parent, res)


    @restapi.method(
    [
        (['/stages'], 'GET')
    ],
    output_param=restapi.Datamodel("carbon.project.stages.response"),auth='public')
    def stages(self):
        """
        获取阶段
        """

        CarbonStage = self.env['carbon.stage'].sudo()

        stages = CarbonStage.search([], order="sequence")
        res = [{
            'id':stage.id,
            'name':stage.name,
        } for stage in stages]
            
        Parent = self.env.datamodels["carbon.project.stages.response"]
        return res_success(Parent, res)


    @restapi.method(
    [
        (['/stages/<int:id>/inventories'], 'GET')
    ],
    output_param=restapi.Datamodel("carbon.project.stages.id.inventories.response"),auth='user')
    def stages_id_inventories(self, id):
        """
        获取阶段对应可选的清单
        """
        user = request.env.user

        CarbonStage = self.env['carbon.stage'].sudo()
        LifeCycleInventory = self.env['life.cycle.inventory'].sudo()
        MaterialLifeCycleInventory = self.env['material.life.cycle.inventory'].sudo()
        MechanicalLifeCycleInventory = self.env['mechanical.life.cycle.inventory'].sudo()
        MaintenanceLifeCycleInventory = self.env['maintenance.life.cycle.inventory'].sudo()

        stage = CarbonStage.browse(id)

        active_inventory = LifeCycleInventory.search([('user_id','=',user.parent_id.id or user.id),('is_active','=',True)],limit=1)
        domain = [('inventory_id','=',active_inventory.id)]
        if stage.name == '原材料':
            inventories = MaterialLifeCycleInventory.search(domain)
        elif stage.name == '养护':
            inventories = MaintenanceLifeCycleInventory.search(domain)
        else:
            inventories = MechanicalLifeCycleInventory.search(domain)
        res = [{
            'id':i.id,
            'name':i.name + i.remark if stage.name == '养护' else i.name,
            'carbon_factor':i.carbon_factor,
            'carbon_unit':i.unit_id.name,
            'unit':i.unit_id.name.split('/')[1],
        } for i in inventories]
            
        Parent = self.env.datamodels["carbon.project.stages.id.inventories.response"]
        return res_success(Parent, res)


    @restapi.method(
    [
        (['/citys'], 'GET')
    ],
    output_param=restapi.Datamodel("carbon.project.citys.response"),auth='public')
    def citys(self):
        """
        获取城市
        """

        ResCountryState = self.env['res.country.state'].sudo()
        ResCountryStateCity = self.env['res.country.state.city'].sudo()

        states = ResCountryState.search([('country_id.code', '=', 'CN')])
        citys = ResCountryStateCity.search([])
        res = [{
            'value':s.id,
            'label':s.name,
            'children': [{
                'value':c.id,
                'label':c.name,
            } for c in s.city_ids],
        } for s in states]
            
        Parent = self.env.datamodels["carbon.project.citys.response"]
        return res_success(Parent, res)

    @restapi.method(
    [
        (['/compositions'], 'GET')
    ],
    output_param=restapi.Datamodel("carbon.project.compositions.response"),auth='public')
    def compositions(self):
        """
        获取所有结构层成分
        """

        StructuralLayerComposition = self.env['structural.layer.composition'].sudo()

        compositions = StructuralLayerComposition.search([])
        res = [{
            'id':c.id,
            'name':f"{c.name}({'、'.join(c.layer_ids.mapped('name'))})",
        } for c in compositions]
            
        Parent = self.env.datamodels["carbon.project.compositions.response"]
        return res_success(Parent, res)

    @restapi.method(
    [
        (['/layers'], 'GET')
    ],
    output_param=restapi.Datamodel("carbon.project.layers.response"),auth='public')
    def layers(self):
        """
        获取结构层
        """

        StructuralLayer = self.env['structural.layer'].sudo()

        layers = StructuralLayer.search([])
        res = [{
            'id':layer.id,
            'name':layer.name,
        } for layer in layers]
            
        Parent = self.env.datamodels["carbon.project.layers.response"]
        return res_success(Parent, res)
    
    
    @restapi.method(
    [
        (['/layers/<int:id>/compositions'], 'GET')
    ],
    input_param=restapi.Datamodel("carbon.project.layers.id.compositions.param"),
    output_param=restapi.Datamodel("carbon.project.layers.id.compositions.response"),auth='public')
    def layers_id_compositions(self, id, param):
        """
        获取结构层的成分配置
        """
        inventory_id = param.inventory_id

        StructuralLayerComposition = self.env['structural.layer.composition'].sudo()
        MaterialLifeCycleInventory = self.env['material.life.cycle.inventory'].sudo()

        compositions = StructuralLayerComposition.search([('layer_ids', 'in', id)])
        res = []
        for composition in compositions:
            _d = {
                'id':composition.id,
                'name':composition.name,
                'type':composition.type,
                'unit':composition.unit or '',
            }
            if composition.type == 'checkbox':
                _d['max_length'] = composition.max_length
                if composition.columns:
                    columns = json.loads(composition.columns)
                    for col in columns:
                        if col.get('type') == 'select':
                            inventories = MaterialLifeCycleInventory.search([('inventory_id','=', inventory_id), ('composition_id','=', composition.id)])
                            col['options'] = [{
                                'value': i.id,
                                'label': i.name,
                            } for i in inventories]
                        if col.get('type') == 'input':
                            if 'value_k_name' in col.keys() and 'value_v_list' in col.keys() and 'value_kv_dic' in col.keys():
                                v = {}
                                inventories = MaterialLifeCycleInventory.search([('inventory_id','=', inventory_id), ('composition_id','=', composition.id)])
                                for idx,i in enumerate(inventories):
                                    # v[i.id] = col['value_v_list'][idx] 取消单位配置
                                    v[i.id] = {
                                        'kgCO₂e/L': 'L/m²',
                                        'kgCO₂e/m³': 'cm'
                                    }[i.unit_id.name] #采用动态映射获取
                                col['value_kv_dic'] = {
                                    'k_name':col['value_k_name'],
                                    'v_name':col['name'],
                                    'kv_dic':v
                                }
                    _d['columns'] = columns
                else:
                    _d['columns'] = []
            if composition.type == 'radio':
                inventories = MaterialLifeCycleInventory.search([('inventory_id','=', inventory_id), ('composition_id','=', composition.id)])
                _d['options'] = [{
                    'value': i.id,
                    'label': i.name,
                } for i in inventories]
                if composition.name == '混合料':
                    _d['options'] = [{
                        'value': 'AC',
                        'label': 'AC',
                    },{
                        'value': 'SMA',
                        'label': 'SMA',
                    },{
                        'value': 'EA-30',
                        'label': 'EA-30',
                    },{
                        'value': 'EA-40',
                        'label': 'EA-40',
                    },{
                        'value': 'GA',
                        'label': 'GA',
                    }]
            res.append(_d)

            
        Parent = self.env.datamodels["carbon.project.layers.id.compositions.response"]
        return res_success(Parent, res)


    @restapi.method(
    [
        (['/geojson'], 'GET')
    ],
    input_param=restapi.Datamodel("carbon.project.geojson.param"),
    output_param=restapi.Datamodel("carbon.project.geojson.response"),auth='public')
    def geojson(self, param):
        """
        获取geojson
        """
        type = param.type
        adcode = param.adcode
        if type == 'country':
            country = self.env['res.country'].sudo().search([('code','=','CN')],limit=1)
            res = {
                'geojson': country.geo_json
            }
        else:
            country = self.env['res.country.state'].sudo().search([('code','=',adcode)],limit=1)
            res = {
                'geojson': country.geo_json
            }
        Parent = self.env.datamodels["carbon.project.geojson.response"]
        return res_success(Parent, res)

    @restapi.method(
    [
        (['/users/childs'], 'GET'),
        (['/users/childs'], 'POST'),
        (['/users/childs'], 'PUT'),
        (['/users/childs'], 'DELETE')
    ],
    input_param=restapi.Datamodel("carbon.project.users.childs.param"),
    output_param=restapi.Datamodel("carbon.project.users.childs.response"),auth='user')
    def users_childs(self, param):
        """
        获取子账号(GET)
        新增子账号(POST)
        修改子账号(PUT)
        删除子账号(DELETE)
        """
        method = request.httprequest.method
        user = request.env.user

        if method == 'GET':
            children = [
                {
                    'label': '数据库管理人员',
                    'name': 'zww'
                },
                {
                    'label': '项目执行人员1',
                    'name': 'zwww'
                },
                {
                    'label': '项目执行人员2',
                    'name': '测试子账号'
                }
                ]
            children = []
            idx = 2
            database_manager_id = 0
            # database_manager = '未分配'
            database_manager = user.name
            has_database_manager = False
            for child in user.child_ids:
                role_names = child.security_role_ids.mapped('name')
                if '数据库管理人员' in role_names:
                    database_manager_id = child.id
                    database_manager = child.name
                    has_database_manager = True
                if '项目执行人员' in role_names:
                    children.append({
                            'label': f'项目执行人员{idx}',
                            'name': child.name
                    })
                    idx += 1
            children.insert(0, {
                    'label': '数据库管理人员',
                    'name': database_manager
            })
            children.insert(1, {
                    'label': '项目执行人员1',
                    'name': user.name
            })
            # _logger.info(children)
            org_data = {
                'label': '管理人员',
                'children': children
            }

            is_master = False if user.parent_id else True
            master_name = user.name if is_master else user.parent_id.name

            child_users = user.child_ids if is_master else user

            res = {
                'success': True,
                'database_manager_id': database_manager_id,
                'database_manager': database_manager,
                'has_database_manager': has_database_manager,
                'org_data': org_data,
                'is_master': is_master,
                'master_name': master_name,
                'data': [{
                    'id':child.id,
                    'name':child.name,
                    'password':child.password_text or '',
                    'roles': [{
                        'id': r.id,
                        'name': r.name,
                    } for r in child.security_role_ids]
                } for child in child_users]
            }
        if method == 'POST':
            vals = param.vals
            
            username = vals.get('username')
            password = vals.get('password')
            roleList = vals.get('roleList')

            

            ResUsers = self.env['res.users'].sudo()
            IrModelData = self.env['ir.model.data'].sudo()

            new_user = ResUsers.search([('name','=',username)])
            if new_user:
                res = {
                    'success': False,
                    'result': '用户名已存在'
                }
            else:
                new_user = ResUsers.create({
                    'name':username,
                    'login':username,
                    'password_text':password,
                    'parent_id':user.id,
                })
                security_role_ids = []
                if '项目执行人员' in roleList:
                    security_role_ids.append(IrModelData.xmlid_to_res_id('carbon.role_project_manager'))
                if '数据库管理人员' in roleList:
                    security_role_ids.append(IrModelData.xmlid_to_res_id('carbon.role_database_manager'))
                new_user.write({
                    'password': password,
                    'security_role_ids': security_role_ids
                })
                res = {
                    'success': True,
                    'result': '创建成功'
                }
        if method == 'PUT':
            try:
                ResUsers = self.env['res.users'].sudo()
                IrModelData = self.env['ir.model.data'].sudo()

                vals = param.vals
                
                id = vals.get('id')
                username = vals.get('username')
                password = vals.get('password')
                roleList = vals.get('roleList')

                security_role_ids = []
                if '项目执行人员' in roleList:
                    security_role_ids.append(IrModelData.xmlid_to_res_id('carbon.role_project_manager'))
                if '数据库管理人员' in roleList:
                    security_role_ids.append(IrModelData.xmlid_to_res_id('carbon.role_database_manager'))

                ResUsers.browse(id).write({
                    'name':username,
                    'login':username,
                    'parent_id':user.id,
                    'password':password,
                    'password_text':password,
                    'security_role_ids':security_role_ids,
                })
                res = {
                        'success': True,
                        'result': '修改成功'
                }
            except:
                res = {
                        'success': False,
                        'result': '用户名已存在'
                }

        if method == 'DELETE':
            try:
                vals = param.vals
                ids = vals.get('ids')
                ResUsers = self.env['res.users'].sudo()
                ResUsers.browse(ids).unlink()
                res = {
                    'success': True
                }
            except:
                res = {
                    'success': False
                }

        Parent = self.env.datamodels["carbon.project.users.childs.response"]
        return res_success(Parent, res)

    @restapi.method(
    [
        (['/users/projects/ranking'], 'GET')
    ],
    input_param=restapi.Datamodel("carbon.project.users.projects.ranking.param"),
    output_param=restapi.Datamodel("carbon.project.users.projects.ranking.response"),auth='user')
    def users_projects_ranking(self, param):
        """
        获取碳排放排行
        """
        user = request.env.user
        # res_all = fields.Char(string='总碳排放')
        # res_area = fields.Char(string='单位面积碳排放强度')
        # res_year = fields.Char(string='平均每年碳排放强度')
        # res_area_year = fields.Char(string='单位面积年均碳排放强度')
        type = param.type
        user_id = param.user_id

        if user_id:
            user = self.env['res.users'].sudo().browse(user_id)


        user_ids = [user.id]
        user_ids.extend(user.child_ids.ids)
        projects = self.env['carbon.project'].search([('user_id','in',user_ids)]).filtered(lambda x: x.is_completed)
        projects.com_res()
        projects = sorted(projects, key=lambda x:float(x[type]), reverse=True)

        if len(projects) >= 5:
            projects = projects[0:5]

        res = {
            'x_data': [p.name for p in projects],
            'y_data': ['%.2f' % float(p[type]) for p in projects],
        }

        Parent = self.env.datamodels["carbon.project.users.projects.ranking.response"]
        return res_success(Parent, res)

    @restapi.method(
    [
        (['/users/projects/overview'], 'GET')
    ],
    input_param=restapi.Datamodel("carbon.project.users.projects.overview.param"),
    output_param=restapi.Datamodel("carbon.project.users.projects.overview.response"),auth='user')
    def users_projects_overview(self, param):
        """
        获取项目概览
        """
        user = request.env.user

        user_id = param.user_id

        if user_id:
            user = self.env['res.users'].sudo().browse(user_id)


        user_ids = [user.id]
        user_ids.extend(user.child_ids.ids)

        projects = self.env['carbon.project'].search([('user_id','in',user_ids)])
        completed_projects = projects.filtered(lambda x:x.is_completed)

        res = {
            'total': len(projects),
            'completed_number': len(completed_projects),
            'city_projects': [{
                'id':c.id,
                'name':c.name,
                'adcode':c.code,
                'geojson':c.geo_json,
                'projects':[{
                    'id':p.id,
                    'name':p.name,
                    'username':p.user_id.name,
                    'location':p.city_id.name,
                    'life':p.life,
                    'area':p.area,
                    'geojson':p.city_id.geo_json,
                    'active_scheme_id':p.active_scheme_id,
                    'show_detail_btn':False
                } for p in projects.filtered(lambda x:x.city_id.id == c.id)],
            } for c in projects.mapped('city_id')],
            'completed_projects': [{
                'id':p.id,
                'name':p.name,
                'username':p.user_id.name,
                'location':p.city_id.name,
                'life':p.life,
                'area':p.area,
                'geojson':p.city_id.geo_json,
                'active_scheme_id':p.active_scheme_id,
                'show_detail_btn':False
            } for p in completed_projects],
        }

        Parent = self.env.datamodels["carbon.project.users.projects.overview.response"]
        return res_success(Parent, res)
    

    @restapi.method(
    [
        (['/verifycode'], 'POST')
    ],
    input_param=restapi.Datamodel("carbon.project.verifycode.param"),
    output_param=restapi.Datamodel("carbon.project.verifycode.response"),auth='public')
    def verifycode(self, param):
        """
        获取单位
        """
        
            
        Parent = self.env.datamodels["carbon.project.verifycode.response"]
        return res_success(Parent, res)
