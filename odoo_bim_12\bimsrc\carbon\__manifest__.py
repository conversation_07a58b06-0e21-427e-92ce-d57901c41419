# -*- coding: utf-8 -*-
{
    'name': "碳计算模块",

    'summary': """
       碳计算模块
       """,

    'description': """
         碳计算模块
    """,
    'sequence': 300,

    'author': "周伟",
    'website': "",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/master/openerp/addons/base/module/module_data.xml
    # for the full list
    'category': 'Base Application',
    'version': '0.1',

    # any module necessary for this one to work correctly
    'depends': ['base','web','base_rest', 'base_rest_datamodel','security_user_roles'],

    # always loaded
    'data': [
        'reports/report_fine.xml',
        'reports/report_fine_compare.xml',
        'reports/report_rough.xml',
        'reports/report_rough_compare.xml',
        'views/res_country_state_city_district.xml',
        'views/res_country_state_city.xml',
        'views/res_country_state_extend.xml',
        'views/structural_layer.xml',
        'views/structural_layer_composition.xml',
        'views/life_cycle_inventory.xml',
        'views/life_cycle_inventory_type.xml',
        'views/material_life_cycle_inventory.xml',
        'views/mechanical_life_cycle_inventory.xml',
        'views/maintenance_life_cycle_inventory.xml',
        'views/carbon_life_cycle_inventory.xml',
        'views/carbon_project.xml',
        'views/carbon_project_scheme.xml',
        'views/carbon_project_result.xml',
        'views/carbon_unit.xml',
        'views/carbon_stage.xml',
        'views/res_users.xml',
        'views/security_role_data.xml',
        'views/menu.xml',
        'views/carbon_crons.xml',
        'views/res_groups_data.xml',
        'security/ir.model.access.csv',
        # 'security/security.xml',
        ],
    # only loaded in demonstration mode
    'demo': [
        # 'demo/demo.xml',
    ],
    'qweb': [],
    'installable': True,
    'application': True,
    'auto_install': False,

}