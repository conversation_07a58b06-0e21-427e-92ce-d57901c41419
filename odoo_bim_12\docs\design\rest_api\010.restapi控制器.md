# RESTAPI控制器

## 创建控制器

基于`base_rest`模块开发REST接口，所有的控制器必须继承自`RestController`， 如下示例：

```python
# -*- coding: utf-8 -*-
from odoo.addons.base_rest.controllers import main


class PingController(main.RestController):
    _root_path = '/api/v2/'
    _collection_name = 'ping.services'
```

控制器中必须定义：

- `_root_path`: 该控制器对应的URL根路径
- `_collection_name`: 该控制器对应的服务集合名称

## 控制器信息

`RestController`控制器的元类为`RestControllerType`:

```python
class RestController(Controller, metaclass=RestControllerType):
    ...
```

在`RestControllerType`的初始化函数中，它在`_rest_controllers_per_module`中注册了该控制器的信息，包括：

- 控制器对应的`root_path`
- 控制器对应的服务集合名称`collection_name`
- 控制器类

```python
    ...
    _rest_controllers_per_module[cls._module].append(
                {
                    "root_path": root_path,
                    "collection_name": collection_name,
                    "controller_class": cls,
                }
            )
    ...
```

这个信息将在系统初始化服务注册中心时使用，根据这个信息来动态创建Odoo的`http.Controller`。

## RESTAPI调用流程

前提条件：

- 系统启动
- 系统安装`base_rest`模块

在系统创建服务注册中心时，已经为系统中所有的`RestController`动态创建了对应Odoo的`http.Controller`。

所以当Odoo收到一个RESTAPI调用请求：

1. Odoo根据路由找到对应的`http.Controller`，也即根据`RestController`动态生成的`http.Controller`
2. 调用对应`http.Controller`的方法，也即在`rest_service_registration.py`中通过模板`METHOD_TMPL`/`METHOD_TMPL_WITH_ARGS`创建的方法
3. 调用`_process_method`进行处理

```python
    ...

    def _process_method(self, service_name, method_name, *args, params=None):
        self._validate_method_name(method_name)
        with self.service_component(service_name) as service:
            result = service.dispatch(method_name, *args, params=params)
            return self.make_response(result)

    ...
```

4. 根据`_collection_name`找到服务，然后调用`service.dispatch`方法，拿到返回的结果后调用`make_response`返回结果。在`service.dispatch`中：
    - 完成输入参数的校验和输入对象的生成
    - 调用服务对应的方法
    - 取出返回对象，生成返回结果
