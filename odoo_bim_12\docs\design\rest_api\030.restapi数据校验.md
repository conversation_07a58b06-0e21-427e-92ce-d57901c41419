# restapi数据校验

在开发REST API时，常见的一类需求就是：

- 校验输入、输出数据的合法性
  - 是否缺失必填字段
  - 字段类型是否错误
  - 字段是否超出范围
- 面向对象编程：将输入、输出数据抽象为对象模型

在Odoo `rest-framework`中采用了`Datamodel`来解决以上两类问题，并且抽取出公共的类和方法，以便所有的REST API开发能够复用。

## `input_param`和`output_param`

使用`@restapi.method`装饰器，可以设置`input_param`和`output_param`，这两个参数设置有两个作用：

- 将参数/返回结果转换为对象，这样在代码实现的时候获取数据比较便捷
- 校验

```python
...

class PingService(Component):
    ...

    @restapi.method(
        [
            (['/'], 'GET'),
        ],
        input_param=restapi.Datamodel("ping.message.param"),
        output_param=restapi.Datamodel("ping.message.response"),
        auth='public')
    def pong(self, ping_message_param):
        PingMessageResponse = self.env.datamodels["ping.message.response"]
        ping_message_response = PingMessageResponse(partial=True)
        ping_message_response.message = f"{ping_message_param.message}"

        return ping_message_response
```

## 定义数据模型

`input_param`和`output_param`被设置为某个数据模型，通过将该数据模型的名称传递给`restapi.Datamodel()`找到对应的数据模型类。

自定义数据模型：

- 继承自`Datamodel`
- `fields`的定义来自于`marshmallow`包

```python
from marshmallow import fields

from odoo.addons.datamodel.core import Datamodel


class PingMessageParam(Datamodel):
    _name = "ping.message.param"

    message = fields.String(required=True, allow_none=False)


class PingMessageResponse(Datamodel):
    _name = "ping.message.response"

    message = fields.String(required=True, allow_none=False)

```

和Odoo的模型定义一样，我们可以通过`_name`以及`_inherit`来实现继承/扩展：

- 同样的`_name`，通过`_inherit`来指明继承，实际上实现的是扩展原有模型
- 不指定`_inherit`，创建新的模型
- 指定`_inherit`，但是不同的`_name`，创建新模型并继承了原模型的内容

## Marshmallow

`datamodel`模块内部基于[marshmallow](https://marshmallow.readthedocs.io/en/stable/quickstart.html)实现数据的教研以及对象序列化和反序列化。
