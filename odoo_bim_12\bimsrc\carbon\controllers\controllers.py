# -*- coding: utf-8 -*-
# from docxtpl import Docx<PERSON><PERSON>plate
from pyvirtualdisplay import Display
display = Display()
display.start()
# from weasyprint import HTML

from odoo.addons.base_rest.controllers import main
from odoo import http
from odoo.http import request, content_disposition
import requests
import os
import logging
import json
# import pdfkit
# from pydocx import PyDocX
_logger = logging.getLogger(__name__)



class CarbonController(http.Controller):
    def report_routes(self, reportname, docids=None, converter=None, **data):
        report = request.env['ir.actions.report']._get_report_from_name(reportname)
        context = dict(request.env.context)

        if docids:
            docids = [int(i) for i in docids.split(',')]
        if data.get('options'):
            data.update(json.loads(data.pop('options')))
        if data.get('context'):
            # Ignore 'lang' here, because the context in data is the one from the webclient *but* if
            # the user explicitely wants to change the lang, this mechanism overwrites it.
            data['context'] = json.loads(data['context'])
            if data['context'].get('lang'):
                del data['context']['lang']
            context.update(data['context'])
        if converter == 'html':
            html = report.with_context(context).render_qweb_html(docids, data=data)[0]
            return request.make_response(html)
        elif converter == 'pdf':
            pdf = report.with_context(context).render_qweb_pdf(docids, data=data)[0]
            pdfhttpheaders = [('Content-Type', 'application/pdf'), ('Content-Length', len(pdf))]
            return request.make_response(pdf, headers=pdfhttpheaders)
        elif converter == 'text':
            text = report.with_context(context).render_qweb_text(docids, data=data)[0]
            texthttpheaders = [('Content-Type', 'text/plain'), ('Content-Length', len(text))]
            return request.make_response(text, headers=texthttpheaders)
        else:
            raise werkzeug.exceptions.HTTPException(description='Converter %s not implemented.' % converter)
            
    @http.route('/download_report', type='http', auth="public")
    def download_url(self, **kwargs):
        id = kwargs.get('id')
        mode = kwargs.get('mode')
        data = f"""
        ["/report/pdf/carbon.project_report_{mode}/{id}","qweb-pdf"]
        """
        token = 'dummy-because-api-expects-one'
        context = {}
        requestcontent = json.loads(data)
        url, type = requestcontent[0], requestcontent[1]
       
        if type in ['qweb-pdf', 'qweb-text']:
            converter = 'pdf' if type == 'qweb-pdf' else 'text'
            extension = 'pdf' if type == 'qweb-pdf' else 'txt'

            pattern = '/report/pdf/' if type == 'qweb-pdf' else '/report/text/'
            reportname = url.split(pattern)[1].split('?')[0]

            docids = None
            if '/' in reportname:
                reportname, docids = reportname.split('/')

            if docids:
                # Generic report:
                response = self.report_routes(reportname, docids=docids, converter=converter, context=context)
            else:
                # Particular report:
                data = dict(url_decode(url.split('?')[1]).items())  # decoding the args represented in JSON
                if 'context' in data:
                    context, data_context = json.loads(context or '{}'), json.loads(data.pop('context'))
                    context = json.dumps({**context, **data_context})
                response = self.report_routes(reportname, converter=converter, context=context, **data)

            report = request.env['ir.actions.report']._get_report_from_name(reportname)
            filename = "%s.%s" % (report.name, extension)

            if docids:
                ids = [int(x) for x in docids.split(",")]
                obj = request.env[report.model].browse(ids)
                if report.print_report_name and not len(obj) > 1:
                    report_name = safe_eval(report.print_report_name, {'object': obj, 'time': time})
                    filename = "%s.%s" % (report_name, extension)
            response.headers.add('Content-Disposition', content_disposition(filename))
            response.set_cookie('fileToken', token)
            return response
        else:
            return
        # except Exception as e:
        #     se = _serialize_exception(e)
        #     error = {
        #         'code': 200,
        #         'message': "Odoo Server Error",
        #         'data': se
        #     }
        #     return request.make_response(html_escape(json.dumps(error)))
        # project = request.env['carbon.project'].sudo().browse(8)
        # stage_result = []
        # res_all_list = []
        # res_area_list = []
        # res_year_list = []
        # res_area_year_list = []
        # for result in project.result_ids:
        #     stage_result.append({
        #         'stage_id': result.stage_id.id,
        #         'stage_name': result.stage_id.name,
        #         'res_all': result.res_all,
        #         'res_area': result.res_area,
        #         'res_year': result.res_year,
        #         'res_area_year': result.res_area_year,
        #     })
        #     res_all_list.append(float(result.res_all))
        #     res_area_list.append(float(result.res_area))
        #     res_year_list.append(float(result.res_year))
        #     res_year_list.append(float(result.res_year))
        #     res_area_year_list.append(float(result.res_year))

        # path = os.path.dirname(os.path.abspath(__file__))
        # _logger.info(path)
        # tpl = DocxTemplate(path + os.sep + 'test.docx')
        # context = {
        #     'res_all': str(sum(res_all_list)),
        #     'res_area': str(sum(res_area_list)),
        #     'res_year': str(sum(res_year_list)),
        #     'res_area_year': str(sum(res_area_year_list)),
        # }
        # tpl.render(context)
        # tpl.save(path + os.sep + 'test1.docx')


        # f = open(path + os.sep + 'test1.docx','rb+')
        # filename = f'{project.name}计算结果报告.docx'

        # html = PyDocX.to_html(path + os.sep + 'test1.docx')
        # f2 = open(path + os.sep + "test1.html", 'w', encoding="utf-8")
        # f2.write(html)
        # path_wkthmltopdf = '/usr/bin/wkhtmltopdf'
        # options = {
        # }
        # config = pdfkit.configuration(wkhtmltopdf=path_wkthmltopdf,**options)
        # _logger.info(config)
        # pdfkit.from_string(html, path + os.sep + 'test1.pdf', configuration=config)


        # f = open(path + os.sep + 'test1.pdf','rb+')
        # filename = f'{project.name}计算结果报告.pdf'

        # response = request.make_response(
        #     None,
        #     headers=[
        #         ('Content-Disposition', content_disposition(filename))
        #     ]
        # )
        # response.stream.write(f.read())

       

        # return response



class modelController(main.RestController):
    _root_path = '/api/carbon/v2/'
    _collection_name = 'carbon.project.services'
