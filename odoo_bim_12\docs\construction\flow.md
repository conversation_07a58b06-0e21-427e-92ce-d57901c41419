# 1. 首页流程

## 1.1 获取概况信息

以下流程图展示首页调用后端接口获取概况信息的过程，接口调用不分顺序：

```mermaid
sequenceDiagram
    前端 ->> 后端: 1.GET /projects/weather 获取天气
    后端 ->> 前端: 返回数据
    前端 ->> 后端: 2.GET /projects/overview 获取工程概况
    后端 ->> 前端: 返回数据
    前端 ->> 后端: 3.GET /projects/progress/overview 获取工程进度完成情况
    后端 ->> 前端: 返回数据
    前端 ->> 后端: 4.GET /models/overview 获取模型概况信息
    后端 ->> 前端: 返回数据
    前端 ->> 后端: 5.GET /projects/engineering_monitor/overview 获取工程监测信息
    后端 ->> 前端: 返回数据
    前端 ->> 后端: 6.GET /projects/investment/overview 获取成本计量信息
    后端 ->> 前端: 返回数据
```

## 1.2 获取飞行和驾驶数据

### 1.2.1 获取飞行路线数据

```mermaid
sequenceDiagram
    前端 ->> 后端: 1.GET /scenes/flights/flying 获取所有飞行路线
    后端 ->> 前端: 返回数据
```

### 1.2.2 获取驾驶路线数据

```mermaid
sequenceDiagram
    前端 ->> 后端: GET /scenes/flights/driving 显示所有的驾驶路线
    后端 ->> 前端: 返回数据
```

### 1.2.3 获取所有路线数据

```mermaid
sequenceDiagram
    前端 ->> 后端: GET /scenes/flights 获取所有的驾驶和飞行路线
    后端 ->> 前端: 返回数据
```

## 1.3 获取视角

```mermaid
sequenceDiagram
    前端 ->> 后端: GET /scenes/bookmarks 获取所有的系统级视角书签
    后端 ->> 前端: 返回数据
```

# 2. 模型管理流程

## 2.1 获取模型概况信息

```mermaid
sequenceDiagram
    前端 ->> 后端: 1.GET /models/overview 获取模型概况信息
    后端 ->> 前端: 返回数据

```

## 2.2 获取构件列表和详细资料信息

### 2.2.1 通过搜索框，模糊搜索构件编码

用户通过搜索构件编码查询构件信息，`/models/{id}/constructs/search`中携带搜索关键字：

```mermaid
sequenceDiagram
    前端 ->> 后端: 1.POST /models/{id}/constructs/search 根据搜索关键字，查询返回构件信息
    后端 ->> 前端: 返回数据
    前端 ->> 后端: 2.GET /models/{id}/constructs/{id} 根据构件id，获取构件详细信息以及它所挂接的资料
    后端 ->> 前端: 返回数据
```

### 2.2.2 通过交互式点击构件，查询构件信息

用户通过搜索构件编码查询构件信息，`/models/{id}/constructs/search`中携带构件编码(constructCode)：

```mermaid
sequenceDiagram
    前端 ->> 后端: 1.POST /models/{id}/constructs/search 根据构件编码，查询返回构件信息
    后端 ->> 前端: 返回构件数据
```

# 3. 工程进度流程

## 3.1 获取任务信息

### 3.1.1 通过搜索构件编码，查询任务信息

```mermaid
sequenceDiagram
    前端 ->> 后端: 1.POST /models/{id}/constructs/search 根据搜索关键字，查询返回构件信息
    后端 ->> 前端: 返回数据
    前端 ->> 后端: 2.GET /models/{id}/constructs/{id}/tasks 通过构件id获取构件挂接的任务信息
    后端 ->> 前端: 返回数据
```

### 3.1.2 通过交互式选择构件，查询任务信息

```mermaid
sequenceDiagram
    前端 ->> 后端: 1.POST /projects/progress/tasks/search 通过构件编码，查询构件挂接的任务信息
    后端 ->> 前端: 返回数据
```

## 3.2 偏差查询

```mermaid
sequenceDiagram
    前端 ->> 后端: 1.POST /projects/progress/tasks/get_features_by_status 通过查询参数指定截止日期，计算开工之日起到截止日期期间内的进度偏差
    后端 ->> 前端: 返回数据
```

## 3.3 进度汇报

```mermaid
sequenceDiagram
    前端 ->> 后端: 1.POST  /projects/progress/tasks/report 通过查询参数设置起止时间，返回该时间段内计划的任务、完成的任务、偏差的构件信息
    后端 ->> 前端: 返回数据
```

## 3.4 进度趋势

```mermaid
sequenceDiagram
    前端 ->> 后端: 1.POST /projects/progress/trend 获取任务完成趋势图(计划，完成，逾期) 通过查询参数指定月、周
    后端 ->> 前端: 返回数据
```

## 3.5 进度模拟

```mermaid
sequenceDiagram
    前端 ->> 后端: 1.POST /projects/progress/tasks/get_features_by_day_plan 按天组织，根据工筹计划完成时间的任务数据，用于施工动画进度模拟
    后端 ->> 前端: 返回数据
```

## 3.6 逾期模拟

```mermaid
sequenceDiagram
    前端 ->> 后端: 1.POST /projects/progress/tasks/get_features_by_day_actual 按天组织，根据填写的实际完成日期的任务数据，用于实际偏差动画模拟 POST。
    后端 ->> 前端: 返回数据
```

# 4. 成本计量

## 4.1 通过交互式点击构件，查询标段计量清单信息

```mermaid
sequenceDiagram
    用户 ->> 前端: 用户设置计量清单查询粒度：标段
    用户 ->> 前端: 用户交互式选择一个构件
    前端 ->> 后端: POST /cost/planned_material_listings/biaoduans/search
    后端 ->> 前端: 返回对应粒度的包围盒以及计量信息
```

## 4.2 通过交互式点击构件，查询基坑计量清单信息

```mermaid
sequenceDiagram
    用户 ->> 前端: 用户设置计量清单查询粒度：基坑
    用户 ->> 前端: 用户交互式选择一个构件
    前端 ->> 后端: POST /cost/planned_material_listings/jikengs/search
    后端 ->> 前端: 返回对应粒度的包围盒以及计量信息
```

## 4.3 通过交互式点击构件，查询节段计量清单信息

```mermaid
sequenceDiagram
    用户 ->> 前端: 用户设置计量清单查询粒度：节段
    用户 ->> 前端: 用户交互式选择一个构件
    前端 ->> 后端: POST /cost/planned_material_listings/jieduans/search
    后端 ->> 前端: 返回对应粒度的包围盒以及计量信息
```

## 4.4 通过交互式点击构件，查询构件计量清单信息

```mermaid
sequenceDiagram
    用户 ->> 前端: 用户设置计量清单查询粒度：构件
    用户 ->> 前端: 用户选择交互式选择一个构件
    前端 ->> 后端: POST /cost/planned_material_listings/constructs/search
    后端 ->> 前端: 返回构件以及构件的计量数据
    用户 ->> 前端: 用户交互式选择一个构件
    前端 ->> 后端: POST /cost/planned_material_listings/constructs/search
    后端 ->> 前端: 返回构件以及构件的计量数据
```

## 4.5 通过搜索标段关键字，查询计量清单信息

```mermaid
sequenceDiagram
    用户 ->> 前端: 用户设置计量清单查询粒度：标段
    用户 ->> 前端: 用户输入关键字
    前端 ->> 后端: POST /models/biaoduans/search
    后端 ->> 前端: 返回搜索的标段列表
    用户 ->> 前端: 用户交互式选择某个标段
    前端 ->> 后端: GET /cost/planned_material_listings/biaoduans/{id}
    后端 ->> 前端: 返回标段以及标段的计量数据
```

## 4.6 通过搜索基坑关键字，查询计量清单信息

```mermaid
sequenceDiagram
    用户 ->> 前端: 用户设置计量清单查询粒度：基坑
    用户 ->> 前端: 用户输入关键字
    前端 ->> 后端: POST /models/jikengs/search
    后端 ->> 前端: 返回搜索的标段列表
    用户 ->> 前端: 用户交互式选择某个基坑
    前端 ->> 后端: GET /cost/planned_material_listings/jikengs/{id}
    后端 ->> 前端: 返回基坑以及基坑的计量数据
```

## 4.7 通过搜索节段关键字，查询计量清单信息

```mermaid
sequenceDiagram
    用户 ->> 前端: 用户设置计量清单查询粒度：节段
    用户 ->> 前端: 用户输入关键字
    前端 ->> 后端: POST /models/jieduans/search
    后端 ->> 前端: 返回搜索的节段列表
    用户 ->> 前端: 用户交互式选择某个节段
    前端 ->> 后端: GET /cost/planned_material_listings/jieduans/{id}
    后端 ->> 前端: 返回节段以及节段的计量数据
```

## 4.8 通过搜索构件关键字，查询计量清单信息

```mermaid
sequenceDiagram
    用户 ->> 前端: 用户设置计量清单查询粒度：构件
    用户 ->> 前端: 用户输入关键字
    前端 ->> 后端: POST /models/constructs/search
    后端 ->> 前端: 返回搜索的列表
    用户 ->> 前端: 用户交互式选择某个构件
    前端 ->> 后端: GET /cost/planned_material_listings/constructs/{id}
    后端 ->> 前端: 返回构件以及构件的计量数据
    用户 ->> 前端: 用户交互式选择某个构件
    前端 ->> 后端: GET /cost/planned_material_listings/constructs/{id}
    后端 ->> 前端: 返回构件以及构件的计量数据
```

## 4.9 查询实际完工构件的计量信息

```mermaid
sequenceDiagram
    用户 ->> 前端: 用户选择标段
    用户 ->> 前端: 用户选择起止时间
    前端 ->> 后端: POST /cost/finished_material_listings
    后端 ->> 前端: 返回计量数据
```

## 4.10 获取标段列表信息

```mermaid
sequenceDiagram
    前端 ->> 后端: GET /models/biaoduans
    后端 ->> 前端: 返回所有标段数据
```

# 5. 工程监测

## 5.1 查询监测点总体信息

```mermaid
sequenceDiagram
    前端 ->> 后端: GET /projects/engineering_monitor/overview
    后端 ->> 前端: 返回工程监测的统计数据
```

## 5.2 获取所有监测项目及监测点信息

```mermaid
sequenceDiagram
    前端 ->> 后端: GET /projects/engineering_monitor/monitor_positions
    后端 ->> 前端: 返回所有的监测点数据信息
```

## 5.3 获取监测项目的信息

```mermaid
sequenceDiagram
    前端 ->> 后端: GET /projects/engineering_monitor/monitor_items
    后端 ->> 前端: 返回所有的监测项目数据
```
