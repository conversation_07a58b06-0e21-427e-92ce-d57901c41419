# -*- coding: utf-8 -*-
from odoo import models, fields,api


class resCountryExtend(models.Model):
    '''
    国家扩展
    '''
    _inherit = 'res.country'
    
    geo_json = fields.Text('geo json')

class resCountryStateExtend(models.Model):
    '''
    州/省扩展
    '''
    _inherit = 'res.country.state'
    
    city_ids = fields.One2many('res.country.state.city','state_id',string=u'城市')
    geo_json = fields.Text('geo json')







