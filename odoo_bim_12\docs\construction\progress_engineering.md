
# /projects/progress/overview

## URI

**URI**: /projects/progress/overview

## 支持的方法

 GET

## 介绍

 根据实际完成构建时间和计划时间做对比，计算之后，最终获取当前工程进度完成情况

## HTTP方法

### GET

请求参数：

无


响应结构：

| 字段 | 类型 | 说明 |
|----|----|----|
|title |   String  |  接口标题|
|  finished  |  Object | 已完成   |
|  inProgress|  Object | 运行中，状态正常   |
|  notStarted|  Object | 未启动，状态正常   |
|  planOverDue|  Object | 未启动，计划已过   |
|  inProgressOverDue  |  Object | 进行中，已逾期   |



返回示例：
```
{
 "jsonrpc":2.0,
 "id":null,
 "result":{
           "code":0,
           "message":"xxx",
           "data":{
                   "title":"工程进度",
                   "data":{
                          "finished":{
                                     "label":"已完成",
                                     "value": 123
                                   },
                          "inProgress":{
                                   "label":"施工中(未逾期)",
                                   "value": 123
                               },
                          "notStarted":{
                                        "label":"未启动(未逾期)",
                                        "value": 123
                                       },
                         "planOverDue":{
                                     "label":"未启动(已逾期)",
                                     "value": 123
                                   },
                          "inProgressOverDue":{
                                            "label":"施工中(已逾期)",
                                            "value": 123
                                           }
                              }
                 }   
          }
}
```

# /projects/progress/trend

## URI

**URI**: /projects/progress/trend

## 支持的方法

 POST

## 介绍

 获取任务完成趋势图(计划，完成，逾期) 通过查询参数指定月、周

## HTTP方法

### POST

**body：**
请求消息体：
```json
{ 
  "date":"months" //传months代表查询月数据，传weeks代表查询周统计信息（必填）
}

```


响应结构：

| 字段 | 类型 | 说明 |
|----|----|----|
|title |   String  |  接口标题|
|date | String | 月份或者周时间|
|  overDueCount  |  Object | 逾期   |
|  finishedCount  |  Object | 完成  |
|  planCount|  Object |计划   |




返回示例：
```
{
 "jsonrpc":2.0,
 "id":null,
 "result":{
           "code":0,
           "message":"xxx",
           "data":{
                   "title":"任务完成情况趋势图",
                   "data":[{
                          "date": "2020/8",
                          "overDueCount":40,
                          "finishedCount": 60,
                          "planCount":100,
                              }
                            ]
                 }   
          }
}
```

# /projects/progress/tasks/search

## URI

**URI**: /projects/progress/tasks/search

## 支持的方法

 POST

## 介绍

通过构件编码，查询构件挂接的任务信息

## HTTP方法

### POST

**body：**
请求消息体：
```json
{ 
  "constructCode":"ZTJG-ZX-CQ-II1-AM2-2" //构件编码（必填）
}

```


响应结构：

| 字段 | 类型 | 说明 |
|----|----|----|
|title |   String  |  接口标题|
|  constructName |   Object |  构件名称|
|  initPlanStartDate|   Object |  工筹计划开始时间|
|  initPlanEndDate|  Object | 工筹计划结束时间   |
|  planStartDate|  Object | 任务计划开始时间   |
|  planEndDate|  Object | 任务计划结束时间   |
|  actualStartDate|  Object | 任务实际开始时间   |
|  actualEndDate|  Object | 任务实际结束时间   |
|  type|  Object | 任务类型  |



返回示例：
```
{
 "jsonrpc":2.0,
 "id":null,
 "result":{
           "code":0,
           "message":"xxx",
           "data":{
                   "title":"构件进度信息",
                   "data":{
                          "constructName":{
                                            "label":"构件名称",
                                            "value": "WHJG-CBJG-II-SZ-4417"
                                           },
                           "initPlanStartDate":{
                                                "label":"工筹计划开始时间：",
                                                "value": "2020-08-10"
                                               },
                           "initPlanEndDate":{
                                                "label":"工筹计划结束时间：",
                                                "value": "2020-08-30"
                                               },
                          "planStartDate":{
                                     "label":"任务计划开始时间：",
                                     "value": "2020-08-10"
                                   },
                          "planEndDate":{
                                   "label":"任务计划结束时间：",
                                   "value": "2020-08-30"
                               },
                          "actualStartDate":{
                                        "label":"任务实际开始时间：",
                                        "value": "2020-08-10"
                                       },
                         "actualEndDate":{
                                     "label":"任务实际结束时间：",
                                     "value": "2020-08-20"
                                   },
                         "type":{
                                     "label":"任务类型：",
                                     "value": "类型名称"
                                   }
                              }
                 }   
          }
}
```

# /models/{id}/constructs/{id}/tasks

## URI

**URI**: /models/{id}/constructs/{id}/tasks
## 支持的方法

 GET

## 介绍

通过构件id，查询构件挂接的任务信息

## HTTP方法

### GET

请求参数：无

响应结构:

| 字段 | 类型 | 说明 |
|----|----|----|
|title |   String  |  接口标题|
|  constructTask |   Object |  构件任务信息|
|  constructName |   Object |  构件名称|
|  initPlanStartDate|   Object |  工筹计划开始时间|
|  initPlanEndDate|  Object | 工筹计划结束时间   |
|  planStartDate|  Object | 任务计划开始时间   |
|  planEndDate|  Object | 任务计划结束时间   |
|  actualStartDate|  Object | 任务实际开始时间   |
|  actualEndDate|  Object | 任务实际结束时间   |
|  type|  Object | 任务类型  |
|  constructMessage |   Object |  构件信息|
|  id|   Object |  构件id|
|  smid|   Object |  smid|
|  constructCode|  Object | 构件编码   |
|  smsdrie|  Object | 面对象外接矩形(东)的坐标   |
|  smsdrin|  Object | 面对象外接矩形(北)的坐标   |
|  smsdriw|  Object | 面对象外接矩形(西)的坐标   |
|  smsdris|  Object | 面对象外接矩形(南)的坐标   |
|  levelName|  Object | levelName  |
|  volume|  Object | 体积   |
|  surfaceArea|  Object | 表面积   |
|  vertexCount|  Object | 顶点数量  |




返回示例：
```
{
 "jsonrpc":2.0,
 "id":null,
 "result":{
           "code":0,
           "message":"xxx",
           "data":{
                   "title":"构件进度信息",
                   "data":{
                          "constructTask":{
                                          "constructName":{
                                                          "label":"构件名称",
                                                          "value": "WHJG-CBJG-II-SZ-4417"
                                                         },
                                         "initPlanStartDate":{
                                                              "label":"工筹计划开始时间：",
                                                              "value": "2020-08-10"
                                                             },
                                         "initPlanEndDate":{
                                                              "label":"工筹计划结束时间：",
                                                              "value": "2020-08-30"
                                                             },
                                        "planStartDate":{
                                                   "label":"任务计划开始时间：",
                                                   "value": "2020-08-10"
                                                 },
                                        "planEndDate":{
                                                 "label":"任务计划结束时间：",
                                                 "value": "2020-08-30"
                                             },
                                        "actualStartDate":{
                                                      "label":"任务实际开始时间：",
                                                      "value": "2020-08-10"
                                                     },
                                       "actualEndDate":{
                                                   "label":"任务实际结束时间：",
                                                   "value": "2020-08-20"
                                                 },
                                       "type":{
                                                   "label":"任务类型：",
                                                   "value": "类型名称"
                                                 }
                          },
                          "constructMessage":{
                                              "id":{
                                                              "label":"构件id",
                                                              "value": "2321"
                                                             },
                                             "smid":{
                                                                  "label":"smid",
                                                                  "value": "321"
                                                                 },
                                             "constructCode":{
                                                                  "label":"构件编码",
                                                                  "value": "ZTJG-ZD-CQ-II1-EAM3-2"
                                                                 },
                                            "smsdrie":{
                                                       "label":"面对象外接矩形(东)的坐标",
                                                       "value": "120.680359"
                                                     },
                                            "smsdrin":{
                                                     "label":"面对象外接矩形(北)的坐标",
                                                     "value": "31.2367077"
                                                 },
                                            "smsdriw":{
                                                          "label":"面对象外接矩形(西)的坐标",
                                                          "value": "120.687637"
                                                         },
                                           "smsdris":{
                                                       "label":"面对象外接矩形(南)的坐标",
                                                       "value": "31.2361221"
                                                     },
                                           "levelName":{
                                                       "label":"levelName",
                                                       "value": "类型名称"
                                                     },
                                            "volume":{
                                                          "label":"体积",
                                                          "value": "139.045 立方m"
                                                         },
                                           "surfaceArea":{
                                                       "label":"表面积",
                                                       "value": "441.164 平方m"
                                                     },
                                           "vertexCount":{
                                                       "label":"顶点数量",
                                                       "value": "122"
                                                     }
                          }
                        
                          }
                 }   
          }
}
```

 
# /projects/progress/tasks/get_features_by_status

## URI

**URI**: /projects/progress/tasks/get_features_by_status


## 支持的方法

 POST

## 介绍

 进度偏差，通过查询参数指定截止日期，计算开工之日起到截止日期期间内的进度偏差 

## HTTP方法

### POST

**body：**
请求消息体：
```json
{ 
  "endDate":"2020-10-10" //截至日期（必填）
}

```


响应结构：

| 字段 | 类型 | 说明 |
|----|----|----|
|title |   String  |  接口标题|
|  notStartedOverDue|  Object | 未启动已逾期|
|  startedOverDue|  Object | 施工中已逾期   |
|  count|  Number| 数量|
|  layers|  Array| 图层|
|  name|  String  | 图层名称   |
|  smids|  Array| 构件smid   |




返回示例：
```
{
 "jsonrpc":2.0,
 "id":null,
 "result":{
           "code":0,
           "message":"xxx",
           "data":{
                   "title":"偏差查询",
                   "data":
                     {
                          "notStartedOverDue":{
                                               "count":"23",
                                               "layers":[
                                                          {
                                                            "name":"图层名",
                                                             "smids": [1,2,5]
                                                         },
                                                        {}]     
                                                       },
                            "startedOverDue":{
                                              "count":"13",
                                              "layers":[
                                                          {
                                                            "name":"图层名",
                                                             "smids": [3,9]
                                                         },
                                                         {}]
                                               }
                  }
         }
     }   
}
```


# /projects/progress/tasks/report

## URI

**URI**: /projects/progress/tasks/report

## 支持的方法

POST

## 介绍

进度汇报，通过查询参数设置起止时间，返回该时间段内计划的任务、完成的任务、偏差的构件信息

## HTTP方法

### POST

请求参数：

| 名称 | 类型 | 含义 |
|----|----|----|
|startTime    |Date    |开始时间, 格式：年-月-日    |
|endTime    |Date   |结束时间    |

**body：**
请求消息体：
```json
{ 
  "startTime"："2020-10-20",
  "endTime":"2020-10-21"
}

```

响应结构：

| 字段 | 类型 | 说明 |
|----|----|----|
|title |   String  |  接口标题|
| plan   |Object    |计划    |
| done   |Object    |完成    |
| overdue   |Object    |逾期    |
| count   |Number    |数量    |
| layers   |Array    |图层    |
| name   |String    |图层名称    |
| smids   |Array    |构件smid    |

layers保存的是对应状态下各个图层的数据，它是一个数组，其中会有一个或多个图层信息。

示例：
```
{
 "jsonrpc":2.0,
 "id":null,
 "result":{
           "code":0,
           "message":"xxx",
           "data":{
                   "title":"进度汇报",
                   "data":{
                          "plan":{
                                  "count":123,
                                  "layers":[{
                                            "name":"主体结构",
                                            "smids":[1,2,4,56,23]
                                           }
                                           ]
                                  
                                 },
                          "done":{
                                  "count":213,
                                  "layers":[{
                                            "name":"围护结构",
                                            "smids":[1,2,4,56,23]
                                           }
                                           ]
                                  
                                 },
                         "overdue":{
                                  "count":213,
                                  "layers":[{
                                            "name":"围护结构",
                                            "smids":[1,2,4,56,23]
                                           }
                                           ]
                                  
                                 }  
                          }
                  }
          }
}

```
# /projects/progress/tasks/get_features_by_day_plan

## URI

**URI**: /projects/progress/tasks/get_features_by_day_plan

## 支持的方法

POST

## 介绍

按天组织，根据工筹计划完成时间的任务数据，用于施工动画进度模拟

## HTTP方法

### POST

请求参数：
**body：**
请求消息体：
```json
{ 
  "modelId":5 //当前模型id
}

```

响应结构：

| 字段 | 类型 | 说明 |
|----|----|----|
|title |   String  |  接口标题|
| startDatePoint|Date|计划最早开始任务时间    |
| endDatePoint|Date    |计划最晚结束任务时间   |
| startedFeatures |Object    | 构件任务的开始时间。按照图层分别组织，根据任务的开始时间排列，每个时间点对应的构件smid数据    |
| lastEndDate|Object    |按层组织feature完工信息    |
| doneFeatures|Object    |按层组织信息：层->日期->构件列表    |

示例：
```
{
 "jsonrpc":2.0,
 "id":null,
 "result":{
           "code":0,
           "message":"xxx",
           "data":{
                   "title":"进度模拟",
                   "data":{
                           "startDatePoint":"123",
                           "endDatePoint":"456",
                           "startedFeatures ":{
                                               "图层名称1":{
                                                            "时间1":[11,23,42]
                                                           },
                                               "图层名称2":{
                                                           "时间2":[11,23,42]
                                                           }
                                              },
                           "lastEndDate":{               
                                          "时间1":["F匝道/WHJG-DXLXQ-III-WHFD-DQ01","第一区段基坑/WHJG-DXLXQ-III-S-DQ084"],
                                          "时间2":["第一区段基坑/WHJG-DXLXQ-III-N-DQ84"]
                                                          
                                             },
                          "doneFeatures":{
                                          "图层名称1":{
                                                       "时间1":[11,23,42]
                                                           },
                                          "图层名称2":{
                                                        "时间2":[11,23,42]
                                                           }
                                             }
                        } 
                          
                  }
          }
}

```

# /projects/progress/tasks/get_features_by_day_actual

## URI

**URI**: /projects/progress/tasks/get_features_by_day_actual

## 支持的方法

POST

## 介绍

按天组织，根据填写的实际完成日期的任务数据，用于实际偏差动画模拟 POST。

## HTTP方法

### POST

请求参数：
**body：**
请求消息体：
```json
{ 
"modelId":11
}

```

响应结构：

| 字段 | 类型 | 说明 |
|----|----|----|
|title |   String  |  接口标题|
| startDatePoint|Date| 实际最早开始任务时间    |
| endDatePoint|Date    | 调用本接口的日期   |
| startedFeatures |Object    | 构件任务的实际开始时间。按照图层分别组织，根据任务的开始时间排列，每个时间点对应的构件smid数据    |
| doneFeatures |Object    |  构件任务的实际完成时间。按照图层分别组织，根据任务的开始时间排列，每个时间点对应的构件smid数据   |
| delayedFeatures |Object    |  构件任务的偏差信息。按照图层分别组织，根据任务的开始时间排列，每个时间点对应的构件smid数据   |

示例：
```
{
 "jsonrpc":2.0,
 "id":null,
 "result":{
           "code":0,
           "message":"xxx",
           "data":{
                  
                   "title":"逾期模拟",
                   "data":{
                           "startDatePoint":"123",
                           "endDatePoint":"456",
                           "startedFeatures ":{
                                               "图层名称1":{
                                                            "时间1":[11,23,42]
                                                           },
                                               "图层名称2":{
                                                           "时间2":[11,23,42]
                                                           }
                                              },
                           "delayedFeatures":{
                                               "图层名称1":{
                                                            "时间1":[11,23,42]
                                                           },
                                               "图层名称2":{
                                                           "时间2":[11,23,42]
                                                           }
                                              },
                          "doneFeatures":{
                                          "图层名称1":{
                                                       "时间1":[11,23,42]
                                                           },
                                          "图层名称2":{
                                                        "时间2":[11,23,42]
                                                           }
                                             }
                        } 
                          
                  }
          }
}

```