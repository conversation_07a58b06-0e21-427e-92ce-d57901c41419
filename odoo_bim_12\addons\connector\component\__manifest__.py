# -*- coding: utf-8 -*-
# Copyright 2017 Camptocamp SA
# License LGPL-3.0 or later (http://www.gnu.org/licenses/lgpl.html)

{'name': 'Components',
 'summary': 'Add capabilities to register and use decoupled components,'
            ' as an alternative to model classes',
 'version': '12.0.1.0.1',
 'author': 'Camptocamp,'
           'Odoo Community Association (OCA)',
 'website': 'https://github.com/OCA/connector',
 'license': 'LGPL-3',
 'category': 'Base Application',
 'depends': ['base',
             ],
 'external_dependencies': {
     'python': ['cachetools'],
 },
 'installable': True,
 'development_status': 'Production/Stable',
 'maintainers': ['guewen'],
 }
