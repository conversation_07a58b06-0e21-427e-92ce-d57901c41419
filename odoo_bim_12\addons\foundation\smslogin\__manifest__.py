# -*- coding: utf-8 -*-
{
    'name': "短信登录模块",

    'summary': """
       短信登录模块
       """,

    'description': """
         短信登录模块
    """,
    'sequence': 310,

    'author': "周伟",
    'website': "",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/master/openerp/addons/base/module/module_data.xml
    # for the full list
    'category': 'Base Application',
    'version': '0.1',

    # any module necessary for this one to work correctly
    'depends': ['base','web','base_rest', 'base_rest_datamodel','security_user_roles'],

    # always loaded
    'data': [
        'views/login_record.xml',
        'views/captcha_record.xml',
        'views/verify_code.xml',
        'views/menu.xml',
        'security/security.xml',
        'security/ir.model.access.csv',
        ],
    # only loaded in demonstration mode
    'demo': [
        # 'demo/demo.xml',
    ],
    'qweb': [],
    'installable': True,
    'application': True,
    'auto_install': False,

}