# 说明

## 1. 设计

本目录中的文件用于将后端 ODOO 进行打包，构建 ODOO 镜像。

### 1.1 目标

- 根据当前所在分支代码，自动将后端代码进行打包，构建可以运行、部署的 Odoo Docker 镜像
- 所构建的镜像应该可以通过运行时环境变量配置，用于指定
  - 所连接数据库的主机名
  - 所连接数据库的端口号
  - 所连接数据库的用户名
  - 所连接数据库的密码
  - 所连接的数据库名称
  - 映射到主机上的端口号

### 1.2 构建

- 打包

通过`rsync`命令将当前目录下的代码同步到`dist`中的临时目录，然后通过`tar`进行打包

- 构建

构建镜像的核心文件存储在`docker/odoo/Dockerfile`中，主要完成：

1. 依赖环境的安装
2. Odoo 代码的解压
3. Odoo 依赖的安装

### 1.3 具体实现

1. 从根目录运行命令`yarn docker:build`;
   - 将源码进行压缩打包，存储在 dist 目录。其中`exclude.list`文件用于过滤文件，也即不被打包的文件、目录
   - 将打包完成的`odoo.tar.gz`拷贝到`docker/odoo`目录中
   - 运行`docker build`命令，基于`docker/odoo/Dockerfile`构件镜像
2. 修改配置文件
   - db.env: PostgreSQL 数据库的用户名和密码配置
   - odoo.env: Odoo 启动的环境变量配置
3. 设置环境变量
   - export ODOO_VERSION=<xxxxx> 代表运行的 ODOO 镜像标签
   - export ODOO_HOST_PORT=<xxxxx> 代表 ODOO 在主机运行的端口号，默认为 8069
4. 镜像构建完成后在根目录运行命令启动基于 docker 镜像的 ODOO`docker-compose -f docker/odoo-cluster.yml up`
   - `odoo-cluster.yml`为`docker-compose`文件，所有的环境配置

![2020-12-04-17-06-13](http://markdown.docs.lyon.pub/2020-12-04-17-06-13.png)

## 2. 构建镜像

1. 将代码切换到指定的分支或者标签
2. 在代码根目录下运行命令`yarn docker:build`
3. 构建成功后通过命令`docker images`可以查看到对应的镜像，镜像名称为 odoo，版本号为分支名或者标签名

## 3. 运行

1. 使用`docker`目录下的模板文件，包括：

- odoo-cluster.yml
- db.env
- odoo.env

2. 如果需要修改数据库或者 odoo 的配置，修改上面提到的两个`.env`文件中对应的配置。
3. 通过命令`export ODOO_VERSION=<xxxxx>`设置运行的 ODOO docker 镜像的版本号，这一点非常重要！！！！！
4. 运行命令`docker-compose -f <odoo-cluster.yml的路径> up`，并确保上述提到的三个文件在同一个目录中，这一点非常重要！！！！！！
