<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record model="ir.cron" id="cron_del_report_images">
            <field name="name">定时删除报告图片</field>
            <field name="model_id" ref="model_carbon_project_scheme"/>
            <field name="state">code</field>
            <field name="code">env['carbon.project.scheme'].sudo().del_report_images()</field>
            <field name="active" eval="True"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="interval_number">1</field> <!-- To decide clearly -->
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
        </record>
    </data>
</odoo>
