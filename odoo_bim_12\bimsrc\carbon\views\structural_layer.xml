<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record model="ir.actions.act_window" id="action_structural_layer">
            <field name="name">结构层</field>
            <field name="res_model">structural.layer</field>
            <field name="view_mode">tree,form</field>
            <field name="target">current</field>
            <field name="help" type="html">
                <p class="oe_view_nocontent_create">创建第一条数据
                </p>
            </field>
        </record>
        <record id="structural_layer_form" model="ir.ui.view">
            <field name="name">structural.layer.form</field>
            <field name="model">structural.layer</field>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <group>
                            <field name="name"/>
                            <field mode="tree" name="composition_ids">
                                <tree>
                                    <field name="sequence" widget="handle"/>
                                    <field name="name"/>
                                </tree>
                            </field>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>
        <record id="structural_layer_tree" model="ir.ui.view">
            <field name="name">structural.layer.tree</field>
            <field name="model">structural.layer</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                    <field name="composition_ids" widget="many2many_tags"/>
                </tree>
            </field>
        </record>
    </data>
</odoo>

