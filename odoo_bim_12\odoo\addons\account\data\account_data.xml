<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <record forcecreate="True" id="decimal_payment" model="decimal.precision">
            <field name="name">Payment Terms</field>
            <field name="digits">6</field>
        </record>

        <!-- Open Settings from Purchase Journal to configure mail servers -->
        <record id="action_open_settings" model="ir.actions.act_window">
            <field name="name">Settings</field>
            <field name="res_model">res.config.settings</field>
            <field name="view_mode">form</field>
            <field name="target">inline</field>
            <field name="context" eval="{'module': 'general_settings', 'bin_size': False}"/>
        </record>

        <!-- TAGS FOR CASH FLOW STATEMENT DIRECT METHOD -->

        <record id="account_tag_operating" model="account.account.tag">
            <field name="name">Operating Activities</field>
            <field name="applicability">accounts</field>
        </record>
        <record id="account_tag_financing" model="account.account.tag">
            <field name="name">Financing Activities</field>
            <field name="applicability">accounts</field>
        </record>
        <record id="account_tag_investing" model="account.account.tag">
            <field name="name">Investing &amp; Extraordinary Activities</field>
            <field name="applicability">accounts</field>
        </record>

        <!--
        Payment terms
        -->
        <record id="account_payment_term_immediate" model="account.payment.term">
            <field name="name">Immediate Payment</field>
            <field name="note">Payment terms: Immediate Payment</field>
        </record>

        <record id="account_payment_term_15days" model="account.payment.term">
            <field name="name">15 Days</field>
            <field name="note">Payment terms: 15 Days</field>
            <field name="line_ids" eval="[(5, 0), (0, 0, {'value': 'balance', 'value_amount': 0.0, 'sequence': 500, 'days': 15, 'option': 'day_after_invoice_date'})]"/>
        </record>

        <record id="account_payment_term_21days" model="account.payment.term">
            <field name="name">21 Days</field>
            <field name="note">Payment terms: 21 Days</field>
            <field name="line_ids" eval="[(5, 0), (0, 0, {'value': 'balance', 'value_amount': 0.0, 'sequence': 500, 'days': 21, 'option': 'day_after_invoice_date'})]"/>
        </record>

        <record id="account_payment_term_30days" model="account.payment.term">
            <field name="name">30 Days</field>
            <field name="note">Payment terms: 30 Days</field>
            <field name="line_ids" eval="[(5, 0), (0, 0, {'value': 'balance', 'value_amount': 0.0, 'sequence': 500, 'days': 30, 'option': 'day_after_invoice_date'})]"/>
        </record>

        <record id="account_payment_term_45days" model="account.payment.term">
            <field name="name">45 Days</field>
            <field name="note">Payment terms: 45 Days</field>
            <field name="line_ids" eval="[(5, 0), (0, 0, {'value': 'balance', 'value_amount': 0.0, 'sequence': 500, 'days': 45, 'option': 'day_after_invoice_date'})]"/>
        </record>

        <record id="account_payment_term_2months" model="account.payment.term">
            <field name="name">2 Months</field>
            <field name="note">Payment terms: 2 Months</field>
            <field name="line_ids" eval="[(5, 0), (0, 0, {'value': 'balance', 'value_amount': 0.0, 'sequence': 500, 'days': 60, 'option': 'day_after_invoice_date'})]"/>
        </record>

        <record id="account_payment_term_end_following_month" model="account.payment.term">
            <field name="name">End of Following Month</field>
            <field name="note">Payment terms: End of Following Month</field>
            <field name="line_ids" eval="[(5, 0), (0, 0, {'value': 'balance', 'value_amount': 0.0, 'sequence': 500, 'days': 31, 'option': 'day_following_month'})]"/>
        </record>

        <record id="account_payment_term_advance_60days" model="account.payment.term">
            <field name="name">30% Now, Balance 60 Days</field>
            <field name="note">Payment terms: 30% Now, Balance 60 Days</field>
            <field name="line_ids" eval="[(5, 0), (0, 0, {'value': 'percent', 'value_amount': 30.0, 'sequence': 400, 'days': 0, 'option': 'day_after_invoice_date'}),
                    (0, 0, {'value': 'balance', 'value_amount': 0.0, 'sequence': 500, 'days': 60, 'option': 'day_after_invoice_date'})]"/>
        </record>

        <!--
        Account Statement Sequences
        -->
        <record id="sequence_reconcile_seq" model="ir.sequence">
            <field name="name">Account reconcile sequence</field>
            <field name="code">account.reconcile</field>
            <field name="prefix">A</field>
            <field eval="1" name="number_next"/>
            <field eval="1" name="number_increment"/>
            <field eval="False" name="company_id"/>
        </record>

        <!-- Account-related subtypes for messaging / Chatter -->
        <record id="mt_invoice_validated" model="mail.message.subtype">
            <field name="name">Validated</field>
            <field name="res_model">account.move</field>
            <field name="default" eval="False"/>
            <field name="description">Invoice validated</field>
        </record>
        <record id="mt_invoice_paid" model="mail.message.subtype">
            <field name="name">Paid</field>
            <field name="res_model">account.move</field>
            <field name="default" eval="False"/>
            <field name="description">Invoice paid</field>
        </record>
        <record id="mt_invoice_created" model="mail.message.subtype">
            <field name="name">Invoice Created</field>
            <field name="res_model">account.move</field>
            <field name="default" eval="False"/>
            <field name="hidden" eval="True"/>
            <field name="description">Invoice Created</field>
        </record>

        <!-- Payment methods -->
        <record id="account_payment_method_manual_in" model="account.payment.method">
            <field name="name">Manual</field>
            <field name="code">manual</field>
            <field name="payment_type">inbound</field>
        </record>
        <record id="account_payment_method_manual_out" model="account.payment.method">
            <field name="name">Manual</field>
            <field name="code">manual</field>
            <field name="payment_type">outbound</field>
        </record>

        <!-- Payment sequences -->
        <record id="sequence_payment_customer_invoice" model="ir.sequence">
            <field name="name">Payments customer invoices sequence</field>
            <field name="code">account.payment.customer.invoice</field>
            <field name="prefix">CUST.IN/%(range_year)s/</field>
            <field eval="1" name="number_next"/>
            <field eval="1" name="number_increment"/>
            <field eval="True" name="use_date_range"/>
            <field eval="False" name="company_id"/>
            <field name="padding">4</field>
        </record>
        <record id="sequence_payment_customer_refund" model="ir.sequence">
            <field name="name">Payments customer credit notes sequence</field>
            <field name="code">account.payment.customer.refund</field>
            <field name="prefix">CUST.OUT/%(range_year)s/</field>
            <field eval="1" name="number_next"/>
            <field eval="1" name="number_increment"/>
            <field eval="True" name="use_date_range"/>
            <field eval="False" name="company_id"/>
            <field name="padding">4</field>
        </record>
        <record id="sequence_payment_supplier_invoice" model="ir.sequence">
            <field name="name">Payments supplier invoices sequence</field>
            <field name="code">account.payment.supplier.invoice</field>
            <field name="prefix">SUPP.OUT/%(range_year)s/</field>
            <field eval="1" name="number_next"/>
            <field eval="1" name="number_increment"/>
            <field eval="True" name="use_date_range"/>
            <field eval="False" name="company_id"/>
            <field name="padding">4</field>
        </record>
        <record id="sequence_payment_supplier_refund" model="ir.sequence">
            <field name="name">Payments supplier credit notes sequence</field>
            <field name="code">account.payment.supplier.refund</field>
            <field name="prefix">SUPP.IN/%(range_year)s/</field>
            <field eval="1" name="number_next"/>
            <field eval="1" name="number_increment"/>
            <field eval="True" name="use_date_range"/>
            <field eval="False" name="company_id"/>
            <field name="padding">4</field>
        </record>
        <record id="sequence_payment_transfer" model="ir.sequence">
            <field name="name">Payments transfer sequence</field>
            <field name="code">account.payment.transfer</field>
            <field name="prefix">TRANS/%(range_year)s/</field>
            <field eval="1" name="number_next"/>
            <field eval="1" name="number_increment"/>
            <field eval="True" name="use_date_range"/>
            <field eval="False" name="company_id"/>
            <field name="padding">4</field>
        </record>

        <!-- Account Tax Group -->
        <record id="tax_group_taxes" model="account.tax.group">
            <field name="name">Taxes</field>
            <field name="sequence">0</field>
        </record>

        <!-- Partner Trust Property -->
        <record forcecreate="True" id="default_followup_trust" model="ir.property">
            <field name="name">Followup Trust Property</field>
            <field name="fields_id" search="[('model', '=', 'res.partner'), ('name', '=', 'trust')]"/>
            <field name="value">normal</field>
            <field name="type">selection</field>
        </record>

        <!-- Share Button in action menu -->
        <record id="model_account_move_action_share" model="ir.actions.server">
            <field name="name">Share</field>
            <field name="model_id" ref="account.model_account_move"/>
            <field name="binding_model_id" ref="account.model_account_move"/>
            <field name="binding_view_types">form</field>
            <field name="state">code</field>
            <field name="code">action = records.action_share()</field>
        </record>

    </data>
</odoo>
