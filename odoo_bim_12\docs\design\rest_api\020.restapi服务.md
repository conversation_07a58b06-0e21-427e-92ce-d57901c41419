# restapi服务

## 服务创建

基于`base_rest`开发REST API，所有的服务均需继承自`Component`，并定义:

- `inherit`
- `_name`
- `_usage`
- `_collection`
- `_description`

```python
from odoo.addons.base_rest import restapi
from odoo.addons.component.core import Component


class PingService(Component):
    _inherit = 'base.rest.service'
    _name = 'ping.service'
    _usage = 'ping'
    _collection = 'ping.services'
    _description = "Ping Service"

    ...
```

## 装饰器@restapi.method()

在服务类中，我们定义方法，并通过`@restapi.method()`来装饰该方法，实现：

- 路由：URL和方法
- 输入参数模型
- 输出参数模型
- 鉴权
- cors
- csrf

具体各个部分的参数，详见该装饰器方法的文档：

```python
def method(
    routes, input_param=None, output_param=None, auth=None, cors=None, csrf=False
):
    """Decorator marking the decorated method as being a handler for
    REST requests. The method must be part of a component inheriting from
  ``base.rest.service``.

    :param routes: list of tuple (path, http method). path is a string or
                  array.
                  Each tuple determines which http requests and http method
                  will match the decorated method. The path part can be a
                  single string or an array of strings. See werkzeug's routing
                  documentation for the format of path expression (
                  http://werkzeug.pocoo.org/docs/routing/ ).
    :param: input_param: An instance of an object that implemented
                  ``RestMethodParam``. When processing a request, the http
                  handler first call the from_request method and then call the
                  decorated method with the result of this call.
    :param: output_param: An instance of an object that implemented
                  ``RestMethodParam``. When processing the result of the
                  call to the decorated method, the http handler first call
                  the `to_response` method with this result and then return
                  the result of this call.
    :param auth: The type of authentication method
    :param cors: The Access-Control-Allow-Origin cors directive value.
    :param bool csrf: Whether CSRF protection should be enabled for the route.
                      Defaults to ``False``

    """
```

## 服务的调用

1. 代码定义的服务会在创建服务注册中心时被用于动态创建Odoo http控制器`http.Controller`，见《300.restapi服务注册_创建.md》
2. 客户端发起API调用时，会通过`RestController`的`_process_method`方法将调用转给服务对应的方法，见《010.restapi控制器.md》
