# /projects/engineering_monitor/overview

# 接口 URI

## URI

**URI**: /projects/engineering_monitor/overview

## 支持的方法

GET

## 介绍

展示工程监测的统计信息:

1.显示监测点的总数

2.显示异常监测点的数量

3.显示活跃的监测点数

4.显示环境监测点数

## HTTP 方法

### GET

请求参数：

无

响应结构：

| 字段                              | 类型    | 说明                                      |
| --------------------------------- | ------- | ----------------------------------------- |
| title                             | String  | 接口标题                                  |
| monitorPositionCount              | Object  | 监测点总数                                |
| abnormalPositionCount             | Object  | 异常监测点数量                            |
| monitorPositionsWithValueCount    | Object  | 活跃监测点数量                            |
| noConstructMonitorPositionsCount  | Object  | 环境监测点数量                          |
| abnormalPositions                 | Array   | 异常监测点                                |
| abnormalPositions.code            | String  | 监测点编码                                |
| abnormalPositions.monitorItemName | String  | 监测项目名称                              |
| abnormalPositions.new             | Boolean | 新增异常标志，true 代表为最新一期数据异常 |

返回示例：

```
{
 "jsonrpc":2.0,
 "id":null,
 "result":{
        "code":0,
        "message":"xxx",
        "data":{
            "title":"工程监测",
            "data":{
                "monitorPositionCount":{
                    "label":"监测点总数",
                    "value": 123
                },
                "abnormalPositionCount":{
                    "label":"异常监测点数量",
                    "value": 123
                },
                "monitorPositionsWithValueCount":{
                    "label":"活跃监测点数量",
                    "value": 123
                },
                "noConstructMonitorPositionsCount":{
                    "label":"环境监测点数量",
                    "value": 123
                },
                "abnormalPositions": [{
                    "code": "ZQC10",
                    "monitorItemName": "监测项目的平移",
                    "new": false
                }]
            }
        }
    }
}
```

# /projects/engineering_monitor/monitor_positions

# 接口 URI

## URI

**URI**: /projects/engineering_monitor/monitor_positions

## 支持的方法

GET

## 介绍

获取项目的所有工程监测点信息。

## HTTP 方法

### GET

请求参数：

无

响应结构：

| 字段                                                    | 类型   | 说明                                                 |
| ------------------------------------------------------- | ------ | ---------------------------------------------------- |
| title                                                   | String | 接口标题                                             |
| monitorPositions                                        | Array  | 监测点                                               |
| monitorPositions.code                                   | String | 监测点编码                                           |
| monitorPositions.monitorItemName                        | String | 监测点所归属的监测项目名称                           |
| monitorPositions.position                               | Object | 监测点位置, x、y、z 分别代表经纬度以及高度           |
| monitorPositions.iconUrl                                | String | 图标的 URL 链接，如果后端没有配置，传递空字符串      |
| monitorPositions.iconScale                              | String | 图标在前端显示时的缩放比例，如果后端没有配置，传递 0 |
| monitorPositions.url                                    | String | 监测点对应的可视化图表 URL                           |
| monitorPositions.monitorPositionConstruct               | Object | 监测点关联的监测点模型的信息，如果没有关联返回 null  |
| monitorPositions.monitorPositionConstruct.constructCode | String | 监测点模型的编码                                     |
| monitorPositions.monitorPositionConstruct.smid          | String | 监测点模型的图元 id                                  |
| monitorPositions.monitorPositionConstruct.levelName     | String | 监测点模型的图元所在的图层名                         |
| monitorPositions.monitorPositionConstruct.smsdrie       | Number | 监测点模型的图元包围盒的坐标                         |
| monitorPositions.monitorPositionConstruct.smsdrin       | Number | 监测点模型的图元包围盒的坐标                         |
| monitorPositions.monitorPositionConstruct.smsdrin       | Number | 监测点模型的图元包围盒的坐标                         |
| monitorPositions.monitorPositionConstruct.smsdris       | Number | 监测点模型的图元包围盒的坐标                         |
| monitorPositions.monitorPositionConstruct.smsdriw       | Number | 监测点模型的图元包围盒的坐标                         |
| monitorPositions.monitorPositionConstruct.topaltitude   | Number | 监测点模型的图元包围盒的最高处的高度                 |
| monitorPositions.monitoredConstructs                    | Object | 监测点所监测的构件列表，如果没有则传递空列表         |
| monitorPositions.monitoredConstructs.constructCode      | String | 被监测构件的构件编码                                 |
| monitorPositions.monitoredConstructs.smid               | String | 被监测构件的图元 id                                  |
| monitorPositions.monitoredConstructs.levelName          | String | 被监测构件图元所在的图层名                           |
| monitorPositions.monitoredConstructs.smsdrie            | Number | 被监测构件图元包围盒的坐标                           |
| monitorPositions.monitoredConstructs.smsdrin            | Number | 被监测构件图元包围盒的坐标                           |
| monitorPositions.monitoredConstructs.smsdrin            | Number | 被监测构件图元包围盒的坐标                           |
| monitorPositions.monitoredConstructs.smsdris            | Number | 被监测构件图元包围盒的坐标                           |
| monitorPositions.monitoredConstructs.smsdriw            | Number | 被监测构件图元包围盒的坐标                           |
| monitorPositions.monitoredConstructs.topaltitude        | Number | 被监测构件图元包围盒的最高处的高度                   |

返回示例：

```
{
 "jsonrpc":2.0,
 "id":null,
 "result":{
        "code":0,
        "message":"xxx",
        "data":{"monitorItems":{
            "monitorPositions": [{
                "code": "ZQC10",
                "monitorItemName": "水平位移",
                "position": {
                    "x": 117.0512,
                    "y": 31.0512,
                    "z": 500
                },
                "iconUrl": "",
                "iconScale": 0,
                "url": "http://x.y.z/ZQC10",
                "monitorPositionConstruct": {
                    "constructCode": "XXXXXXX-123",
                    "smid": 123,
                    "levelName": "...",
                    "smsdrie": 120,
                    "smsdrin": 31,
                    "smsdris": 30,
                    "smsdriw": 119,
                    "topaltitude": 100
                },
                "monitoredConstructs": [{
                    "constructCode": "XXXXXXX-123",
                    "smid": 123,
                    "levelName": "...",
                    // 两个点的经纬度+高度
                    "smsdrie": 1.XX,
                    "smsdrin": 31.2XXX,
                    "smsdris": XXX,
                    "smsdri2": XXX,
                    "topaltitude": XXX,

                }]
            }]
        }
    }}
}
```

# /projects/engineering_monitor/monitor_items

# 接口 URI

## URI

**URI**: /projects/engineering_monitor/monitor_items

## 支持的方法

GET

## 介绍

获取所有的监测项目信息

## HTTP 方法

### GET

请求参数：

无

响应结构：

| 字段                                       | 类型   | 说明                                 |
| ------------------------------------------ | ------ | ------------------------------------ |
| title                                      | String | 接口标题                             |
| monitorItems                               | Array  | 监测项目列表                         |
| monitorItems.name                          | String | 监测项目列表                         |
| monitorItems.monitorPositionsCount         | Number | 监测项目包含的监测点数量             |
| monitorItems.abnormalMonitorPositionsCount | Number | 监测项目包含的异常监测点数量         |
| monitorItems.valueMonitorPositionsCount  | Number | 监测项目包含的有监测数据的监测点数量 |

返回示例：

```
{
 "jsonrpc":2.0,
 "id":null,
 "result":{
        "code":0,
        "message":"xxx",
        "data":{
            "title":"工程监测",
            "monitorItems": [{
                "name": "水平位移1",
                "monitorPositionsCount": 100,
                "abnormalMonitorPositionsCount": 25,
                "valueMonitorPositionsCount", 10
            }]
        }
    }
}
```
