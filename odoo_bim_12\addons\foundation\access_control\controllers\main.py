# -*- coding: utf-8 -*-
from odoo import http
from odoo.http import request
import json
import logging

_logger = logging.getLogger(__name__)

class AccessControlApi(http.Controller):
    @http.route('/api/get_user_navigations', type='http', auth='user', methods=['GET'], csrf=False)
    def get_user_navigations(self, *args, **kwargs):
        try:
            user = request.env.user
            user_roles = user.security_role_ids
            data = {}
            for role in user_roles:
                data[role.id] = role.get_role_navigation_access_by_web()
            # data = [role.get_role_navigation_access_by_web() for role in user_roles]
            # res = []
            # for x in data:
            #     for navigation in x.get('navigations'):
            #         if navigation.get('path') not in list(map(lambda x: x.get('path'), res)):
            #             res.append(navigation)


                    # else:
                    #     index = None
                    #     for idx, r in enumerate(res):
                    #         if r.get('path') == navigation.get('path'):
                    #             index = idx
                    #             break


            # print(res)
            res = {
                "jsonrpc": 2.0,
                "id": None,
                "result": {
                    "code": 0,
                    "message": "ok",
                    "data": data
                }
            }
        except Exception as e:
            res = {
                "jsonrpc": 2.0,
                "id": None,
                "result": {
                    "code": 1,
                    "message": str(e),
                    "data": []
                }
            }
        return json.dumps(res)
    @http.route('/api/get_user_routes', type='http', auth='user', methods=['GET'], csrf=False)
    def get_user_routes(self, *args, **kwargs):
        # try:
            user = request.env.user
            user_roles = user.security_role_ids
            data = {}
            for role in user_roles:
                data[role.id] = role.get_role_access_by_web()
            # data = [role.get_role_access_by_web() for role in user_roles]
            # _logger.info(data)
            # res = []
            # for x in data:
            #     for route in x.get('routes'):
            #         if route.get('path') not in list(map(lambda x: x.get('path'), res)):
            #             res.append(route)
            #         else:
            #             index = None
            #             for idx, r in enumerate(res):
            #                 if r.get('path') == route.get('path'):
            #                     index = idx
            #                     break

            #             buttons = res[index].get('buttons')
            #             for button in route.get('buttons'):
            #                 if button.get('bid') not in list(map(lambda x: x.get('bid'), buttons)):
            #                     buttons.append({
            #                         'bid': button.get('bid'),
            #                         'name': button.get('name'),
            #                         'selected': button.get('selected'),
            #                         'state': button.get('state')
            #                     })
            #                 else:
            #                     index2 = None
            #                     for idx2, b in enumerate(buttons):
            #                         if b.get('bid') == button.get('bid'):
            #                             index2 = idx2
            #                     state = 'invisible'
            #                     _logger.info(button)
            #                     if (button.get('selected') and buttons[index2]['selected']) or ( not button.get('selected') and not buttons[index2]['selected']):
            #                         if button.get('state') == 'disable' or buttons[index2]['selected'] == 'disable':
            #                             state = 'disable'
            #                         if button.get('state') == 'active' or buttons[index2]['selected'] == 'active':
            #                             state = 'active'
            #                     if button.get('selected'):
            #                         state = button.get('state')
            #                     if buttons[index2]['selected']:
            #                         state = buttons[index2]['state']
            #                     buttons[index2]['state'] = state
            #                     buttons[index2]['selected'] = button.get('selected') or buttons[index2]['selected']
            #             res[index]['buttons'] = buttons
            
            res = {
                "jsonrpc": 2.0,
                "id": None,
                "result": {
                    "code": 0,
                    "message": "ok",
                    "data":data
                }
            }
        # except Exception as e:
        #     res = {
        #         "jsonrpc": 2.0,
        #         "id": None,
        #         "result": {
        #             "code": 1,
        #             "message": str(e),
        #             "data": []
        #         }
        #     }
            return json.dumps(res)

    @http.route('/api/routes', type='json',auth='user', methods=['POST'],csrf=False)
    def routes(self, *args, **kwargs):
        try:
            data = json.loads(request.httprequest.data)
            print(data)
            data = data.get('routes')
            request.env['access.route'].search([]).unlink()
            request.env['role.access.route'].search([]).unlink()

            def get_buttons(route):
                try:
                    if route.get('meta').get('buttons'):
                        return [(0,0,b) for b in route.get('meta').get('buttons')]
                except:
                    return []

            def depth_traversal(_route, route,ps):
                is_exist = ps.get('is_exist')
                exist_id = ps.get('exist_id')
                n = 1 if is_exist else 0
                if route.get('children'):
                    _route['children'] = []
                    for r in route.get('children'):
                        ps_r = {
                            'is_exist': False,
                            'exist_id': 0
                        }
                        _r = {
                            'path': r.get('path'),
                            'name': r.get('name'),
                            'buttons': get_buttons(r)
                        }
                        depth_traversal(_r, r,ps_r)
                        _route['children'].append((n, exist_id, _r))

            res = []
            for route in data:
                ps = {
                    'is_exist':False,
                    'exist_id':0
                }
                _route = {
                    'path': route.get('path'),
                    'name': route.get('name'),
                    'buttons':get_buttons(route)
                }
                depth_traversal(_route, route,ps)
                res.append(_route)
            print(res)

            for vals in res:
                request.env['access.route'].create(vals)

            res = {
                "code": 0,
                "message": 'ok',
                "data": [],
            }

        except Exception as e:
            res = {
                "code": 1,
                "message": str(e),
                "data": []
            }
        return res



        # for route in data:
        #     active_route = request.env['access.route'].search([('path','=',route.get('path'))],limit=1)
        #     if active_route:#update route
        #         active_route.update({
        #             'is_active':True,
        #             'name':route.get('name'),
        #             'path':route.get('path'),
        #         })
        #         active_route_exist_buttons = active_route.buttons
        #         ready_deactive_buttons = [b for b in active_route_exist_buttons if b.bid not in list(map(lambda x: x.get('bid'), route.get('buttons')))]
        #         for button in route.get('buttons'):
        #             active_button = request.env['access.route.button'].search([('bid', '=', button.get('bid')),('route_id','=',active_route.id)], limit=1)
        #             if active_button:#update route
        #                 active_button.update({
        #                     'is_active': True,
        #                     'name':button.get('name'),
        #                 })
        #             else:#create route
        #                 request.env['access.route.button'].create({
        #                     'bid':button.get('bid'),
        #                     'name':button.get('name'),
        #                     'route_id':active_route.id,
        #                 })
        #         for b in ready_deactive_buttons:
        #             b.update({
        #                 'is_active':False
        #             })
        #     else:#create route
        #         request.env['access.route'].create({
        #             'name':route.get('name'),
        #             'path':route.get('path'),
        #             'buttons':[(0,0,{
        #                 'bid':b.get('bid'),
        #                 'name':b.get('name'),
        #             }) for b in route.get('buttons')],
        #         })
        # for r in ready_deactive_routes:
        #     r.update({
        #         'is_active': False
        #     })
        #     r.buttons.update({
        #         'is_active': False
        #     })
        # return 'success'
