<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- <record id="account_comp_rule_knowcenter_qiniufile" model="ir.rule">
     <field name="name">Account multi-knowcenter_qiniufile</field>
     <field name="model_id" ref="model_knowcenter_qiniufile"/>
     <field name="global" eval="True"/>
     <field name="domain_force">['|',('company_id','=',False),('company_id','child_of',user.active_company_ids.ids)]</field>
    </record>

    <record id="account_comp_knowcenter_provider" model="ir.rule">
     <field name="name">Account multi-knowcenter_provider</field>
     <field name="model_id" ref="model_knowcenter_provider"/>
     <field name="global" eval="True"/>
     <field name="domain_force">['|',('company_id','=',False),('company_id','child_of',user.active_company_ids.ids)]</field>
    </record>

    <record id="account_comp_knowcenter_library" model="ir.rule">
     <field name="name">Account multi-knowcenter_library</field>
     <field name="model_id" ref="model_knowcenter_library"/>
     <field name="global" eval="True"/>
     <field name="domain_force">['|',('company_id','=',False),('company_id','child_of',user.active_company_ids.ids)]</field>
    </record>

    <record id="account_comp_knowcenter_attachment" model="ir.rule">
     <field name="name">Account multi-knowcenter_attachment</field>
     <field name="model_id" ref="model_knowcenter_attachment"/>
     <field name="global" eval="True"/>
     <field name="domain_force">['|',('company_id','=',False),('company_id','child_of',user.active_company_ids.ids)]</field>
    </record> -->

</odoo>