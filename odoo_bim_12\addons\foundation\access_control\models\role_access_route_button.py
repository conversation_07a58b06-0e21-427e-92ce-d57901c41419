# -*- coding: utf-8 -*-
from odoo import fields, api, models

class RoleAccessRouteButton(models.Model):
    """
    按钮权限
    """
    _name = 'role.access.route.button'
    _description = "按钮权限"

    domId = fields.Char('按钮ID',related='button_id.domId')
    name = fields.Char('按钮名称',related='button_id.name')
    button_id = fields.Many2one('access.route.button', '按钮')
    state = fields.Selection([('active', 'active'),
                             ('disable', 'disable'),
                             ('invisible', 'invisible')], string='按钮状态',default='active')
    role_route_id = fields.Many2one('role.access.route', '路由权限',ondelete='cascade')















