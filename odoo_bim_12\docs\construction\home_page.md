# /projects/overview

## URI

**URI**: /projects/overview

## 支持的方法

GET

## 介绍

获取工程概况信息

## HTTP方法

### GET

请求参数：无


响应结构：

| 字段 | 类型 | 说明 |
|----|----|----|
| title    |String    | 接口标题   |
| name   |  String   |  项目名称  |
|  totalInvestment  |  Number   | 总投资额，单位元   |
| projectStartEndDate | String | 计划起止时间 |
| projectOwner | String | 业主单位 |
| projectConstructionSide | String | 施工单位 |
| projectSuperisionSide | String | 监理单位 | 
| overallProjectStartTime| String | 整体项目开始时间 |
| overallProjectEndTime| String | 整体项目结束时间 |
| latestProjectUpdateTime | String | 项目最新更新时间 |

示例：
```
{
 "jsonrpc":2.0,
 "id":null,
 "result":{
           "code":0,
           "message":"xxx",
           "data":{
                   "title":"工程概况",
                   "dataList":{
                                "name":{
                                "label":"项目名称",
                                "value": "312隧道工程"
                               },
                                "totalInvestment":{
                                "label":"总投资额(元)",
                                "value": 12345
                               },
                               "projectStartEndDate":{
                                "label":"起止日期",
                                "value": "2020/10/19--2020/10/23"
                               },
                               "projectOwner":{
                                "label":"业主单位",
                                "value": "xx公司"
                               },
                               "projectConstructionSide":{
                                "label":"施工单位",
                                "value": "xx公司"
                               },
                               "projectSupervisionSide":{
                                "label":"监理单位",
                                "value": "xx公司"
                               },
                               "overallProjectStartTime":{
                                "label":"整体项目开始时间",
                                "value": "2020/10/19"
                               },
                               "overallProjectEndTime":{
                                "label":"整体项目结束时间",
                                "value": "2020/10/23"
                               },
                               "latestProjectUpdateTime ":{
                                "label":"项目最新更新时间",
                                "value": "2020/11/02"
                               }
                              }
                 }   
          }
}
```

# /projects/weather

## URI

**URI**: /projects/weather

## 支持的方法

GET

## 介绍

获取天气概况信息

## HTTP方法

### GET

请求参数：无


响应结构：

| 字段 | 类型 | 说明 |
|----|----|----|
|title    |String    | 接口标题   |
| city   |  String   | 城市名称   |
| weather | Object | 天气信息 |



示例：
```
{
  "id": null,
  "result": {
            "code": 0,
            "message": "xxx",
            "data": {
                      "title": "天气信息",
                      "data": {
                                "cityid": "101221301",
                                "update_time": "2020-10-22 09:06:13",
                                "city": "南京",
                                "cityEn": "tongling",
                                "country": "中国",
                                "countryEn": "China",
                                "data":[{
                                        "day": "22日（今天）",
                                        "date": "2020-10-22",
                                        "week": "星期四",
                                        "wea": "晴",
                                        "wea_img": "qing",
                                        "air": 16,
                                        "humidity": 91,
                                        "air_level": "优",
                                        "air_tips": "空气很好，可以外出活动，呼吸新鲜空气，拥抱大自然！",
                                        "alarm": {
                                                  "alarm_type": "",
                                                  "alarm_level": "",
                                                  "alarm_content": ""
                                                },
                                        "tem1": "19℃",
                                        "tem2": "10℃",
                                        "tem": "15℃",
                                        "win": [
                                                  "北风",
                                                  "东北风"
                                                ],
                                        "win_speed": "<3级",
                                        "hours": [
                                                  {
                                                    "day": "22日08时",
                                                    "wea": "多云",
                                                    "tem": "15℃",
                                                    "win": "北风",
                                                    "win_speed": "<3级"
                                                  },
                                                  {
                                                    "day": "22日11时",
                                                    "wea": "晴",
                                                    "tem": "17℃",
                                                    "win": "北风",
                                                    "win_speed": "<3级"
                                                  },
                                                  {
                                                    "day": "22日14时",
                                                    "wea": "晴",
                                                    "tem": "19℃",
                                                    "win": "北风",
                                                    "win_speed": "<3级"
                                                  },
                                                  {
                                                    "day": "22日17时",
                                                    "wea": "晴",
                                                    "tem": "17℃",
                                                    "win": "北风",
                                                    "win_speed": "<3级"
                                                  },
                                                  {
                                                    "day": "22日20时",
                                                    "wea": "晴",
                                                    "tem": "14℃",
                                                    "win": "北风",
                                                    "win_speed": "<3级"
                                                  },
                                                  {
                                                    "day": "22日23时",
                                                    "wea": "晴",
                                                    "tem": "13℃",
                                                    "win": "东北风",
                                                    "win_speed": "<3级"
                                                  },
                                                  {
                                                    "day": "23日02时",
                                                    "wea": "晴",
                                                    "tem": "10℃",
                                                    "win": "东北风",
                                                    "win_speed": "<3级"
                                                  },
                                                  {
                                                    "day": "23日05时",
                                                    "wea": "晴",
                                                    "tem": "11℃",
                                                    "win": "东北风",
                                                    "win_speed": "<3级"
                                                  }
                                                ],
                                         "index": [      
                                                  {
                                                    "title": "中暑指数",
                                                    "level": "无中暑风险",
                                                    "desc": "天气舒适，令人神清气爽的一天，不用担心中暑的困扰。"
                                                  },
                                                  {
                                                    "title": "运动指数",
                                                    "level": "",
                                                    "desc": "春天不减肥，夏天徒伤悲。天气较舒适，快去运动吧。"
                                                  },
                                                  {
                                                    "title": "健臻·血糖指数",
                                                    "level": "易波动",
                                                    "desc": "血糖易波动，注意监测。"
                                                  },
                                                  {
                                                    "title": "穿衣指数",
                                                    "level": "较舒适",
                                                    "desc": "建议穿薄外套或牛仔裤等服装。"
                                                  },
                                                  {
                                                    "title": "洗车指数",
                                                    "level": "适宜",
                                                    "desc": "天气较好，适合擦洗汽车。"
                                                  },
                                                  {
                                                    "title": "紫外线指数",
                                                    "level": "强",
                                                    "desc": "涂擦SPF大于15、PA+防晒护肤品。"
                                                  }
                                                ]
                                      }
                                    ]
                              }
            }
  }
}
```

    

# /projects/video_monitor/overview

## URI

**URI**: /projects/video_monitor/overview

## 支持的方法

 GET

## 介绍

 获取项目当前视频监控的整体情况

## HTTP方法

### GET

请求参数：

无


响应结构：

| 字段 | 类型 | 说明 |
|----|----|----|
|title    |String    | 接口标题   |
|monitorPositionCount    |Object    |监控点总数    |
|onlinePositionCount    |Object    |在线总数    |
|offlinePositionCount    |Object    |离线总数    |


示例：
```
{
 "jsonrpc":2.0,
 "id":null,
 "result":{
           "code":0,
           "message":"xxx",
           "data":{
                   "title":"视频监控",
                   "data":{
                           "monitorPositionCount":{
                                               "label":"监测点总数",
                                               "value": 123
                                               },
                           "onlinePositionCount":{
                                               "label":"在线总数",
                                               "value":232
                                               },
                           "offlinePositionCount":{
                                              "label":"离线总数",
                                              "value": 412
                                               }
                              }
                 }   
          }
}
```    
    




# /projects/investment/overview

## URI

**URI**: /projects/investment/overview

## 支持的方法

 GET

## 介绍

 获取当前项目工程投资额整体信息

## HTTP方法

### GET

请求参数：

 无


响应结构：

| 字段 | 类型 | 说明 |
|----|----|----|
|title    |String    | 接口标题   |
|totalInvestmentAmount |Object    | 总投资额(万元)   |
|investedAmount |Object    |  已用金额  |
|unInvestedAmount |Object     |  剩余金额  |


示例：
```
{
 "jsonrpc":2.0,
 "id":null,
 "result":{
           "code":0,
           "message":"xxx",
           "data":{
                   "title":"成本计量",
                   "data":{"totalInvestment":{
                                              "label":"总投资额(万元)",
                                              "value": 123
                                               },
                           "investedAmount":{
                                        "label":"已用金额",
                                        "value":222,
                                        "valuePercent":"70%"
                                        },
                           "unInvestedAmount":{
                                             "label":"剩余金额 ",
                                             "value": 22,
                                             "valuePercent":"30%"
                                             }
                              }
                 }   
          }
}
```    


# /models/overview

## URI

**URI**: /models/overview

## 支持的方法

 GET 

## 介绍

 获取系统当前生效模型版本的概况信息

 1.显示当前模板的构件总数

 2.显示当前模板的图层总数

 3.逐个显示当前模板的各个bim模型下的构件总数和图层数

  

## HTTP方法

### GET

请求参数：

无

响应结构：

| 字段 | 类型 | 说明 |
|----|----|----|
|title    |String    | 接口标题   |
|   totalConstructCount|Object   |当前模板的构件总数    |
|  totalLayerCount |Object    |模板的图层总数    |
|  structureName|String    |bim模型名称   |
|  layerCount|Array   |bim模型下的构件总数  |
|  constructCount|Array   |bim模型下的构件图层数  |
|  ... |...    |...    |


返回示例：
```
{
 "jsonrpc":2.0,
 "id":null,
 "result":{
           "code":0,
           "message":"xxx",
           "data":{
                   "title":"模型信息",
                   "data":{
                           "totalConstructCount":{"lable":"构件总数",
                                             "value":123},
                           "totalLayerCount":{"lable":"图层总数",
                                          "value":12},
                           "structureName":[{
                                        "name":"主体结构",
                                        "constructCount":11,
                                        "layerCount":22
                                       },
                                       {
                                        "name":"附加结构",
                                        "constructCount":11,
                                        "layerCount":22
                                       },
                                       {
                                        "name":"围护结构",
                                        "constructCount":11,
                                        "layerCount":22
                                       },
                                       {
                                        "name":"机电工程",
                                        "constructCount":11,
                                        "layerCount":22
                                       },
                                       {}
                                     ]
                        }
                 }   
          }
}
```



# /models/{id}/explosions

## URI

**URI**: /models/{id}/explosions

## 支持的方法

 GET 

## 介绍

 获取系统当前模型的爆炸点信息

## HTTP方法

### GET

请求参数：

无

响应结构：

| 字段 | 类型 | 说明 |
|----|----|----|
|  id    | Number    | 爆炸点ID  |
|  name    | String    | 爆炸点名称  |
|  position    | Array    | 爆炸点的位置, 使用墨卡托坐标系坐标，经纬度以及高度  |
|  layers    | Array    | 爆炸区域按图层组织的图元信息  |
|  layers.name    | String    | 图层名称  |
|  layers.offset    | Array    | x,y,z方向的偏移 |
|  layers.ids    | Array    | 该图层的图元数组 |
|  layers.ids.smid    | ID    | 图元的SMID |
|  layers.ids.position    | ID    | 图元的中心点坐标 |
|  layers.ids.constructCode    | ID    | 图元对应的构件编码 |
|  layers.ids.layerName    | ID    | 图元对应的图层名称 |


返回示例：
```
{
 "jsonrpc":2.0,
 "id":null,
 "result":{
           "code":0,
           "message":"xxx",
           "data":[{
                        "id": 0,
                        "name": "爆炸点1",
                        "position": [ 121, 30, 50 ],
                        "layers": [{
                            "name": "顶板",
                            "offset": [ 100, 50, 5],
                            "ids": [{
                                "smid": "1",
                                "position": [ 121, 29.98, 45],
                                "constructCode": "X-Y-Z1",
                                "layerName": "顶板"
                            }, {
                                "smid": "2",
                                "position": [ 121, 29.98, 45],
                                "constructCode": "X-Y-Z2",
                                "layerName": "顶板"
                            }]
                        }]
                    }]
                    
        }
}
```


# /scenes/flights

## URI

**URI**: /scenes/flights

## 支持的方法

 GET 

## 介绍

 所有的驾驶和飞行路线

 1.显示驾驶的所有路线

 2.显示飞行的所有路线 

## HTTP方法

### GET

请求参数：

无

响应结构：

| 字段 | 类型 | 说明 |
|----|----|----|
|lineName  |String    | 路线名称   |
|type  |Number | 0 - 驾驶  1 - 飞行   |
|route |Object    |显示飞行的路线    |

返回示例：
```
{
 "jsonrpc":2.0,
 "id":null,
 "result":{
           "code":0,
           "message":"xxx",
           "data":[
                  {
                   "lineName":"驾驶路径名1",
                   "type":0,
                   "route":{
                            "id": "05141424192",
                            "name": "H匝道",
                            "geometry": {
                                        "type": "LineString",
                                        "coordinates": [
                                                        [120.677708264787,31.236798361041,14.9343952881172],
                                                        [120.706149620925,31.2429894333088,8.81605981942266]]
                                         },
                            "properties": {
                                            "edittype": "polyline",
                                            "name": "线",
                                            "position": {
                                                        "height": true,
                                                        "minCount": 2
                                                        },
                                            "style": {
                                                     "color": "#ffff00"
                                                     },
                                            "attr": {
                                                    "id": "05141424192",
                                                    "name": "H匝道",
                                                    "point": "",
                                                    "showLabel": false,
                                                    "showLine": false,
                                                    "showShadow": false,
                                                    "showHeightWarn": false,
                                                    "warnHeight": 500,
                                                    "cameraType": "dy",
                                                    "followedX": 200,
                                                    "followedZ": 0,
                                                    "clockRange": false
                                                    },
                                            "type": "polyline"
                                         }
                        }
                },
                {
                   "lineName":"飞行路径名1",
                   "type":1,
                   "route":{
                            "id": "05141424192",
                            "name": "H匝道",
                            "geometry": {
                                        "type": "LineString",
                                        "coordinates": [
                                                        [120.677708264787,31.236798361041,14.9343952881172],
                                                        [120.706149620925,31.2429894333088,8.81605981942266]]
                                         },
                            "properties": {
                                            "edittype": "polyline",
                                            "name": "线",
                                            "position": {
                                                        "height": true,
                                                        "minCount": 2
                                                        },
                                            "style": {
                                                     "color": "#ffff00"
                                                     },
                                            "attr": {
                                                    "id": "05141424192",
                                                    "name": "H匝道",
                                                    "point": "",
                                                    "showLabel": false,
                                                    "showLine": false,
                                                    "showShadow": false,
                                                    "showHeightWarn": false,
                                                    "warnHeight": 500,
                                                    "cameraType": "dy",
                                                    "followedX": 200,
                                                    "followedZ": 0,
                                                    "clockRange": false
                                                    },
                                            "type": "polyline"
                                         }
                        }
                }
                ]
           }
 }   
```



# /scenes/flights/driving

## URI

**URI**: /scenes/flights/driving

## 支持的方法

 GET 

## 介绍

 显示所有的驾驶路线

## HTTP方法

### GET

请求参数：

无

响应结构：

| 字段 | 类型 | 说明 |
|----|----|----|
|lineName  |String    | 路线名称   |
|type  |Number | 0 - 驾驶  1 - 飞行   |
|route |Object    |显示飞行的路线    |


返回示例：
```
{
 "jsonrpc":2.0,
 "id":null,
 "result":{
           "code":0,
           "message":"xxx",
           "data":[
                  {
                   "lineName":"驾驶路径名1",
                   "type":0,
                   "route":{
                            "id": "05141424192",
                            "name": "H匝道",
                            "geometry": {
                                        "type": "LineString",
                                        "coordinates": [
                                                        [120.677708264787,31.236798361041,14.9343952881172],
                                                        [120.706149620925,31.2429894333088,8.81605981942266]]
                                         },
                            "properties": {
                                            "edittype": "polyline",
                                            "name": "线",
                                            "position": {
                                                        "height": true,
                                                        "minCount": 2
                                                        },
                                            "style": {
                                                     "color": "#ffff00"
                                                     },
                                            "attr": {
                                                    "id": "05141424192",
                                                    "name": "H匝道",
                                                    "point": "",
                                                    "showLabel": false,
                                                    "showLine": false,
                                                    "showShadow": false,
                                                    "showHeightWarn": false,
                                                    "warnHeight": 500,
                                                    "cameraType": "dy",
                                                    "followedX": 200,
                                                    "followedZ": 0,
                                                    "clockRange": false
                                                    },
                                            "type": "polyline"
                                         }
                        }
                }]
           }
 }   

```



# /scenes/flights/flying

## URI

**URI**: /scenes/flights/flying

## 支持的方法

 GET 

## 介绍

 显示所有飞行路线 

## HTTP方法

### GET

请求参数：

无

响应结构：

| 字段 | 类型 | 说明 |
|----|----|----|
|lineName  |String    | 路线名称   |
|type  |Number | 0 - 驾驶  1 - 飞行   |
|route |Object    |显示飞行的路线    |

返回示例：

```
{
 "jsonrpc":2.0,
 "id":null,
 "result":{
           "code":0,
           "message":"xxx",
           "data":[
                  {
                   "lineName":"飞行路径名1",
                   "type":1,
                   "route":{
                            "id": "05141424192",
                            "name": "H匝道",
                            "geometry": {
                                        "type": "LineString",
                                        "coordinates": [
                                                        [120.677708264787,31.236798361041,14.9343952881172],
                                                        [120.706149620925,31.2429894333088,8.81605981942266]]
                                         },
                            "properties": {
                                            "edittype": "polyline",
                                            "name": "线",
                                            "position": {
                                                        "height": true,
                                                        "minCount": 2
                                                        },
                                            "style": {
                                                     "color": "#ffff00"
                                                     },
                                            "attr": {
                                                    "id": "05141424192",
                                                    "name": "H匝道",
                                                    "point": "",
                                                    "showLabel": false,
                                                    "showLine": false,
                                                    "showShadow": false,
                                                    "showHeightWarn": false,
                                                    "warnHeight": 500,
                                                    "cameraType": "dy",
                                                    "followedX": 200,
                                                    "followedZ": 0,
                                                    "clockRange": false
                                                    },
                                            "type": "polyline"
                                         }
                        }
                }]
           }
 }   

```

# /scenes/bookmarks

## URI

**URI**: /scenes/bookmarks

## 支持的方法

 GET 

## 介绍

 获取所有的系统级视角书签 

## HTTP方法

### GET

请求参数：

无

响应结构：

| 字段 | 类型 | 说明 |
|----|----|----|
|perspective  |String    | 视角   |


返回示例：
```
{
 "jsonrpc":2.0,
 "id":null,
 "result":{
           "code":0,
           "message":"xxx",
           "data":[]
          }        
 }   
```

# /projects/engineering_monitor/overview

# 接口 URI

## URI

**URI**: /projects/engineering_monitor/overview

## 支持的方法

GET

## 介绍

展示工程监测的统计信息:

1.显示监测点的总数

2.显示异常监测点的数量

3.显示活跃的监测点数

4.显示环境监测点数

## HTTP 方法

### GET

请求参数：

无

响应结构：

| 字段                              | 类型    | 说明                                      |
| --------------------------------- | ------- | ----------------------------------------- |
| title                             | String  | 接口标题                                  |
| monitorPositionCount              | Object  | 监测点总数                                |
| abnormalPositionCount             | Object  | 异常监测点数量                            |
| monitorPositionsWithValueCount    | Object  | 活跃监测点数量                            |
| noConstructMonitorPositionsCount  | Object  | 无构件监测点数量                          |
| abnormalPositions                 | Array   | 异常监测点                                |
| abnormalPositions.code            | String  | 监测点编码                                |
| abnormalPositions.monitorItemName | String  | 监测项目名称                              |
| abnormalPositions.new             | Boolean | 新增异常标志，true 代表为最新一期数据异常 |

返回示例：

```
{
 "jsonrpc":2.0,
 "id":null,
 "result":{
        "code":0,
        "message":"xxx",
        "data":{
            "title":"工程监测",
            "data":{
                "monitorPositionCount":{
                    "label":"监测点总数",
                    "value": 123
                },
                "abnormalPositionCount":{
                    "label":"异常监测点",
                    "value": 123
                },
                "monitorPositionsWithValueCount":{
                    "label":"带采集数据监测点",
                    "value": 123
                },
                "noConstructMonitorPositionsCount":{
                    "label":"无构件监测点",
                    "value": 123
                },
                "abnormalPositions": [{
                    "code": "ZQC10",
                    "monitorItemName": "监测项目的平移",
                    "new": false
                }]
            }
        }
    }
}
```

# /projects/progress/overview

## URI

**URI**: /projects/progress/overview

## 支持的方法

 GET

## 介绍

 根据实际完成构建时间和计划时间做对比，计算之后，最终获取当前工程进度完成情况

## HTTP方法

### GET

请求参数：

无


响应结构：

| 字段 | 类型 | 说明 |
|----|----|----|
|title |   String  |  接口标题|
|finishedConstructsCount |   Object |  已完成构件总数|
|finishedConstructsPercentage |   Object |  已完成构件比例|
|  finished  |  Object | 已完成，状态正常   |
|  inProgress|  Object | 运行中，状态正常   |
|  notStarted|  Object | 未启动，状态正常   |
|  planOverDue|  Object | 未启动，计划已过   |
|  inProgressOverDue  |  Object | 进行中，已逾期   |



返回示例：
```
{
 "jsonrpc":2.0,
 "id":null,
 "result":{
           "code":0,
           "message":"xxx",
           "data":{
                   "title":"工程进度",
                   "data":{
                           "finishedConstructsCount":{
                                                "label":"已完成构件总数",
                                                "value": 123
                                               },
                           "finishedConstructsPercentage":{
                                                "label":"已完成构件比例",
                                                "value": "60%"
                                               },
                          "finished":{
                                     "label":"已完成",
                                     "value": 123
                                   },
                          "inProgress":{
                                   "label":"施工中(未逾期)",
                                   "value": 123
                               },
                          "notStarted":{
                                        "label":"未启动(未逾期)",
                                        "value": 123
                                       },
                         "planOverDue":{
                                     "label":"未启动(已逾期)",
                                     "value": 123
                                   },
                          "inProgressOverDue":{
                                            "label":"施工中(已逾期)",
                                            "value": 123
                                           }
                              }
                 }   
          }
}
```