<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- VIEWS -->

        <!-- account.move.line (Journal items) -->
        <record id="view_move_line_form" model="ir.ui.view">
            <field name="name">account.move.line.form</field>
            <field name="model">account.move.line</field>
            <field eval="2" name="priority"/>
            <field name="arch" type="xml">
                <form string="Journal Item" create="false">
                    <sheet>
                        <field name="company_id" invisible="1"/>
                        <field name="parent_state" invisible="1"/>
                        <group>
                            <field name="name"/>
                            <field name="partner_id"
                                domain="['|', ('parent_id', '=', False), ('is_company', '=', True)]"
                                attrs="{'readonly': [('parent_state', '=', 'posted')]}"/>
                        </group>
                        <notebook colspan="4">
                            <page string="Information">
                                <group>
                                    <group string="Amount">
                                        <field name="account_id" options="{'no_create': True}" domain="[('company_id', '=', company_id)]" attrs="{'readonly':[('parent_state','=','posted')]}"/>
                                        <field name="debit" attrs="{'readonly':[('parent_state','=','posted')]}"/>
                                        <field name="credit" attrs="{'readonly':[('parent_state','=','posted')]}"/>
                                        <field name="quantity" attrs="{'readonly':[('parent_state','=','posted')]}"/>
                                    </group>
                                    <group string="Accounting Documents">
                                        <field name="move_id" attrs="{'readonly':[('parent_state','=','posted')]}"/>
                                        <field name="statement_id" readonly="True" attrs="{'invisible': [('statement_id','=',False)]}"/>
                                    </group>
                                    <group string="Dates">
                                        <field name="date" groups="account.group_account_user"/>
                                        <field name="date_maturity"/>
                                    </group>

                                    <group string="Taxes" attrs="{'invisible': [('tax_line_id','=',False), ('tax_ids','=',[])]}">
                                        <field name="tax_line_id" readonly="1" attrs="{'invisible': [('tax_line_id','=',False)]}"/>
                                        <field name="tax_ids" widget="many2many_tags" readonly="1" attrs="{'invisible': [('tax_ids','=',[])]}"/>
                                        <field name="tax_exigible" attrs="{'readonly':[('parent_state','=','posted')]}"/>
                                        <field name="tax_audit"/>
                                    </group>
                                    <group string="Matching" attrs="{'invisible':[('matched_debit_ids', '=', []),('matched_credit_ids', '=', [])]}">
                                        <label for="full_reconcile_id"/>
                                        <div>
                                            <field name="full_reconcile_id" attrs="{'invisible':[('full_reconcile_id','=',False)]}"/>
                                            <field name="matched_debit_ids" invisible="1"/>
                                            <field name="matched_credit_ids" invisible="1"/>
                                            <button name="open_reconcile_view"
                                                class="oe_link"
                                                type="object"
                                                string="-> View partially reconciled entries"
                                                attrs="{'invisible': ['|', ('full_reconcile_id', '!=', False), '&amp;', ('matched_debit_ids', '=', []),('matched_credit_ids', '=', [])]}">
                                            </button>
                                        </div>
                                    </group>
                                    <group string="Currency" groups="base.group_multi_currency">
                                        <field name="currency_id" invisible="1"/>
                                        <field name="amount_currency"/>
                                    </group>
                                    <group string="Product" attrs="{'invisible': [('product_id', '=', False)]}">
                                        <field name="product_id"/>
                                    </group>
                                    <group string="States">
                                        <field name="blocked"/>
                                    </group>
                                    <group string="Analytic" groups="analytic.group_analytic_accounting,analytic.group_analytic_tags">
                                        <field name="analytic_account_id" groups="analytic.group_analytic_accounting"
                                            domain="['|', ('company_id', '=', company_id), ('company_id', '=', False)]"
                                            attrs="{'readonly':[('parent_state','=','posted')]}"/>
                                        <field name="analytic_tag_ids" groups="analytic.group_analytic_tags"
                                            widget="many2many_tags"/>
                                    </group>
                                </group>
                            </page>
                            <page string="Analytic Lines" groups="analytic.group_analytic_accounting">
                                <field name="date" invisible="1"/>
                                <field name="analytic_line_ids" context="{'tree_view_ref':'analytic.view_account_analytic_line_tree', 'default_general_account_id':account_id, 'default_name': name, 'default_date':date, 'amount': (debit or 0.0)-(credit or 0.0)}"/>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="account_move_line_view_kanban" model="ir.ui.view">
            <field name="name">account.move.line.kanban</field>
            <field name="model">account.move.line</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile">
                    <field name="date_maturity"/>
                    <field name="move_id"/>
                    <field name="name"/>
                    <field name="partner_id"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div class="oe_kanban_global_click">
                                <div class="row mb4">
                                    <strong class="col-6">
                                        <span t-esc="record.move_id.value"/>
                                    </strong>
                                    <strong class="col-6 text-right">
                                        <i class="fa fa-clock-o" aria-label="Date" role="img" title="Date"/><field name="date_maturity"/>
                                    </strong>
                                    <div class="col-10">
                                        <span t-esc="record.name.value"/>
                                    </div>
                                    <div class="col-2 text-right">
                                        <img t-att-src="kanban_image('res.partner', 'image_128', record.partner_id.raw_value)" t-att-title="record.partner_id.value" t-att-alt="record.partner_id.value" class="oe_kanban_avatar o_image_24_cover"/>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record id="view_move_line_pivot" model="ir.ui.view">
            <field name="name">account.move.line.pivot</field>
            <field name="model">account.move.line</field>
            <field name="arch" type="xml">
                <pivot string="Journal Items">
                    <field name="journal_id" type="row"/>
                    <field name="date" type="col"/>
                    <field name="balance" type="measure"/>
                </pivot>
            </field>
        </record>

        <record id="view_move_line_tree" model="ir.ui.view">
            <field name="name">account.move.line.tree</field>
            <field name="model">account.move.line</field>
            <field eval="100" name="priority"/>
            <field name="arch" type="xml">
                <tree string="Journal Items" create="false" edit="false">
                    <field name="date" optional="show"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                    <field name="journal_id" options='{"no_open":True}'/>
                    <field name="move_id" optional="show"/>
                    <field name="account_id" options="{'no_open': True}" groups="account.group_account_user"/>
                    <field name="partner_id" optional="show"/>
                    <field name="statement_id" invisible="1"/>
                    <field name="ref" optional="show"/>
                    <field name="name" optional="show"/>
                    <field name="analytic_account_id" groups="account.group_account_user" optional="show"/>
                    <field name="analytic_tag_ids" widget="many2many_tags" groups="analytic.group_analytic_tags" optional="hide"/>
                    <field name="tax_ids" widget="many2many_tags" width="0.5" optional="show"/>
                    <field name="debit" sum="Total Debit"/>
                    <field name="credit" sum="Total Credit"/>
                    <field name="amount_currency" groups="base.group_multi_currency" optional="hide"/>
                    <field name="tag_ids" widget="many2many_tags" width="0.5" optional="hide"/>
                    <field name="full_reconcile_id" optional="show"/>
                    <field name="reconcile_model_id" invisible="1"/>
                    <field name="reconciled" invisible="1"/>
                    <field name="date_maturity" optional="hide"/>
                    <field name="currency_id" readonly="1" invisible="1"/>
                    <field name="company_currency_id" invisible="1"/>
                    <field name="parent_state" invisible="1"/>
                </tree>
            </field>
        </record>

        <record id="view_move_line_tree_grouped" model="ir.ui.view">
            <field name="name">account.move.line.tree.grouped</field>
            <field name="model">account.move.line</field>
            <field eval="1" name="priority"/>
            <field name="arch" type="xml">
                <tree string="Journal Items" create="false" expand="context.get('expand', False)" multi_edit="1">
                    <field name="date" optional="show" readonly="1"/>
                    <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}" optional="show"/>
                    <field name="move_id" optional="show"/>
                    <field name="account_id" optional="show" options="{'no_open': True, 'no_create': True}"
                           domain="[('company_id', '=', company_id)]"
                           groups="account.group_account_user"/>
                    <field name="statement_id" invisible="1"/>
                    <field name="partner_id" optional="show" readonly="1"/>
                    <field name="ref" optional="hide"/>
                    <field name="name"/>
                    <field name="reconciled" invisible="1"/>
                    <field name="full_reconcile_id" string="Matching" optional="show"/>
                    <field name="reconcile_model_id" invisible="1"/>
                    <field name="date_maturity" optional="hide"/>
                    <field name="debit" sum="Total Debit" readonly="1"/>
                    <field name="credit" sum="Total Credit" readonly="1"/>
                    <field name="amount_currency" readonly="1" groups="base.group_multi_currency"/>
                    <field name="currency_id" readonly="1" invisible="1" />
                    <field name="company_currency_id" invisible="1"/>
                    <field name="parent_state" invisible="1"/>
                    <field name="tax_ids" widget="many2many_tags" width="0.5" optional="hide" readonly="1"/>
                    <field name="tag_ids" widget="many2many_tags" width="0.5" optional="hide" string="Tax Grids"
                           options="{'no_open': True, 'no_create': True}"
                           domain="[('applicability', '=', 'taxes')]"/>
                    <groupby name="move_id">
                        <field name="state" invisible="1"/>
                        <button name="edit" type="edit" icon="fa-edit" title="Edit"/>
                        <button name="action_post" states="draft" icon="fa-check" title="Post" type="object" groups="account.group_account_invoice"/>
                        <button name="%(action_view_account_move_reversal)d" states="posted" title="Reverse" icon="fa-refresh" type="action" groups="account.group_account_invoice"/>
                        <button name="action_duplicate" icon="fa-files-o" title="Duplicate" type="object" groups="account.group_account_invoice"/>
                    </groupby>
                </tree>
            </field>
        </record>

        <record id="view_move_line_tree_grouped_sales_purchases" model="ir.ui.view">
            <field name="name">account.move.line.tree.grouped.sales.purchase</field>
            <field name="model">account.move.line</field>
            <field name="mode">primary</field>
            <field name="inherit_id" ref="account.view_move_line_tree_grouped"/>
            <field name="arch" type="xml">
                <field name="date" position="attributes"><attribute name="optional">hide</attribute></field>
                <field name="move_id" position="attributes"><attribute name="optional">hide</attribute></field>
                <field name="partner_id" position="attributes"><attribute name="optional">hide</attribute></field>
                <field name="full_reconcile_id" position="attributes"><attribute name="optional">hide</attribute></field>
                <field name="tax_ids" position="attributes"><attribute name="optional">show</attribute></field>
                <field name="tag_ids" position="attributes"><attribute name="optional">show</attribute></field>
            </field>
        </record>

        <record id="view_move_line_tree_grouped_bank_cash" model="ir.ui.view">
            <field name="name">account.move.line.tree.grouped.bank.cash</field>
            <field name="model">account.move.line</field>
            <field name="mode">primary</field>
            <field name="inherit_id" ref="account.view_move_line_tree_grouped"/>
            <field name="arch" type="xml">
                <field name="date" position="attributes"><attribute name="optional">hide</attribute></field>
                <field name="move_id" position="attributes"><attribute name="optional">hide</attribute></field>
                <field name="ref" position="attributes"><attribute name="optional">show</attribute></field>
                <field name="full_reconcile_id" position="attributes"><attribute name="optional">hide</attribute></field>
            </field>
        </record>

        <record id="view_move_line_tree_grouped_misc" model="ir.ui.view">
            <field name="name">account.move.line.tree.grouped.misc</field>
            <field name="model">account.move.line</field>
            <field name="mode">primary</field>
            <field name="inherit_id" ref="account.view_move_line_tree_grouped"/>
            <field name="arch" type="xml">
                <field name="date" position="attributes"><attribute name="optional">hide</attribute></field>
                <field name="move_id" position="attributes"><attribute name="optional">hide</attribute></field>
            </field>
        </record>

        <record id="view_move_line_tree_grouped_general" model="ir.ui.view">
            <field name="name">account.move.line.tree.grouped.misc</field>
            <field name="model">account.move.line</field>
            <field name="mode">primary</field>
            <field name="inherit_id" ref="account.view_move_line_tree_grouped"/>
            <field name="arch" type="xml">
                <field name="account_id" position="attributes">
                    <attribute name="optional">hide</attribute>
                    <attribute name="readonly">1</attribute>
                </field>
            </field>
        </record>


        <record id="view_move_line_tree_grouped_partner" model="ir.ui.view">
            <field name="name">account.move.line.tree.grouped.partner</field>
            <field name="model">account.move.line</field>
            <field name="mode">primary</field>
            <field name="inherit_id" ref="account.view_move_line_tree_grouped"/>
            <field name="arch" type="xml">
                <field name="partner_id" position="attributes"><attribute name="optional">hide</attribute></field>
                <field name="date_maturity" position="attributes"><attribute name="optional">show</attribute></field>
            </field>
        </record>

        <record id="view_move_line_tax_audit_tree" model="ir.ui.view">
            <field name="name">account.move.line.tax.audit.tree</field>
            <field name="model">account.move.line</field>
            <field name="mode">primary</field>
            <field name="inherit_id" ref="account.view_move_line_tree"/>
            <field name="arch" type="xml">
                <field name="full_reconcile_id" position="after">
                    <field name="tax_line_id" string="Tax"/>
                    <field name="tax_base_amount" sum="Total Base Amount"/>
                    <field name="tax_audit"/>
                    <field name="move_id"/>
                </field>
                <field name="date_maturity" position="replace"/>
                <field name="analytic_account_id" position="replace"/>
                <field name="full_reconcile_id" position="replace"/>
                <field name="debit" position="replace"/>
                <field name="credit" position="replace"/>
            </field>
        </record>

        <record id="account_move_line_graph_date" model="ir.ui.view">
            <field name="name">account.move.line.graph</field>
            <field name="model">account.move.line</field>
            <field name="arch" type="xml">
                <graph string="Account Statistics" type="bar">
                    <field name="date"/>
                    <field name="balance" operator="+" type='measure'/>
                </graph>
            </field>
        </record>

        <record id="view_account_move_line_filter" model="ir.ui.view">
            <field name="name">account.move.line.search</field>
            <field name="model">account.move.line</field>
            <field name="arch" type="xml">
                <search string="Search Journal Items">
                    <field name="name" string="Journal Item" filter_domain="[
                        '|', '|', '|',
                        ('name', 'ilike', self), ('ref', 'ilike', self), ('account_id', 'ilike', self), ('partner_id', 'ilike', self)]"/>
                    <field name="date"/>
                    <field name="account_id"/>
                    <field name="account_root_id"/>
                    <field name="account_internal_type"/>
                    <field name="partner_id"/>
                    <field name="journal_id"/>
                    <field name="move_id" string="Journal Entry" filter_domain="[
                        '|', '|', ('move_id.name', 'ilike', self), ('move_id.ref', 'ilike', self), ('move_id.partner_id', 'ilike', self)]"/>
                    <field name="tax_ids" />
                    <field name="tax_line_id" string="Originator Tax"/>
                    <field name="reconcile_model_id"/>
                    <separator/>
                    <filter string="Unposted" name="unposted" domain="[('move_id.state', '=', 'draft')]" help="Unposted Journal Items"/>
                    <filter string="Posted" name="posted" domain="[('move_id.state', '=', 'posted')]" help="Posted Journal Items"/>
                    <separator/>
                    <filter string="To Check" name="to_check" domain="[('move_id.to_check', '=', True)]"/>
                    <separator/>
                    <filter string="Unreconciled" domain="[('full_reconcile_id', '=', False), ('balance', '!=', 0), ('account_id.reconcile', '=', True)]" help="Journal items where matching number isn't set" name="unreconciled"/>
                    <separator/>
                    <filter string="Sales" name="sales" domain="[('move_id.journal_id.type', '=', 'sale')]" context="{'default_journal_type': 'sale'}"/>
                    <filter string="Purchases" name="purchases" domain="[('move_id.journal_id.type', '=', 'purchase')]" context="{'default_journal_type': 'purchase'}"/>
                    <filter string="Bank" name="bank" domain="[('move_id.journal_id.type', '=', 'bank')]" context="{'default_journal_type': 'bank'}"/>
                    <filter string="Cash" name="cash" domain="[('move_id.journal_id.type', '=', 'cash')]" context="{'default_journal_type': 'cash'}"/>
                    <filter string="Miscellaneous" domain="[('move_id.journal_id.type', '=', 'general')]" name="misc_filter" context="{'default_journal_type': 'general'}"/>
                    <separator/>
                    <filter string="Payable" domain="[('account_id.internal_type', '=', 'payable')]" help="From Payable accounts" name="payable"/>
                    <filter string="Receivable" domain="[('account_id.internal_type', '=', 'receivable')]" help="From Receivable accounts" name="receivable"/>
                    <separator/>
                    <filter string="Date" name="date" date="date"/>
                    <separator/>
                    <group expand="0" string="Group By">
                        <filter string="Journal Entry" name="group_by_move" domain="[]" context="{'group_by': 'move_id'}"/>
                        <filter string="Account" name="group_by_account" domain="[]" context="{'group_by': 'account_id'}"/>
                        <filter string="Partner" name="group_by_partner" domain="[]" context="{'group_by': 'partner_id'}"/>
                        <filter string="Journal" name="journal" domain="[]" context="{'group_by': 'journal_id'}"/>
                        <filter string="Date" name="groupby_date" domain="[]" context="{'group_by': 'date'}"/>
                    </group>
                </search>
            </field>
        </record>
        <record id="view_account_move_line_filter_with_root_selection" model="ir.ui.view">
            <field name="name">account.move.line.search</field>
            <field name="model">account.move.line</field>
            <field name="mode">primary</field>
            <field name="priority">20</field>
            <field name="inherit_id" ref="account.view_account_move_line_filter"/>
            <field name="arch" type="xml">
                <xpath expr="//group" position="after">
                    <searchpanel class="account_root">
                        <field name="account_root_id" icon="fa-filter"/>
                    </searchpanel>
                </xpath>
            </field>
        </record>

        <!-- account.move (Journal Entry) -->

        <record id="view_move_tree" model="ir.ui.view">
            <field name="name">account.move.tree</field>
            <field name="model">account.move</field>
            <field name="arch" type="xml">
                <tree decoration-info="state == 'draft'" string="Journal Entries" decoration-muted="state == 'cancel'">
                    <field name="date"/>
                    <field name="name"/>
                    <field name="partner_id" optional="show"/>
                    <field name="ref" optional="show"/>
                    <field name="journal_id"/>
                    <field name="company_id" groups="base.group_multi_company" optional="show"/>
                    <field name="amount_total_signed" sum="Total Amount" string="Total"/>
                    <field name="state"/>
                    <field name="currency_id" invisible="1"/>
                </tree>
            </field>
        </record>

        <record id="view_invoice_tree" model="ir.ui.view">
            <field name="name">account.invoice.tree</field>
            <field name="model">account.move</field>
            <field name="arch" type="xml">
                <tree string="Invoices"
                      decoration-info="state == 'draft'"
                      decoration-muted="state == 'cancel'"
                      banner_route="/account/account_invoice_onboarding"
                      js_class="account_tree">
                    <field name="name"/>
                    <field name="partner_id" invisible="1"/>
                    <field name="invoice_source_email" invisible="1"/>
                    <field name="invoice_partner_icon" string=" "/>
                    <field name="invoice_partner_display_name" invisible="context.get('default_type') not in ('in_invoice', 'in_refund','in_receipt')" groups="base.group_user" string="Vendor" />
                    <field name="invoice_partner_display_name" invisible="context.get('default_type') not in ('out_invoice', 'out_refund','out_receipt')" groups="base.group_user" string="Customer" />
                    <field name="invoice_date" optional="show" invisible="context.get('default_type') not in ('in_invoice', 'in_refund','in_receipt')" string="Bill Date"/>
                    <field name="invoice_date" optional="show" invisible="context.get('default_type') not in ('out_invoice', 'out_refund','out_receipt')" string="Invoice Date"/>
                    <field name="invoice_origin" optional="show" string="Source Document"/>
                    <field name="invoice_payment_ref" optional="hide" invisible="context.get('default_type') in ('out_invoice', 'out_refund','out_receipt')"/>
                    <field name="ref" optional="hide"/>
                    <field name="invoice_user_id" optional="show" invisible="context.get('default_type') not in ('out_invoice', 'out_refund','out_receipt')" string="Sales Person"/>
                    <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}" optional="show"/>
                    <field name="invoice_date_due" optional="show"/>
                    <field name="amount_untaxed_signed" string="Tax Excluded" sum="Total" optional="show"/>
                    <field name="amount_tax_signed" string="Tax" sum="Total" optional="hide"/>
                    <field name="amount_total_signed" string="Total" sum="Total" optional="show"/>
                    <field name="amount_residual_signed" string="Amount Due" sum="Amount Due" optional="show"/>
                    <field name="currency_id" invisible="1"/>
                    <field name="company_currency_id" invisible="1"/>
                    <field name="state" optional="show"/>
                    <field name="invoice_payment_state" optional="hide"/>
                    <field name="type" invisible="context.get('default_type', True)"/>
                </tree>
            </field>
        </record>

        <record id="view_account_move_kanban" model="ir.ui.view">
            <field name="name">account.move.kanban</field>
            <field name="model">account.move</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile">
                    <field name="journal_id"/>
                    <field name="partner_id"/>
                    <field name="ref"/>
                    <field name="date"/>
                    <field name="state"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div t-attf-class="oe_kanban_global_click">
                                <div class="row mb4">
                                    <div class="col-6 o_kanban_record_headings">
                                        <strong>
                                            <span>
                                                <field name="partner_id" attrs="{'invisible': [('partner_id', '=', False)]}" />
                                                <field name="journal_id" attrs="{'invisible': [('partner_id', '!=', False)]}" />
                                            </span>
                                        </strong>
                                    </div>
                                    <div class="col-6 text-right">
                                        <strong><i class="fa fa-clock-o" aria-label="Date" role="img" title="Date"/> <t t-esc="record.date.value"/></strong>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <span><field name="ref"/></span>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-6">
                                        <span><field name="amount_total" widget='monetary'/></span>
                                        <span><field name="currency_id" invisible="1"/></span>
                                    </div>
                                    <div class="col-6">
                                        <span class="float-right">
                                            <field name="state" widget="label_selection" options="{'classes': {'draft': 'default', 'posted': 'success'}}"/>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record id="view_move_form" model="ir.ui.view">
            <field name="name">account.move.form</field>
            <field name="model">account.move</field>
            <field name="arch" type="xml">
                <form string="Account Entry">
                    <header>
                        <!-- Post -->
                        <button name="action_post" string="Post" class="oe_highlight"
                                type="object" groups="account.group_account_invoice"
                                attrs="{'invisible': [('state', '!=', 'draft')]}"/>
                        <!-- Send (only invoices) -->
                        <button name="action_invoice_sent"
                                type="object"
                                string="Send &amp; Print"
                                attrs="{'invisible':['|', '|', ('state', '!=', 'posted'), ('invoice_sent', '=', True), ('type', 'not in', ('out_invoice', 'out_refund'))]}"
                                class="oe_highlight"
                                groups="base.group_user"/>
                        <button name="action_invoice_sent"
                                type="object"
                                string="Send &amp; Print"
                                attrs="{'invisible':['|', '|', ('state', '!=', 'posted'), ('invoice_sent', '=', False), ('type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund'))]}"
                                groups="base.group_user"/>
                        <!-- Register Payment (only invoices / receipts) -->
                        <button name="action_invoice_register_payment" id="account_invoice_payment_btn"
                                type="object" class="oe_highlight"
                                attrs="{'invisible': ['|', '|', ('state', '!=', 'posted'), ('invoice_payment_state', '!=', 'not_paid'), ('type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt'))]}"
                                string="Register Payment"
                                groups="account.group_account_invoice"/>
                        <!-- Preview (only customer invoices) -->
                        <button name="preview_invoice" type="object" string="Preview"
                                attrs="{'invisible': [('type', 'not in', ('out_invoice', 'out_refund'))]}"/>
                        <!-- Reverse -->
                        <button name="%(action_view_account_move_reversal)d" string="Reverse Entry"
                                type="action" groups="account.group_account_invoice"
                                attrs="{'invisible': ['|', ('type', '!=', 'entry'), ('state', '!=', 'posted')]}"/>
                        <button name="action_reverse" string='Add Credit Note'
                                type='object' groups="account.group_account_invoice"
                                attrs="{'invisible': ['|', ('type', 'not in', ('out_invoice', 'in_invoice')), ('state', '!=', 'posted')]}"/>
                        <!-- Duplicate -->
                        <button name="action_duplicate" string="Duplicate" type="object" groups="account.group_account_invoice"
                                attrs="{'invisible': [('type', '!=', 'entry')]}"/>
                        <!-- Cancel -->
                        <button name="button_cancel" string="Cancel Entry" type="object" groups="account.group_account_invoice"
                                attrs="{'invisible' : ['|', ('id', '=', False), ('state', '!=', 'draft')]}"/>
                        <button name="button_draft" string="Reset to Draft" type="object" groups="account.group_account_invoice"
                                attrs="{'invisible' : ['|', ('restrict_mode_hash_table', '=', True), ('state', 'not in', ('posted', 'cancel'))]}"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,posted"/>
                    </header>
                    <!-- Invoice outstanding credits -->
                    <div groups="account.group_account_invoice"
                         class="alert alert-warning" role="alert" style="margin-bottom:0px;"
                         attrs="{'invisible': ['|', ('state', '!=', 'draft'), ('tax_lock_date_message', '=', False)]}">
                        <field name="tax_lock_date_message" nolabel="1"/>
                    </div>
                    <div groups="account.group_account_invoice"
                         class="alert alert-info" role="alert" style="margin-bottom:0px;"
                         attrs="{'invisible': ['|', '|', ('type', 'not in', ('out_invoice', 'out_refund')), ('invoice_has_outstanding', '=', False), ('invoice_payment_state', '!=', 'not_paid')]}">
                        You have <bold><a class="alert-link" href="#outstanding" role="button">outstanding payments</a></bold> for this customer. You can allocate them to mark this invoice as paid.
                    </div>
                    <div groups="account.group_account_invoice"
                         class="alert alert-info" role="alert" style="margin-bottom:0px;"
                         attrs="{'invisible': ['|', '|', ('type', 'not in', ('in_invoice', 'in_refund')), ('invoice_has_outstanding', '=', False), ('invoice_payment_state', '!=', 'not_paid')]}">
                        You have <bold><a class="alert-link" href="#outstanding" role="button">outstanding debits</a></bold> for this supplier. You can allocate them to mark this bill as paid.
                    </div>
                    <!-- Invoice suspense accounts -->
                    <div groups="account.group_account_invoice" class="alert alert-info" role="alert" style="margin-bottom:0px;"
                         attrs="{'invisible': ['|', ('type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt')), ('invoice_has_matching_suspense_amount','=',False)]}">
                        You have suspense account moves that match this invoice. <bold><button class="alert-link" type="object" name="action_open_matching_suspense_moves" role="button" string="Check them" style="padding: 0;vertical-align: baseline;"/></bold> to mark this invoice as paid.
                    </div>
                    <sheet>
                        <div name="button_box" class="oe_button_box">
                            <button name="open_reconcile_view"
                                    class="oe_stat_button"
                                    icon="fa-bars"
                                    type="object"
                                    attrs="{'invisible': ['|', '|', ('type', '!=', 'entry'), ('id', '=', False), ('has_reconciled_entries', '=', False)]}"
                                    string="Reconciled Entries">
                            </button>
                        </div>

                        <!-- Payment status for invoices / receipts -->
                        <widget name="web_ribbon" title="Paid"
                                attrs="{'invisible': ['|', ('invoice_payment_state', '!=', 'paid'), ('type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt'))]}"/>
                        <widget name="web_ribbon" title="In Payment"
                                attrs="{'invisible': ['|', ('invoice_payment_state', '!=', 'in_payment'), ('type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt'))]}"/>

                        <!-- Invisible fields -->
                        <field name="id" invisible="1"/>
                        <field name="type" invisible="1"/>
                        <field name="invoice_payment_state" invisible="1" force_save="1"/>
                        <field name="invoice_filter_type_domain" invisible="1"/>
                        <field name="company_currency_id" invisible="1"/>
                        <field name="commercial_partner_id" invisible="1"/>
                        <field name="bank_partner_id" invisible="1"/>

                        <field name="invoice_has_outstanding" invisible="1"/>
                        <field name="invoice_sent" invisible="1"/>
                        <field name="invoice_sequence_number_next_prefix" invisible="1"/>
                        <field name="invoice_sequence_number_next" invisible="1"/>
                        <field name="invoice_has_matching_suspense_amount" invisible="1"/>
                        <field name="has_reconciled_entries" invisible="1"/>
                        <field name="restrict_mode_hash_table" invisible="1"/>

                        <div>
                            <!-- Invoice draft header -->
                            <span class="o_form_label"><field name="type" attrs="{'invisible': ['|', ('type', '=', 'entry'), ('state', '=', 'draft')]}" readonly="1" nolabel="1"/></span>
                            <h1>
                                <span attrs="{'invisible': ['|', '|', ('type', '!=', 'out_invoice'), ('state', '!=', 'draft'), ('name', '!=', '/')]}">Draft Invoice</span>
                                <span attrs="{'invisible': ['|', '|', ('type', '!=', 'out_refund'), ('state', '!=', 'draft'), ('name', '!=', '/')]}">Draft Credit Note</span>
                                <span attrs="{'invisible': ['|', '|', ('type', '!=', 'in_invoice'), ('state', '!=', 'draft'), ('name', '!=', '/')]}">Draft Bill</span>
                                <span attrs="{'invisible': ['|', '|', ('type', '!=', 'in_refund'), ('state', '!=', 'draft'), ('name', '!=', '/')]}">Draft Refund</span>
                                <span attrs="{'invisible': ['|', '|', ('type', '!=', 'out_receipt'), ('state', '!=', 'draft'), ('name', '!=', '/')]}">Draft Sales Receipt</span>
                                <span attrs="{'invisible': ['|', '|', ('type', '!=', 'in_receipt'), ('state', '!=', 'draft'), ('name', '!=', '/')]}">Draft Purchase Receipt</span>
                            </h1>

                            <!-- Select next number header (only invoices) -->
                            <span class="o_form_label" attrs="{'invisible': [('invoice_sequence_number_next_prefix', '=', False)]}">First Number:</span>

                            <!-- Number -->
                            <h1 class="mt0">
                                <field name="name" readonly="True" attrs="{'invisible':[('name', '=', '/')]}"/>
                            </h1>

                            <!-- Select next number header (only invoices) -->
                            <div  attrs="{'invisible': [('invoice_sequence_number_next_prefix', '=', False)]}">
                                <field name="invoice_sequence_number_next_prefix" class="oe_inline"/>
                                <field name="invoice_sequence_number_next" class="oe_inline"/>
                            </div>
                        </div>
                        <group>
                            <group id="header_left_group">
                                <div class="o_td_label">
                                    <label for="partner_id" string="Customer" style="font-weight:bold;"
                                           attrs="{'invisible': [('type', 'not in', ('out_invoice', 'out_refund', 'out_receipt'))]}"/>
                                    <label for="partner_id" string="Vendor" style="font-weight:bold;"
                                           attrs="{'invisible': [('type', 'not in', ('in_invoice', 'in_refund', 'in_receipt'))]}"/>
                                </div>
                                <field name="partner_id" widget="res_partner_many2one" nolabel="1"
                                       context="{
                                            'res_partner_search_mode': (context.get('default_type', 'entry') in ('out_invoice', 'out_refund', 'out_receipt') and 'customer') or (context.get('default_type', 'entry') in ('in_invoice', 'in_refund', 'in_receipt') and 'supplier') or False,
                                            'show_address': 1, 'default_is_company': True, 'show_vat': True}"
                                       options='{"always_reload": True, "no_quick_create": True}'
                                       attrs="{'invisible': [('type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt'))]}"/>

                                <field name="ref"/>
                                <field name="invoice_vendor_bill_id"
                                       attrs="{'invisible': ['|', ('state', '!=', 'draft'), ('type', '!=', 'in_invoice')]}"
                                       domain="[('company_id', '=', company_id), ('partner_id','child_of', [partner_id]), ('type','=','in_invoice')]"
                                       string="Auto-Complete" placeholder="Select an old vendor bill"
                                       options="{'no_create': True}" context="{'show_total_amount': True}"/>
                            </group>
                            <group id="header_right_group">
                                <!-- Invoice date (only invoices / receipts) -->
                                <div class="o_td_label">
                                    <label for="invoice_date" string="Invoice Date" style="font-weight:bold;"
                                           attrs="{'invisible': [('type', 'not in', ('out_invoice', 'out_refund', 'out_receipt'))]}"/>
                                    <label for="invoice_date" string="Bill Date" style="font-weight:bold;"
                                           attrs="{'invisible': [('type', 'not in', ('in_invoice', 'in_refund', 'in_receipt'))]}"/>
                                </div>
                                <field name="invoice_date" nolabel="1" options="{'datepicker': {'warn_future': true}}"
                                       attrs="{'invisible': [('type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt'))]}"/>

                                <field name="date" string="Accounting Date"
                                       attrs="{'invisible': [('type', 'in', ('out_invoice', 'out_refund', 'out_receipt'))]}"/>

                                <!-- Invoice payment terms (only invoices) + due date (only invoices / receipts) -->
                                <label for="invoice_payment_term_id"
                                       attrs="{'invisible': [('type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt'))]}"/>
                                <div class="d-flex" attrs="{'invisible': [('type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt'))]}">
                                    <field name="invoice_payment_term_id"
                                           placeholder="Terms"
                                           attrs="{'invisible': [('type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt'))]}"/>
                                    <span class="o_form_label mx-3 oe_edit_only"
                                          attrs="{'invisible': [ '|', '|', '|', ('state', '!=', 'draft'), ('invoice_payment_term_id', '!=', False), ('type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt'))]}"> or </span>
                                    <field name="invoice_date_due" force_save="1"
                                           placeholder="Date"
                                           attrs="{'invisible': ['|', ('invoice_payment_term_id', '!=', False), ('type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt'))]}"/>
                                </div>

                                <field name="journal_id"
                                       invisible="context.get('default_journal_id') and context.get('type', 'entry') != 'entry'"
                                       groups="account.group_account_user"
                                       options="{'no_create': True}"
                                       domain="[('type', '=?', invoice_filter_type_domain)]"
                                       attrs="{'readonly': [('name', '!=', '/')]}"/>

                                <field name="company_id" groups="base.group_multi_company"/>

                                <field name="currency_id"
                                       groups="base.group_multi_currency"
                                       attrs="{'invisible': [('type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund'))]}"/>
                            </group>
                        </group>
                        <notebook>
                            <page id="invoice_tab"
                                  string="Invoice Lines"
                                  attrs="{'invisible': [('type', '=', 'entry')]}">
                                <field name="invoice_line_ids"
                                       widget="section_and_note_one2many"
                                       mode="tree,kanban"
                                       context="{'default_type': context.get('default_type'), 'journal_id': journal_id, 'default_partner_id': commercial_partner_id, 'default_currency_id': currency_id != company_currency_id and currency_id or False}">
                                    <tree editable="bottom" string="Journal Items" default_order="sequence, date desc, move_name desc, id">
                                        <control>
                                            <create name="add_line_control" string="Add a line"/>
                                            <create name="add_section_control" string="Add a section" context="{'default_display_type': 'line_section'}"/>
                                            <create name="add_note_control" string="Add a note" context="{'default_display_type': 'line_note'}"/>
                                        </control>

                                        <!-- Displayed fields -->
                                        <field name="sequence" widget="handle"/>
                                        <field name="move_name" invisible="1"/>
                                        <field name="product_id"
                                               optional="show"
                                               domain="
                                                    context.get('default_type') in ('out_invoice', 'out_refund', 'out_receipt')
                                                    and [('sale_ok', '=', True), '|', ('company_id', '=', False), ('company_id', '=', parent.company_id)]
                                                    or [('purchase_ok', '=', True), '|', ('company_id', '=', False), ('company_id', '=', parent.company_id)]
                                               "/>
                                        <field name="name" widget="section_and_note_text" attrs="{'required': [('parent.type', 'not in', ('in_invoice', 'in_refund','in_receipt'))]}"/>
                                        <field name="account_id"
                                               groups="account.group_account_user"
                                               domain="[('deprecated', '=', False), ('user_type_id.type', 'not in', ('receivable', 'payable')), ('company_id', '=', parent.company_id)]"
                                               attrs="{'required': [('display_type', '=', False)]}"/>
                                        <field name="analytic_account_id"
                                               domain="['|', ('company_id', '=', False), ('company_id', '=', parent.company_id)]"
                                               groups="analytic.group_analytic_accounting"
                                               optional="show"/>
                                        <field name="analytic_tag_ids"
                                               domain="['|', ('company_id', '=', False), ('company_id', '=', parent.company_id)]"
                                               groups="analytic.group_analytic_tags"
                                               optional="show"
                                               widget="many2many_tags"/>
                                        <field name="quantity"/>
                                        <field name="product_uom_id" string="UoM" groups="uom.group_uom" optional="show"/>
                                        <field name="price_unit" string="Price"/>
                                        <field name="discount" string="Disc.%" groups="base.group_no_one" optional="show"/>
                                        <field name="tax_ids" widget="many2many_tags"
                                               domain="[('type_tax_use', '=?', parent.invoice_filter_type_domain), ('company_id', '=', parent.company_id)]"
                                               context="{'append_type_to_tax_name': not parent.invoice_filter_type_domain}"
                                               options="{'no_create': True}"
                                               optional="show"/>
                                        <field name="price_subtotal"
                                               string="Subtotal"
                                               groups="account.group_show_line_subtotals_tax_excluded"/>
                                        <field name="price_total"
                                               string="Total"
                                               groups="account.group_show_line_subtotals_tax_included"/>

                                        <!-- Others fields -->
                                        <field name="partner_id" invisible="1"/>
                                        <field name="amount_currency" invisible="1"/>
                                        <field name="currency_id" invisible="1"/>
                                        <field name="debit" invisible="1"/>
                                        <field name="credit" invisible="1"/>
                                        <field name="date" invisible="1"/>
                                        <field name="date_maturity" invisible="1"/>

                                        <field name="tax_line_id" invisible="1"/>
                                        <field name="tax_repartition_line_id" invisible="1"/>
                                        <field name="tag_ids" invisible="1"/>
                                        <field name="tax_base_amount" invisible="1"/>
                                        <field name="tax_exigible" invisible="1"/>
                                        <field name="company_id" invisible="1"/>
                                        <field name="company_currency_id" invisible="1"/>
                                        <field name="recompute_tax_line" invisible="1" force_save="1"/>
                                        <field name="display_type" force_save="1" invisible="1"/>
                                        <field name="is_rounding_line" invisible="1"/>
                                        <field name="exclude_from_invoice_tab" invisible="1"/>
                                        <field name="account_internal_type" invisible="1"/>
                                        <field name="always_set_currency_id" invisible="1"/>
                                    </tree>
                                    <kanban class="o_kanban_mobile">
                                        <!-- Displayed fields -->
                                        <field name="name"/>
                                        <field name="product_id"/>
                                        <field name="price_subtotal" groups="account.group_show_line_subtotals_tax_excluded"/>
                                        <field name="price_total" groups="account.group_show_line_subtotals_tax_included"/>
                                        <field name="quantity"/>
                                        <field name="product_uom_id" groups="uom.group_uom"/>
                                        <field name="price_unit"/>
                                        <field name="display_type"/>
                                        <templates>
                                            <t t-name="kanban-box">
                                                <div t-attf-class="oe_kanban_card oe_kanban_global_click {{ record.display_type.raw_value ? 'o_is_' + record.display_type.raw_value : '' }}">
                                                    <t t-if="!record.display_type.raw_value">
                                                        <div class="row">
                                                            <div class="col-2">
                                                                <img t-att-src="kanban_image('product.product', 'image_128', record.product_id.raw_value)" t-att-title="record.product_id.value" t-att-alt="record.product_id.value" style="max-width: 100%;"/>
                                                            </div>
                                                            <div class="col-10">
                                                                <strong>
                                                                    <span>
                                                                        <t t-esc="record.product_id.value"/>
                                                                    </span>
                                                                </strong>
                                                                <div class="text-muted">
                                                                    Quantity:
                                                                    <t t-esc="record.quantity.value"/>
                                                                    <t t-esc="record.product_uom_id.value"/>
                                                                </div>
                                                                <div class="text-muted">
                                                                    Unit Price:
                                                                    <t t-esc="record.price_unit.value"/>
                                                                </div>
                                                                <strong>
                                                                    <span class="float-right text-right">
                                                                        <t t-esc="record.price_subtotal.value" groups="account.group_show_line_subtotals_tax_excluded"/>
                                                                        <t t-esc="record.price_total.value" groups="account.group_show_line_subtotals_tax_included"/>
                                                                    </span>
                                                                </strong>
                                                            </div>
                                                        </div>
                                                    </t>
                                                    <t t-if="record.display_type.raw_value === 'line_section' || record.display_type.raw_value === 'line_note'">
                                                        <div class="row">
                                                            <div class="col-12">
                                                                <span>
                                                                    <t t-esc="record.name.value"/>
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </t>
                                                </div>
                                            </t>
                                        </templates>

                                        <!-- Others fields -->
                                        <field name="partner_id" invisible="1"/>
                                        <field name="amount_currency" invisible="1"/>
                                        <field name="currency_id" invisible="1"/>
                                        <field name="debit" invisible="1"/>
                                        <field name="credit" invisible="1"/>
                                        <field name="date" invisible="1"/>
                                        <field name="date_maturity" invisible="1"/>

                                        <field name="tax_line_id" invisible="1"/>
                                        <field name="tax_repartition_line_id" invisible="1"/>
                                        <field name="tag_ids" invisible="1"/>
                                        <field name="tax_base_amount" invisible="1"/>
                                        <field name="tax_exigible" invisible="1"/>
                                        <field name="company_id" invisible="1"/>
                                        <field name="company_currency_id" invisible="1"/>
                                        <field name="recompute_tax_line" invisible="1" force_save="1"/>
                                        <field name="display_type" force_save="1" invisible="1"/>
                                        <field name="is_rounding_line" invisible="1"/>
                                        <field name="exclude_from_invoice_tab" invisible="1"/>
                                        <field name="account_internal_type" invisible="1"/>
                                        <field name="always_set_currency_id" invisible="1"/>
                                    </kanban>
                                    <form>
                                        <sheet>
                                            <field name="display_type" invisible="1"/>
                                            <field name="parent_state" invisible="1"/>
                                            <group>
                                                <field name="partner_id" invisible="1"/>
                                                <field name="company_id" invisible="1"/>
                                                <field name="product_id" widget="many2one_barcode"/>
                                                <field name="quantity"/>
                                                <field name="product_uom_id" groups="uom.group_uom"/>
                                                <field name="price_unit"/>
                                                <field name="discount" groups="product.group_discount_per_so_line"/>
                                                <field name="currency_id" invisible="1"/>
                                            </group>
                                            <group>
                                                <field name="analytic_tag_ids" groups="analytic.group_analytic_tags" widget="many2many_tags"/>
                                                <field name="account_id" options="{'no_create': True}" domain="[('company_id', '=', company_id)]" attrs="{'readonly':[('parent_state','=','posted')]}"/>
                                                <field name="tax_ids" widget="many2many_tags"/>
                                                <field name="analytic_account_id" groups="analytic.group_analytic_accounting"/>
                                            </group>
                                            <label for="name" string="Description" attrs="{'invisible': [('display_type', '!=', False)]}"/>
                                            <label for="name" string="Section" attrs="{'invisible': [('display_type', '!=', 'line_section')]}"/>
                                            <label for="name" string="Note" attrs="{'invisible': [('display_type', '!=', 'line_note')]}"/>
                                            <field name="name" widget="text"/>
                                        </sheet>
                                    </form>
                                </field>

                                <!-- Totals (only invoices / receipts) -->
                                <group class="oe_subtotal_footer"
                                    attrs="{'invisible': [('type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt'))]}">
                                    <field name="amount_untaxed"/>
                                    <field name="amount_by_group" widget="tax-group-custom-field" nolabel="1" colspan="2"/>
                                    <field name="amount_total" class="oe_subtotal_footer_separator"/>
                                    <field name="invoice_payments_widget" colspan="2" nolabel="1" widget="payment"/>
                                    <field name="amount_residual" class="oe_subtotal_footer_separator" attrs="{'invisible': [('state', '=', 'draft')]}"/>
                                </group>

                                <field name="invoice_outstanding_credits_debits_widget"
                                    class="oe_invoice_outstanding_credits_debits"
                                    colspan="2" nolabel="1" widget="payment"
                                    attrs="{'invisible': ['|', ('state', '!=', 'posted'), ('type', 'in', ('out_receipt', 'in_receipt'))]}"/>

                                <!-- Internal note -->
                                <field name="narration" placeholder="Add an internal note..." nolabel="1" height="50"/>
                            </page>
                            <page id="aml_tab" string="Journal Items" groups="account.group_account_user">
                                <field name="line_ids"
                                       context="{'default_type': context.get('default_type'), 'line_ids': line_ids, 'journal_id': journal_id, 'default_partner_id': commercial_partner_id, 'default_currency_id': currency_id != company_currency_id and currency_id or False}">
                                    <tree editable="bottom" string="Journal Items" decoration-muted="display_type in ('line_section', 'line_note')" default_order="sequence, date desc, move_name desc, id">
                                        <!-- Displayed fields -->
                                        <field name="account_id"
                                               attrs="{
                                                    'required': [('display_type', 'not in', ('line_section', 'line_note'))],
                                                    'invisible': [('display_type', 'in', ('line_section', 'line_note'))],
                                               }"
                                               domain="[('deprecated', '=', False), ('company_id', '=', parent.company_id)]" />
                                        <field name="partner_id"
                                               domain="['|', ('parent_id', '=', False), ('is_company', '=', True)]"
                                               attrs="{'column_invisible': [('parent.type', '!=', 'entry')]}"/>
                                        <field name="name" widget="section_and_note_text" optional="show"/>
                                        <field name="analytic_account_id"
                                               optional="hide"
                                               domain="['|', ('company_id', '=', parent.company_id), ('company_id', '=', False)]"
                                               groups="analytic.group_analytic_accounting"
                                               attrs="{'invisible': [('display_type', 'in', ('line_section', 'line_note'))]}"/>
                                        <field name="analytic_tag_ids"
                                               optional="show"
                                               groups="analytic.group_analytic_tags"
                                               widget="many2many_tags"
                                               attrs="{'invisible': [('display_type', 'in', ('line_section', 'line_note'))]}"/>
                                        <field name="date_maturity"
                                               optional="hide"
                                               invisible="context.get('view_no_maturity')"
                                               attrs="{'invisible': [('display_type', 'in', ('line_section', 'line_note'))]}"/>
                                        <field name="amount_currency"
                                               groups="base.group_multi_currency"
                                               optional="hide"/>
                                        <field name="currency_id" options="{'no_create': True}"
                                               groups="base.group_multi_currency"
                                               attrs="{'column_invisible': [('parent.type', '!=', 'entry')]}"/>
                                        <field name="tax_ids" widget="many2many_tags"
                                               optional="hide"
                                               domain="[('type_tax_use', '=?', parent.invoice_filter_type_domain)]"
                                               context="{'append_type_to_tax_name': not parent.invoice_filter_type_domain}"
                                               options="{'no_create': True}"
                                               force_save="1"
                                               attrs="{'readonly': [
                                                    '|', '|',
                                                    ('display_type', 'in', ('line_section', 'line_note')),
                                                    ('tax_line_id', '!=', False),
                                                    '&amp;',
                                                    ('parent.type', 'in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt')),
                                                    ('account_internal_type', 'in', ('receivable', 'payable')),
                                                ]}"/>
                                        <field name="debit"
                                               sum="Total Debit"
                                               attrs="{'invisible': [('display_type', 'in', ('line_section', 'line_note'))]}"/>
                                        <field name="credit"
                                               sum="Total Credit"
                                               attrs="{'invisible': [('display_type', 'in', ('line_section', 'line_note'))]}"/>
                                        <field name="country_id" invisible="1"/>
                                        <field name="tag_ids" widget="many2many_tags" string="Tax Grids" optional="show" domain="[('country_id', '=', country_id), ('applicability', '=', 'taxes')]"/>

                                        <!-- Others fields -->
                                        <field name="payment_id" invisible="1"/>

                                        <field name="product_id" invisible="1"/>
                                        <field name="quantity" invisible="1"/>
                                        <field name="product_uom_id" invisible="1"/>
                                        <field name="price_unit" invisible="1"/>
                                        <field name="discount" invisible="1"/>
                                        <field name="price_subtotal" invisible="1"/>
                                        <field name="price_total" invisible="1"/>

                                        <field name="sequence" invisible="1"/>
                                        <field name="move_name" invisible="1"/>
                                        <field name="date" invisible="1"/>
                                        <field name="tax_line_id" invisible="1"/>
                                        <field name="tax_group_id" invisible="1"/>
                                        <field name="tax_repartition_line_id" invisible="1" force_save="1"/>
                                        <field name="tax_base_amount" invisible="1" force_save="1"/>
                                        <field name="tax_exigible" invisible="1" force_save="1"/>
                                        <field name="company_id" invisible="1"/>
                                        <field name="company_currency_id" invisible="1"/>
                                        <field name="recompute_tax_line" invisible="1" force_save="1"/>
                                        <field name="display_type" force_save="1" invisible="1"/>
                                        <field name="is_rounding_line" invisible="1"/>
                                        <field name="exclude_from_invoice_tab" invisible="1"/>
                                        <field name="account_internal_type" invisible="1"/>
                                    </tree>
                                    <!-- Form view to cover mobile use -->
                                    <form>
                                      <group>
                                        <field name="tax_line_id" invisible="1"/>
                                        <field name="account_id" domain="[('company_id', '=', parent.company_id), ('deprecated', '=', False)]"/>
                                        <field name="partner_id" domain="['|', ('parent_id', '=', False), ('is_company', '=', True)]"/>
                                        <field name="name"/>
                                        <field name="analytic_account_id" groups="analytic.group_analytic_accounting"/>
                                        <field name="analytic_tag_ids" groups="analytic.group_analytic_tags" widget="many2many_tags"/>
                                        <field name="amount_currency" groups="base.group_multi_currency"/>
                                        <field name="company_currency_id" invisible="1"/>
                                        <field name="company_id" invisible="1"/>
                                        <field name="currency_id" options="{'no_create': True}" groups="base.group_multi_currency"/>
                                        <field name="debit" sum="Total Debit"/>
                                        <field name="credit" sum="Total Credit"/>
                                        <field name="tax_ids" string="Taxes Applied" widget="many2many_tags" options="{'no_create': True}"/>
                                        <field name="date_maturity" required="0" invisible="context.get('view_no_maturity', False)"/>
                                        <field name="recompute_tax_line" invisible="1" readonly="1"/>
                                      </group>
                                    </form>
                                </field>
                            </page>
                            <page id="other_tab" string="Other Info" name="other_info"
                                  attrs="{'invisible': [('type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund'))]}">
                                <group id="other_tab_group">
                                    <group string="Invoice"
                                           name="sale_info_group"
                                           attrs="{'invisible': [('type', 'not in', ('out_invoice', 'out_refund'))]}">
                                        <field name="invoice_user_id"/>
                                        <field name="invoice_origin" force_save="1" string="Source Document"
                                               attrs="{'invisible': [('invoice_origin', '=', False)]}"/>
                                    </group>
                                    <group string="Accounting"
                                           name="accounting_info_group"
                                           attrs="{'invisible': [('type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund'))]}">
                                        <field name="invoice_incoterm_id"/>
                                        <field name="fiscal_position_id"/>
                                        <field name="invoice_cash_rounding_id" groups="account.group_cash_rounding"/>
                                        <field name="invoice_source_email"
                                               widget="email"
                                               attrs="{'invisible': ['|', ('type', 'not in', ('in_invoice', 'in_refund')), ('invoice_source_email', '=', False)]}"/>
                                    </group>
                                    <group string="Payments"
                                           name="payments_info_group"
                                           attrs="{'invisible': [('type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund'))]}">
                                        <field name="invoice_payment_ref"
                                               attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                        <field name="invoice_partner_bank_id"
                                               domain="[('partner_id', '=', bank_partner_id)]"
                                               attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                    </group>
                                </group>
                            </page>
                            <page id="other_tab_entry" string="Other Info" name="other_info"
                                  attrs="{'invisible': [('type', '!=', 'entry')]}">
                                <group id="other_tab_entry_group">
                                    <group name="misc_group">
                                        <field name="auto_post"
                                               attrs="{'invisible': [('type', '!=', 'entry')], 'readonly': [('reversed_entry_id', '!=', False)]}"/>
                                        <field name="reversed_entry_id"
                                               attrs="{'invisible': ['|', ('reversed_entry_id', '=', False), ('type', '!=', 'entry')]}"/>
                                        <field name="to_check"
                                               attrs="{'invisible': [('type', '!=', 'entry')]}" />
                                    </group>
                                    <group>
                                        <field name="company_id" groups="base.group_multi_company" required="1"/>
                                    </group>
                                </group>
                                <!-- Internal note -->
                                <field name="narration" placeholder="Add an internal note..." nolabel="1" height="50"/>
                            </page>
                        </notebook>
                    </sheet>
                    <!-- Attachment preview -->
                    <div class="o_attachment_preview"
                         attrs="{'invisible': ['|', '|',
                                ('type', 'not in', ('out_invoice', 'out_refund', 'in_invoice', 'in_refund')),
                                ('state', '!=', 'draft')]}" />
                    <!-- Chatter -->
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers" groups="base.group_user"/>
                        <field name="activity_ids" widget="mail_activity"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form>
            </field>
        </record>

        <record id="view_account_move_filter" model="ir.ui.view">
            <field name="name">account.move.select</field>
            <field name="model">account.move</field>
            <field name="arch" type="xml">
                <search string="Search Move">
                    <field name="name" string="Journal Entry" filter_domain="['|', '|', ('name', 'ilike', self), ('ref', 'ilike', self), ('partner_id', 'ilike', self)]"/>
                    <field name="date"/>
                    <field name="partner_id"/>
                    <field name="journal_id"/>
                    <filter string="Unposted" name="unposted" domain="[('state', '=', 'draft')]" help="Unposted Journal Entries"/>
                    <filter string="Posted" name="posted" domain="[('state', '=', 'posted')]" help="Posted Journal Entries"/>
                    <separator/>
                    <filter string="To Check" name="to_check" domain="[('to_check', '=', True)]"/>
                    <separator/>
                    <filter string="Sales" name="sales" domain="[('journal_id.type', '=', 'sale')]" context="{'default_journal_type': 'sale'}"/>
                    <filter string="Purchases" name="purchases" domain="[('journal_id.type', '=', 'purchase')]" context="{'default_journal_type': 'purchase'}"/>
                    <filter string="Bank" name="bankoperations" domain="[('journal_id.type', '=', 'bank')]" context="{'default_journal_type': 'bank'}"/>
                    <filter string="Cash" name="cashoperations" domain="[('journal_id.type', '=', 'cash')]" context="{'default_journal_type': 'cash'}"/>
                    <filter string="Miscellaneous" name="misc_filter" domain="[('journal_id.type', '=', 'general')]" context="{'default_journal_type': 'general'}"/>
                    <separator/>
                    <filter string="Date" name="date" date="date"/>
                    <separator/>
                    <group expand="0" string="Group By">
                        <filter string="Partner" name="partner" domain="[]" context="{'group_by': 'partner_id'}"/>
                        <filter string="Journal" name="journal" domain="[]" context="{'group_by': 'journal_id'}"/>
                        <filter string="Status" name="status" domain="[]" context="{'group_by': 'state'}"/>
                        <filter string="Date" name="by_date" domain="[]" context="{'group_by': 'date'}" help="Journal Entries by Date"/>
                        <filter string="Company" name="by_company" domain="[]" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="view_account_invoice_filter" model="ir.ui.view">
            <field name="name">account.invoice.select</field>
            <field name="model">account.move</field>
            <field name="arch" type="xml">
                <search string="Search Invoice">
                    <field name="name" string="Invoice"
                           filter_domain="[
                                '|', '|' , '|', '|',
                                ('name', 'ilike', self), ('invoice_origin', 'ilike', self),
                                ('ref', 'ilike', self), ('invoice_payment_ref', 'ilike', self),
                                ('partner_id', 'child_of', self)]"/>
                    <field name="journal_id"/>
                    <field name="partner_id" operator="child_of"/>
                    <field name="invoice_user_id" string="Salesperson" domain="[('share', '=', False)]"/>
                    <field name="date" string="Period"/>
                    <filter domain="[('invoice_user_id', '=', uid)]" name="myinvoices" help="My Invoices"/>
                    <separator/>
                    <filter name="draft" string="Draft" domain="[('state','=','draft')]"/>
                    <filter name="posted" string="Posted" domain="[('state', '=', 'posted')]"/>
                    <filter name="cancel" string="Cancelled" domain="[('state', '=', 'cancel')]"/>
                    <separator/>
                    <filter name="in_payment" string="In Payment" domain="[('invoice_payment_state', '=', 'in_payment'), ('state', '=', 'posted')]"/>
                    <filter name="paid" string="Paid" domain="[('invoice_payment_state', '=', 'paid'), ('state', '=', 'posted')]"/>
                    <filter name="unpaid" string="Not Paid" domain="[('invoice_payment_state', '=', 'not_paid'), ('state', '=', 'posted')]"/>
                    <filter name="late" string="Overdue" domain="['&amp;', ('invoice_date_due', '&lt;', time.strftime('%%Y-%%m-%%d')), ('state', '=', 'posted'), ('invoice_payment_state', '=', 'not_paid')]" help="Overdue invoices, maturity date passed"/>
                    <separator/>
                    <filter name="invoice_date" string="Invoice Date" date="invoice_date"/>
                    <filter name="due_date" string="Due Date" date="invoice_date_due"/>
                    <separator/>
                    <filter invisible="1" string="Late Activities" name="activities_overdue"
                        domain="[('activity_ids.date_deadline', '&lt;', context_today().strftime('%Y-%m-%d'))]"
                        help="Show all records which has next action date is before today"/>
                    <filter invisible="1" string="Today Activities" name="activities_today"
                        domain="[('activity_ids.date_deadline', '=', context_today().strftime('%Y-%m-%d'))]"/>
                    <filter invisible="1" string="Future Activities" name="activities_upcoming_all"
                        domain="[('activity_ids.date_deadline', '&gt;', context_today().strftime('%Y-%m-%d'))]"/>
                    <group expand="0" string="Group By">
                        <filter string="Salesperson" name="salesperson" context="{'group_by':'invoice_user_id'}"/>
                        <filter name="status" string="Status" context="{'group_by':'state'}"/>
                        <separator/>
                        <filter string="Invoice Date" name="invoicedate" context="{'group_by': 'invoice_date'}"/>
                        <filter string="Due Date" name="duedate" context="{'group_by': 'invoice_date_due'}"/>
                    </group>
               </search>
            </field>
        </record>

        <!-- ACTIONS -->

        <!-- account.move.line (Journal Items) -->

        <act_window
            id="action_move_line_select"
            name="Journal Items"
            context="{'search_default_account_id': [active_id]}"
            res_model="account.move.line"/>

        <act_window
            id="action_move_line_select_by_partner"
            name="Journal Items"
            context="{'search_default_partner_id': [active_id]}"
            res_model="account.move.line"/>

        <record id="action_view_account_move_line_reconcile" model="ir.actions.client">
             <field name="name">Reconcile</field>
             <field name="tag">manual_reconciliation_view</field>
             <field name="binding_model_id" ref="account.model_account_move_line"/>
             <field name="binding_type">action</field>
             <field name="binding_view_types">list</field>
         </record>

        <record id="action_manual_reconcile" model="ir.actions.client">
            <field name="name">Journal Items to Reconcile</field>
            <field name="res_model">account.move.line</field>
            <field name="tag">manual_reconciliation_view</field>
        </record>

        <record id="action_accrual_entry" model="ir.actions.server">
            <field name="name">Create Accrual Entry</field>
            <field name="model_id" ref="account.model_account_move_line"/>
            <field name="binding_model_id" ref="account.model_account_move_line"/>
            <field name="state">code</field>
            <field name="code">action = records.action_accrual_entry()</field>
        </record>

        <act_window
            id="action_move_line_select_tax_audit"
            name="Journal Items for Tax Audit"
            context="{'search_default_account_id': [active_id]}"
            res_model="account.move.line"
            view_id="account.view_move_line_tax_audit_tree"/>

        <record id="action_account_moves_all_a" model="ir.actions.act_window">
            <field name="context">{'journal_type':'general', 'search_default_group_by_move': 1, 'search_default_posted':1, 'name_groupby':1, 'create':0}</field>
            <field name="name">Journal Items</field>
            <field name="res_model">account.move.line</field>
            <field name="domain">[('display_type', 'not in', ('line_section', 'line_note'))]</field>
            <field name="view_id" ref="view_move_line_tree_grouped"/>
            <field name="view_mode">tree,pivot,graph,form,kanban</field>
        </record>

        <record id="action_account_moves_journal_sales" model="ir.actions.act_window">
            <field name="context">{'journal_type':'sales', 'search_default_group_by_move': 1, 'search_default_posted':1, 'search_default_sales':1, 'name_groupby':1, 'expand': 1}</field>
            <field name="name">Sales</field>
            <field name="res_model">account.move.line</field>
            <field name="domain">[('display_type', 'not in', ('line_section', 'line_note'))]</field>
            <field name="view_id" ref="view_move_line_tree_grouped_sales_purchases"/>
            <field name="view_mode">tree,pivot,graph</field>
        </record>

        <record id="action_account_moves_journal_purchase" model="ir.actions.act_window">
            <field name="context">{'journal_type':'purchase', 'search_default_group_by_move': 1, 'search_default_posted':1, 'search_default_purchases':1, 'name_groupby':1, 'expand': 1}</field>
            <field name="name">Purchases</field>
            <field name="res_model">account.move.line</field>
            <field name="domain">[('display_type', 'not in', ('line_section', 'line_note'))]</field>
            <field name="view_id" ref="view_move_line_tree_grouped_sales_purchases"/>
            <field name="view_mode">tree,pivot,graph</field>
        </record>

        <record id="action_account_moves_journal_bank_cash" model="ir.actions.act_window">
            <field name="context">{'journal_type':'bank', 'search_default_group_by_move': 1, 'search_default_posted':1, 'search_default_bank':1, 'search_default_cash':1, 'name_groupby':1, 'expand': 1}</field>
            <field name="name">Bank and Cash</field>
            <field name="res_model">account.move.line</field>
            <field name="domain">[('display_type', 'not in', ('line_section', 'line_note'))]</field>
            <field name="view_id" ref="view_move_line_tree_grouped_bank_cash"/>
            <field name="view_mode">tree,pivot,graph</field>
        </record>

        <record id="action_account_moves_journal_misc" model="ir.actions.act_window">
            <field name="context">{'journal_type':'general', 'search_default_group_by_move': 1, 'search_default_posted':1, 'search_default_misc_filter':1, 'name_groupby':1, 'expand': 1}</field>
            <field name="name">Miscellaneous</field>
            <field name="res_model">account.move.line</field>
            <field name="domain">[('display_type', 'not in', ('line_section', 'line_note'))]</field>
            <field name="view_id" ref="view_move_line_tree_grouped_misc"/>
            <field name="view_mode">tree,pivot,graph</field>
        </record>

        <record id="action_account_moves_ledger_general" model="ir.actions.act_window">
            <field name="context">{'journal_type':'general', 'search_default_group_by_account': 1, 'search_default_posted':1}</field>
            <field name="name">General Ledger</field>
            <field name="res_model">account.move.line</field>
            <field name="domain">[('display_type', 'not in', ('line_section', 'line_note'))]</field>
            <field name="view_id" ref="view_move_line_tree_grouped_general"/>
            <field name="search_view_id" ref="view_account_move_line_filter_with_root_selection"/>
            <field name="view_mode">tree,pivot,graph</field>
        </record>

        <record id="action_account_moves_ledger_partner" model="ir.actions.act_window">
            <field name="context">{'journal_type':'general', 'search_default_group_by_partner': 1, 'search_default_posted':1, 'search_default_payable':1, 'search_default_receivable':1, 'search_default_unreconciled':1}</field>
            <field name="name">Partner Ledger</field>
            <field name="res_model">account.move.line</field>
            <field name="domain">[('display_type', 'not in', ('line_section', 'line_note'))]</field>
            <field name="view_id" ref="view_move_line_tree_grouped_partner"/>
            <field name="search_view_id" ref="view_account_move_line_filter"/>
            <field name="view_mode">tree,pivot,graph</field>
        </record>

        <record id="action_account_moves_all_tree" model="ir.actions.act_window">
            <field name="name">Journal Items</field>
            <field name="res_model">account.move.line</field>
            <field name="domain">[('display_type', 'not in', ('line_section', 'line_note'))]</field>
            <field name="context">{'search_default_partner_id': [active_id], 'default_partner_id': active_id, 'search_default_posted':1}</field>
            <field name="view_id" ref="view_move_line_tree"/>
        </record>

        <record id="action_account_moves_all" model="ir.actions.act_window">
            <field name="context">{'journal_type':'general', 'search_default_posted':1}</field>
            <field name="name">Journal Items</field>
            <field name="res_model">account.move.line</field>
            <field name="domain">[('display_type', 'not in', ('line_section', 'line_note')), ('move_id.state', '!=', 'cancel')]</field>
            <field name="view_id" ref="view_move_line_tree"/>
            <field name="view_mode">tree,pivot,graph,form,kanban</field>
        </record>

        <record id="action_move_line_graph" model="ir.actions.act_window">
            <field name="name">Journal Items</field>
            <field name="view_mode">graph,pivot</field>
            <field name="view_id" ref="account_move_line_graph_date"/>
            <field name="domain">[('display_type', 'not in', ('line_section', 'line_note'))]</field>
            <field name="context">{'search_default_account_id': [active_id]}</field>
            <field name="res_model">account.move.line</field>
        </record>

        <record id="action_move_line_graph_posted" model="ir.actions.act_window">
            <field name="name">Journal Items</field>
            <field name="view_mode">graph,pivot</field>
            <field name="view_id" ref="account_move_line_graph_date"/>
            <field name="domain">[('display_type', 'not in', ('line_section', 'line_note'))]</field>
            <field name="context">{'search_default_account_id': [active_id], 'search_default_posted': 1}</field>
            <field name="res_model">account.move.line</field>
        </record>

        <!-- account.move (Journal Entry) -->

        <record id="action_move_journal_line" model="ir.actions.act_window">
            <field name="name">Journal Entries</field>
            <field name="res_model">account.move</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="view_id" ref="view_move_tree"/>
            <field name="search_view_id" ref="view_account_move_filter"/>
            <field name="context">{'default_type': 'entry', 'search_default_misc_filter':1, 'view_no_maturity': True}</field>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create a journal entry
              </p><p>
                A journal entry consists of several journal items, each of
                which is either a debit or a credit transaction.
              </p>
            </field>
        </record>

        <record id="action_move_out_invoice_type" model="ir.actions.act_window">
            <field name="name">Invoices</field>
            <field name="res_model">account.move</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="view_id" ref="view_invoice_tree"/>
            <field name="search_view_id" ref="view_account_invoice_filter"/>
            <field name="domain">[('type', '=', 'out_invoice')]</field>
            <field name="context">{'default_type': 'out_invoice'}</field>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create a customer invoice
              </p><p>
                Create invoices, register payments and keep track of the discussions with your customers.
              </p>
            </field>
        </record>

        <record id="action_move_out_refund_type" model="ir.actions.act_window">
            <field name="name">Credit Notes</field>
            <field name="res_model">account.move</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="view_id" ref="view_invoice_tree"/>
            <field name="search_view_id" ref="view_account_invoice_filter"/>
            <field name="domain">[('type', '=', 'out_refund')]</field>
            <field name="context">{'default_type': 'out_refund'}</field>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create a credit note
              </p><p>
                Note that the easiest way to create a credit note is to do it directly
                from the customer invoice.
              </p>
            </field>
        </record>

        <record id="action_move_in_invoice_type" model="ir.actions.act_window">
            <field name="name">Bills</field>
            <field name="res_model">account.move</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="view_id" ref="view_invoice_tree"/>
            <field name="search_view_id" ref="view_account_invoice_filter"/>
            <field name="domain">[('type', '=', 'in_invoice')]</field>
            <field name="context">{'default_type': 'in_invoice'}</field>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create a vendor bill
              </p><p>
                Create invoices, register payments and keep track of the discussions with your vendors.
              </p>
            </field>
        </record>

        <record id="action_move_in_refund_type" model="ir.actions.act_window">
            <field name="name">Refund</field>
            <field name="res_model">account.move</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="view_id" ref="view_invoice_tree"/>
            <field name="search_view_id" ref="view_account_invoice_filter"/>
            <field name="domain">[('type', '=', 'in_refund')]</field>
            <field name="context">{'default_type': 'in_refund'}</field>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create a vendor credit note
              </p><p>
                Note that the easiest way to create a vendor credit note it to do it directly from the vendor bill.
              </p>
            </field>
        </record>

        <record id="action_move_out_receipt_type" model="ir.actions.act_window">
            <field name="name">Receipts</field>
            <field name="res_model">account.move</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="view_id" ref="view_invoice_tree"/>
            <field name="search_view_id" ref="view_account_invoice_filter"/>
            <field name="domain">[('type', '=', 'out_receipt')]</field>
            <field name="context">{'default_type': 'out_receipt'}</field>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create a new sales receipt
              </p><p>
                When the sale receipt is confirmed, you can record the customer
                payment related to this sales receipt.
              </p>
            </field>
        </record>

        <record id="action_move_in_receipt_type" model="ir.actions.act_window">
            <field name="name">Receipts</field>
            <field name="res_model">account.move</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="view_id" ref="view_invoice_tree"/>
            <field name="search_view_id" ref="view_account_invoice_filter"/>
            <field name="domain">[('type', '=', 'in_receipt')]</field>
            <field name="context">{'default_type': 'in_receipt'}</field>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Register a new purchase receipt
              </p><p>
                When the purchase receipt is confirmed, you can record the
                vendor payment related to this purchase receipt.
              </p>
            </field>
        </record>

        <record id="action_move_line_form" model="ir.actions.act_window">
            <field name="name">Entries</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">account.move</field>
            <field name="view_id" ref="view_move_tree"/>
            <field name="search_view_id" ref="view_account_move_filter"/>
        </record>

        <record model="ir.actions.server" id="action_move_switch_invoice_to_credit_note">
            <field name="name">Switch into refund/credit note</field>
            <field name="model_id" ref="account.model_account_move"/>
            <field name="binding_model_id" ref="account.model_account_move" />
            <field name="state">code</field>
            <field name="code">
if records:
    action = records.action_switch_invoice_into_refund_credit_note()
            </field>
        </record>

        <record model="ir.actions.server" id="action_check_hash_integrity">
            <field name="name">Data Inalterability Check</field>
            <field name="model_id" ref="account.model_res_company"/>
            <field name="type">ir.actions.server</field>
            <field name="state">code</field>
            <field name="code">
                action = env.company._action_check_hash_integrity()
            </field>
        </record>

        <!-- MENUS -->

        <menuitem
            id="menu_action_move_out_invoice_type"
            action="action_move_out_invoice_type"
            parent="account.menu_finance_receivables"
            sequence="1"/>

        <menuitem
            id="menu_action_move_out_refund_type"
            action="action_move_out_refund_type"
            parent="account.menu_finance_receivables"
            sequence="1"/>

        <menuitem
            id="menu_action_move_out_receipt_type"
            action="action_move_out_receipt_type"
            parent="account.menu_finance_receivables"
            sequence="1"/>

        <menuitem
            id="menu_action_move_in_invoice_type"
            action="action_move_in_invoice_type"
            parent="account.menu_finance_payables"
            sequence="1"/>

        <menuitem
            id="menu_action_move_in_refund_type"
            action="action_move_in_refund_type"
            parent="account.menu_finance_payables"
            sequence="1"/>

        <menuitem
            id="menu_action_move_in_receipt_type"
            action="action_move_in_receipt_type"
            parent="account.menu_finance_payables"
            sequence="1"/>

        <menuitem
            id="menu_action_account_moves_all"
            action="action_account_moves_all"
            parent="account.menu_finance_entries_accounting_miscellaneous"
            groups="base.group_no_one"
            sequence="10"/>

        <menuitem
            id="menu_action_move_journal_line_form"
            action="action_move_journal_line"
            parent="account.menu_finance_entries_accounting_miscellaneous"
            groups="account.group_account_user"
            sequence="1"/>

        <menuitem
            id="menu_action_account_moves_journal_sales"
            action="action_account_moves_journal_sales"
            parent="account.menu_finance_entries_accounting_journals"
            groups="account.group_account_user"
            sequence="1"/>

        <menuitem
            id="menu_action_account_moves_journal_purchase"
            action="action_account_moves_journal_purchase"
            parent="account.menu_finance_entries_accounting_journals"
            groups="account.group_account_user"
            sequence="1"/>

        <menuitem
            id="menu_action_account_moves_journal_bank_cash"
            action="action_account_moves_journal_bank_cash"
            parent="account.menu_finance_entries_accounting_journals"
            groups="account.group_account_user"
            sequence="1"/>

        <menuitem
            id="menu_action_account_moves_journal_misc"
            action="action_account_moves_journal_misc"
            parent="account.menu_finance_entries_accounting_journals"
            groups="account.group_account_user"
            sequence="1"/>

        <menuitem
            id="menu_action_account_moves_ledger_general"
            action="action_account_moves_ledger_general"
            parent="account.menu_finance_entries_accounting_ledgers"
            groups="account.group_account_user"
            sequence="1"/>

        <menuitem
            id="menu_action_account_moves_ledger_partner"
            action="action_account_moves_ledger_partner"
            parent="account.menu_finance_entries_accounting_ledgers"
            groups="account.group_account_user"
            sequence="1"/>

    </data>
</odoo>
