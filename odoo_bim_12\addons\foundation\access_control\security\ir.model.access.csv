id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
access_access_navigation_manager,access.navigation,model_access_navigation,access_control.group_access_control_manager,1,1,1,1
access_access_route_manager,access.route,model_access_route,access_control.group_access_control_manager,1,1,1,1
access_role_access_navigation_manager,role.access.navigation,model_role_access_navigation,access_control.group_access_control_manager,1,1,1,1
access_role_access_route_manager,role.access.route,model_role_access_route,access_control.group_access_control_manager,1,1,1,1
access_access_route_button_manager,access.route.button,model_access_route_button,access_control.group_access_control_manager,1,1,1,1
access_role_access_route_button_manager,role.access.route.button,model_role_access_route_button,access_control.group_access_control_manager,1,1,1,1


access_access_navigation_customer,access.navigation,model_access_navigation,access_control.group_access_control_customer,1,0,0,0
access_access_route_customer,access.route,model_access_route,access_control.group_access_control_customer,1,0,0,0
access_role_access_navigation_customer,role.access.navigation,model_role_access_navigation,access_control.group_access_control_customer,1,0,0,0
access_role_access_route_customer,role.access.route,model_role_access_route,access_control.group_access_control_customer,1,0,0,0
access_access_route_button_customer,access.route.button,model_access_route_button,access_control.group_access_control_customer,1,0,0,0
access_role_access_route_button_customer,role.access.route.button,model_role_access_route_button,access_control.group_access_control_customer,1,0,0,0
