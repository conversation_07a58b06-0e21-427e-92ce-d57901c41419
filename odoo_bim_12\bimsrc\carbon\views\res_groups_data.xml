<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record model="ir.module.category" id="module_carbon_project">
            <field name="name">项目</field>
            <field name="description">碳计算项目管理</field>
        </record>


        <record model="res.groups" id="group_district_customer">
            <field name="name">地区只读</field>
            <field name="category_id" ref="module_carbon_project"/>
        </record>
        <record model="res.groups" id="group_district_manager">
            <field name="name">地区管理</field>
            <field name="category_id" ref="module_carbon_project"/>
            <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
        </record>

        <record model="res.groups" id="group_base_customer">
            <field name="name">基础只读</field>
            <field name="category_id" ref="module_carbon_project"/>
        </record>
        <record model="res.groups" id="group_base_manager">
            <field name="name">基础管理</field>
            <field name="category_id" ref="module_carbon_project"/>
            <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
        </record>

        <record model="res.groups" id="group_inventory_customer">
            <field name="name">清单只读</field>
            <field name="category_id" ref="module_carbon_project"/>
        </record>
        <record model="res.groups" id="group_inventory_manager">
            <field name="name">清单管理</field>
            <field name="category_id" ref="module_carbon_project"/>
            <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
        </record>

        <record model="res.groups" id="group_project_customer">
            <field name="name">项目只读</field>
            <field name="category_id" ref="module_carbon_project"/>
        </record>
        <record model="res.groups" id="group_project_manager">
            <field name="name">项目管理</field>
            <field name="category_id" ref="module_carbon_project"/>
            <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
        </record>

        <record model="res.groups" id="group_user_customer">
            <field name="name">用户只读</field>
            <field name="category_id" ref="module_carbon_project"/>
        </record>
        <record model="res.groups" id="group_user_manager">
            <field name="name">用户管理</field>
            <field name="category_id" ref="module_carbon_project"/>
            <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
        </record>
    </data>
</odoo>