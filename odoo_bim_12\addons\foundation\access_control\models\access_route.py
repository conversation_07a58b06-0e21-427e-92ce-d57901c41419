# -*- coding: utf-8 -*-
from odoo import fields, api, models

class AccessRoute(models.Model):
    """
    路由
    """
    _name = 'access.route'
    _description = "路由"

    name = fields.Char('名称')
    pid = fields.Many2one('access.route','Parent')
    children = fields.One2many('access.route','pid','Childrens')
    is_active = fields.<PERSON><PERSON>an('Active',default=True)
    path = fields.Char('路径')
    buttons = fields.One2many('access.route.button','route_id','按钮')


    def name_get(self):
        res = []
        for record in self:
            res.append((record.id, '%s (%s)' % (record.name, record.path)))
        return res















