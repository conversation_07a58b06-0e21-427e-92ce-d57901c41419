# -*- coding: utf-8 -*-
from odoo import models, fields,api


class resCountryStateCity(models.Model):
    '''
    城市管理
    '''
    _name = 'res.country.state.city'
    _description = '城市管理'

    name = fields.Char(u'城市名称')
    state_id = fields.Many2one('res.country.state', u'州/省')
    country_id = fields.Many2one('res.country', u'国家',related='state_id.country_id')
    code = fields.Char(u'城市代码')
    geo_json = fields.Text('geo json')
    district_ids = fields.One2many('res.country.state.city.district','city_id',string='区/县列表')