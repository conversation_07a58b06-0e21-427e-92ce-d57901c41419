# 1. 基础模块依赖关系

##1.1 以下流程图展示基础模块依赖关系，箭头指向的模块是当前模块所需要依赖的模块（但不包括依赖base，web)

```mermaid
graph LR;
    authing --> app_odoo_customize
    authing --> auth_oauth
    automatic_backup --> knowcenter
    automatic_backup --> mail
    app_odoo_customize --> mail
    app_odoo_customize --> web_settings_dashboard
    app_odoo_customize --> iap
    org_structure --> hr
    org_structure --> security_user_roles
    app_odoo_customize --> security_user_roles
    app_odoo_customize --> security_user_roles
    app_odoo_customize --> security_user_roles
    base_import_widget --> plugin_center
    web_mobile --> web_settings_dashboard
```
##1.2 以下流程图展示基础模块依赖base和web模块的依赖关系

```mermaid
graph LR;
  
    authing --> base
    org_structure --> base
    message_channel --> base 
    restapi --> base
    knowcenter --> base
    base_import_widget --> base
    plugin_center --> base
    security_user_roles --> base
    web_enterprise --> web
    web_settings_dashboard --> web
    web_gantt --> web
    plugin_center --> web
    message_channel --> web
    app_odoo_customize --> web
    knowcenter --> web
    base_import_widget --> web
    
    
```

# 2. bim施工管理平台业务模块依赖关系

##2.1 以下流程图展示施工管理平台业务模块，箭头指向的模块是当前模块所需要依赖的模块（但不包括依赖base，web)
```mermaid
graph LR;
    app_web_superbar --> app_web_widget_ztree
    action_timed_task --> security_user_roles
    action_timed_task --> auth_oauth
    action_timed_task --> smart_site
    action_timed_task --> construction_management_api
    base_api --> smart_video_monitor
    base_api --> baseconfig
    bim_model_mgmt --> app_web_superbar
    bim_model_mgmt --> kendoui
    iserver_manage --> plugin_center
    construction_management_api --> smart_video_monitor
    construction_management_api --> knowcenter
    construction_management_api --> document_center
    construction_project --> app_web_superbar
    construction_project --> bim_model_mgmt
    construction_quality --> app_web_superbar
    construction_quality --> bim_model_mgmt
    construction_safety --> app_web_superbar
    construction_safety --> bim_model_mgmt
    construction_safety --> document_search_view
    cost_measurement --> bim_model_mgmt
    cost_measurement --> base_import_widget
    generic_request --> mail
    generic_request --> generic_mixin
    generic_request --> generic_tag
    generic_request --> crnd_web_diagram_plus
    generic_request --> crnd_web_list_popover_widget
    generic_request --> crnd_web_tree_colored_field
    generic_request --> web_widget_colorpicker
    generic_request --> web_settings_dashboard
    generic_request --> base_setup
    generic_request --> display_import_button
    smart_site --> bim_model_mgmt
    supermap --> construction_project
    supermap --> construction_quality
    access_control --> security_user_roles
    access_control --> plugin_center
    

```

##2.2 以下流程图展示施工管理平台业务模块依赖base和web模块的依赖关系

```mermaid
graph LR;
        base_api --> base
        bim_model_mgmt --> base
        construction_project --> base
        construction_quality --> base
        construction_management_api --> base
        construction_safety --> base
        cost_measurement --> base
        generic_request --> base
        smart_site --> base
        smart_video_monitor --> base
        supermap --> base
        baseconfig --> base
        access_control --> base
        iserver_manage --> base
        app_web_superbar --> web

```