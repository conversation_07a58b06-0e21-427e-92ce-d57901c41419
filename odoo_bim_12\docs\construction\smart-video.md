# /projects/video_monitor/get_camera_list

## URI

**URI**: /projects/video_monitor/get_camera_list

## 支持的方法

GET

## 介绍

获取视频监控的所有信息

## HTTP方法

### GET

请求参数：

无


响应结构：

| 字段 | 类型 | 说明 |
|----|----|----|
|  name  | String    | 摄像头名称   |
| type | String | 型号|
|status | String| 状态 |
|  lat   | Number   | 摄像头经度   |
|  lon   | Number   | 摄像头纬度   |
|  height   | Number   | 摄像头高度   |
|  cameraIndexCode   | String    | 摄像头编号   |
|  ip   | String    | IP地址   |
|  port  | String   | 端口号   |
|  userId  | String   | 用户id   |
|  appkey  | String    | AK码   |
|  secret  | String   | SK码   |


示例：
```
{
"jsonrpc":2.0,
"id":0,
"result":{
        "code":0,
        "message":"OK",
        "data":[{
                "name": "仓库1",
                "type":"K234",
                "status":"在线",
                "pos": {
                    "lat": 123,
                    "lon": 31,
                    "height": 345
                },
                "cameraIndexCode":"",      
                "ip": "",
                "port": "",
                "userId": "",
                "appkey": "...",
                "secret": "..."
                },
                {
                "name": "仓库2",
                "type":"K234",
                "status":"在线",
                "pos": {
                    "lat": 123,
                    "lon": 31,
                    "height": 345
                },
                "cameraIndexCode":"",      
                "ip": "",
                "port": "",
                "userId": "",
                "appkey": "...",
                "secret": "..."
                }
            ]      
    }
}

```
