# -*- coding: utf-8 -*-
{
    'name': "权限模块",

    'summary': """
       权限模块
       """,

    'description': """
         权限模块
    """,
    'sequence': 200,

    'author': "周伟",
    'website': "",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/master/openerp/addons/base/module/module_data.xml
    # for the full list
    'category': 'Bim Construction',
    'version': '0.1',

    # any module necessary for this one to work correctly
    'depends': ['base','security_user_roles','plugin_center'],

    # always loaded
    'data': [
        'views/access_navigation.xml',
        'views/access_route.xml',
        'views/access_route_button.xml',
        'views/role_access_navigation.xml',
        'views/role_access_route.xml',
        'views/role_access_route_button.xml',
        'views/menu.xml',
        'views/security_role_extend.xml',
        'security/ir.model.access.csv',
        'security/security.xml',
        ],
    # only loaded in demonstration mode
    'demo': [
        # 'demo/demo.xml',
    ],
    'qweb': [
        "static/src/xml/template.xml",
    ],
    'installable': True,
    'application': True,
    'auto_install': False,

}