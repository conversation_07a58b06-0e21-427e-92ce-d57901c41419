<?xml version="1.0"?>
<odoo>

    <record id="security_role_view_search" model="ir.ui.view">
        <field name="name">security.role.search</field>
        <field name="model">security.role</field>
        <field name="arch" type="xml">
            <search>
               <field name="name"/>
               <field name="user_ids"/>
               <filter string="Archived"
                        name="inactive"
                        domain="[('active', '=', False)]"
               />
            </search>
        </field>
    </record>

    <record id="security_role_view_form" model="ir.ui.view">
        <field name="name">security.role.form</field>
        <field name="model">security.role</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_create_user"
                                type="object"
                                class="oe_stat_button"
                                icon="fa-user"
                        >
                            New user
                        </button>
                        <button name="toggle_active"
                                type="object"
                                class="oe_stat_button"
                                icon="fa-archive"
                        >
                            <field name="active"
                                   widget="boolean_button"
                                   options='{"terminology": "archive"}'
                            />
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="name"
                                   placeholder="Role"
                                   class="oe_inline"
                            />
                        </h1>
                    </div>
                    <group>
                        <field name="user_ids"
                               widget="many2many_tags"
                               options="{'no_create_edit': 1, 'no_quick_create': 1}"
                        />
                    </group>
                    <notebook>
                        <page name="access_rights" string="Access Rights">
                            <field name="group_ids"/>
                        </page>
                        <page name="preferences" string="Preferences">
                            <group>
                                <field name="action_id" string="Home Action" />
                                <field name="odoobot_state" string="Odoo Bot" />
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id="security_role_view_tree" model="ir.ui.view">
        <field name="name">security.role.tree</field>
        <field name="model">security.role</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
                <field name="user_ids" widget="many2many_tags"/>
            </tree>
        </field>
    </record>

    <record id="security_role_action" model="ir.actions.act_window">
         <field name="name">User Roles</field>
         <field name="type">ir.actions.act_window</field>
         <field name="res_model">security.role</field>
         <field name="view_mode">tree,form</field>
         <field name="search_view_id" eval="security_role_view_search"/>
         <field name="help" type="html">
              <p class="oe_view_nocontent_create">
                    Click to add a new security role
              </p>
         </field>
    </record>

    <record id="security_role_action_form_only" model="ir.actions.act_window">
         <field name="name">User Role</field>
         <field name="type">ir.actions.act_window</field>
         <field name="res_model">security.role</field>
         <field name="view_mode">form</field>
    </record>

    <menuitem name="角色"
              id="menu_security_roels"
              parent="base.menu_users"
              sequence="3"
              action="security_role_action"
              groups="base.group_erp_manager"
    />

    <!-- TODO: 需要移入org_structure模块 -->
    <!-- <menuitem id="org_roles"
        name="角色"
        parent="org_structure.menu_org_structure"
        action="security_role_action"
        sequence="40"/> -->

    <data noupdate="1">
        <record id="security_groups_view" model="ir.ui.view">
            <field name="name">security.role.groups</field>
            <field name="model">security.role</field>
            <field name="inherit_id" ref="security_user_roles.security_role_view_form"/>
            <field name="arch" type="xml">
                <!-- dummy, will be modified by groups -->
                <field name="group_ids" position="after"/>
            </field>
        </record>
    </data>

</odoo>
