# -*- coding: utf-8 -*-
from odoo import fields, api, models
from odoo.exceptions import UserError, ValidationError
import logging
import os
import time
import uuid
import json
import matplotlib
import matplotlib.pyplot as plt
from odoo.http import request
import requests
from datetime import datetime,timedelta




import numpy as np
_logger = logging.getLogger(__name__)

def create_pie(x,labels,explode,title,save_path_image):
    # 不存在负值情况，生成一个饼图
    path = os.path.dirname(os.path.abspath(__file__)).replace('models','static')
    chinese = matplotlib.font_manager.FontProperties(fname=path + '/fonts/simhei.ttf')

    fig, ax = plt.subplots(1, 1, figsize=(8, 8))
    _, l_text, autotexts = ax.pie(x = x, labels=labels, shadow=0,autopct='%.2f%%', explode=explode)

    # 将饼图中的字体改为白色
    for autotext in autotexts:
        autotext.set_color('white')
    for t in l_text: 
        t.set_fontproperties(chinese)
    ax.set_title(title, loc="center", fontsize=16, fontproperties=chinese)
    plt.savefig(path + save_path_image)


def create_two_pie(z_x,f_x,labels,explode,title,save_path_image):
    # 存在负值情况，生成一个外环（正）和内环(负)
    path = os.path.dirname(os.path.abspath(__file__)).replace('models','static')
    chinese = matplotlib.font_manager.FontProperties(fname=path + '/fonts/simhei.ttf')

    fig, ax = plt.subplots(1, 1, figsize=(8, 8))
    size = 0.3
    vals = np.array([[60., 32.], [37., 40.], [29., 10.]])
    cmap = plt.get_cmap("tab20c")
    outer_colors = cmap(np.arange(5)*4)
    inner_colors = cmap([1, 2, 5, 6, 9, 10])

    _logger.info('vals.sum(axis=1)')
    _logger.info(vals.sum(axis=1))
    _logger.info(vals.flatten())
    _, l_text, autotexts = ax.pie(z_x, radius=1,labels=labels,pctdistance=0.85, colors=outer_colors,autopct='%.2f%%', wedgeprops=dict(width=size, edgecolor='w'))
    for autotext in autotexts:
        autotext.set_color('white')
    for t in l_text: 
        t.set_fontproperties(chinese)
    ax.set_title(title, loc="center", fontsize=16, fontproperties=chinese)
    _, l_text, autotexts = ax.pie([10], radius=0.5,labels=np.array(['碳汇']),pctdistance=0.7, colors=['red'],autopct='-%.2f%%', wedgeprops=dict(width=size, edgecolor='w'))
    for t in l_text: 
        t.set_fontproperties(chinese)
    for autotext in autotexts:
        autotext.set_color('white')
    plt.savefig(path + save_path_image)

def create_bar(x,y,title,save_path_image):
    path = os.path.dirname(os.path.abspath(__file__)).replace('models','static')
    chinese = matplotlib.font_manager.FontProperties(fname=path + '/fonts/simhei.ttf')

    fig, ax = plt.subplots(1, 1, figsize=(8, 8))

    ax.set_title(title, loc="center", fontsize=16, fontproperties=chinese)
    ax.bar(x, y, color='#547ccb')
    for label in ax.get_xticklabels():
        label.set_fontproperties(chinese)
    plt.savefig(path + save_path_image)


class ProjectReportRough(models.AbstractModel):
    _name = 'report.carbon.project_report_rough'

    @api.model
    def _get_report_values(self, docids, data=None):
        path = os.path.dirname(os.path.abspath(__file__)).replace('models','static')
        timestamp = time.time()

        base_url = self.env["ir.config_parameter"].sudo().get_param("server.base.url")
        CarbonProjectScheme = self.env['carbon.project.scheme'].sudo()
        docs = CarbonProjectScheme.browse(docids)
        scheme = docs[0]
        project = docs[0].project_id
        A_Area = float(project.area)
        

        data = json.loads(scheme.data)
        def get_constructs(data):
            constructs = []

            
            LayerData = data.get('LayerData')
            # _logger.info(data)
            # _logger.info(LayerData)
            for l in LayerData:
                composition_value = l.get('composition_value')
                layer_name = l.get('layer').split('&')[1]
                # _logger.info(layer_name)
                k = layer_name
                v = []
                for c in composition_value:
                    _id = c.get('id')
                    value = c.get('value')
                    composition = self.env['structural.layer.composition'].sudo().browse(_id)
                
            
                    # _logger.info(composition.type)
                    if composition.type == 'fill':
                        unit = composition.unit
                        # _logger.info(f'{composition.name}')
                        # _logger.info(f'{value}')
                        v.append(f'{composition.name}({value}{unit})')
                    if composition.type == 'radio':
                        # _logger.info(f'{composition.name}')
                        # _logger.info(f'{value}')
                        if composition.name == '混合料':
                            pass
                        else:
                            value = self.env['material.life.cycle.inventory'].browse(value).name
                        
                        v.append(f'{composition.name}({value})')

                    if composition.type == 'checkbox':
                        # _logger.info(f'{composition.name}')
                        # _logger.info(f'{value}')
                        if composition.name == '矿料':
                            _l = []
                            for i in value:
                                category = i.get('category')
                                quality_proportion = i.get('quality_proportion')
                                n = self.env['material.life.cycle.inventory'].browse(category).name
                                _l.append(f'{n}{quality_proportion}%')
                            s = '、'.join(_l)
                            v.append(f'矿料({s})')
                        else:
                            if len(value):
                                category = value[0].get('category')
                                number = value[0].get('number')
                                unit = value[0].get('unit')
                                n = self.env['material.life.cycle.inventory'].browse(category).name
                                v.append(f'{n}({number}{unit})')

                        # if composition.name == '混合料':
                        #     pass
                        # else:
                        #     value = self.env['material.life.cycle.inventory'].browse(value).name
                        
                        # v.append(f'{composition.name}({value})')
                
                constructs.append(k + '：  ' + '、'.join(v))
            _logger.info(constructs)
            return constructs


        scheme_data = {
            'name': scheme.name or '',
            'life': data.get('A-Year'),
            'res_all': '%.5f'% float(scheme.res_all),
            'constructs': get_constructs(data),
        }

        A_Year = float(scheme_data.get('life'))

        
        images = []
        _logger.info('xxxxxxxxxxxxxxxxxxxxx')
        _logger.info([float(r.res_all) * 10000 for r in scheme.result_ids])
        has_f = False # 是否有负值
        # if '碳汇' in [r.stage_id.name for r in scheme.result_ids]:
        #     has_f = True
        if has_f:
            save_path_image = '/src/images/' + f'rough_two_pie_scheme_{scheme.id}_{timestamp}.png'
            create_two_pie(np.array([float(r.res_all) * 10000 for r in scheme.result_ids if float(r.res_all)>0]),np.array([]),np.array([r.stage_id.name for r in scheme.result_ids if float(r.res_all)>0]),[0 for r in scheme.result_ids if float(r.res_all)>0],'图1 各单元碳排放占比', save_path_image)
            images.append(f'{base_url}/carbon/static{save_path_image}')
        else:
            result_ids = scheme.result_ids.filtered(lambda x:x.stage_id.name != '碳汇')
            save_path_image = '/src/images/' + f'rough_pie_scheme_{scheme.id}_{timestamp}.png'
            create_pie(np.array([float(r.res_all) * 10000 for r in result_ids]),np.array([r.stage_id.name for r in result_ids]),[0 for r in result_ids],'图1 各单元碳排放占比', save_path_image)
            images.append(f'{base_url}/carbon/static{save_path_image}')

        save_path_image = '/src/images/' + f'rough_bar_year_scheme_{scheme.id}_{timestamp}.png'
        create_bar(np.array([r.stage_id.name for r in scheme.result_ids]), np.array([float(r.res_year) for r in scheme.result_ids]), '图2 平均每年碳排放强度', save_path_image)
        images.append(f'{base_url}/carbon/static{save_path_image}')

        save_path_image = '/src/images/' + f'rough_bar_area_scheme_{scheme.id}_{timestamp}.png'
        create_bar(np.array([r.stage_id.name for r in scheme.result_ids]), np.array([float(r.res_area) for r in scheme.result_ids]), '图3 单位面积碳排放强度', save_path_image)
        images.append(f'{base_url}/carbon/static{save_path_image}')
        
        save_path_image = '/src/images/' + f'rough_bar_area_year_scheme_{scheme.id}_{timestamp}.png'
        create_bar(np.array([r.stage_id.name for r in scheme.result_ids]), np.array([float(r.res_area_year) for r in scheme.result_ids]), '图4 平均每年单位面积碳排放强度', save_path_image)
        images.append(f'{base_url}/carbon/static{save_path_image}')

        _logger.info(images)
        # _logger.info(scheme_data)
        

        # 在每个子图中绘制一个图形
        # n = 1
        # for ax in axs.flat:
        #     if n == 1:
        #         x = np.array([float(r.res_all) for r in scheme.result_ids])
        #         labels = np.array([r.stage_id.name for r in scheme.result_ids])
        #         explode = [0 for r in scheme.result_ids]

                # _, l_text, autotexts = ax.pie(x = x, labels=labels, shadow=1,autopct='%.2f%%', explode=explode)

                # # 将饼图中的字体改为白色
                # for autotext in autotexts:
                #     autotext.set_color('white')
                # for t in l_text: 
	            #     t.set_fontproperties(chinese)
                # ax.set_title(f"图1 各单元碳排放占比", loc="center", fontsize=16, fontproperties=chinese)
            # if n == 2:
            #     x = np.array([r.stage_id.name for r in scheme.result_ids])
            #     y = np.array([float(r.res_year) for r in scheme.result_ids])
            #     ax.set_title(f"图2 平均每年碳排放强度", loc="center", fontsize=16, fontproperties=chinese)
            #     ax.bar(x, y, color='#547ccb')
            #     for label in ax.get_xticklabels():
            #         label.set_fontproperties(chinese)
            # if n == 3:
            #     x = np.array([r.stage_id.name for r in scheme.result_ids])
            #     y = np.array([float(r.res_area) for r in scheme.result_ids])
            #     ax.set_title(f"图3 单位面积碳排放强度", loc="center", fontsize=16, fontproperties=chinese)
            #     ax.bar(x, y, color='#547ccb')
            #     for label in ax.get_xticklabels():
            #         label.set_fontproperties(chinese)

            # if n == 4:
            #     x = np.array([r.stage_id.name for r in scheme.result_ids])
            #     y = np.array([float(r.res_area_year) for r in scheme.result_ids])
            #     ax.set_title(f"图4 平均每年单位面积碳排放强度", loc="center", fontsize=16, fontproperties=chinese)
            #     ax.bar(x, y, color='#547ccb')
            #     for label in ax.get_xticklabels():
            #         label.set_fontproperties(chinese)

            # n += 1

        # my_dpi=96
        # plt.figure(figsize=(480/my_dpi,480/my_dpi),dpi=my_dpi)
        # plt.pie(x=np.array([float(r.res_all) for r in scheme.result_ids]),#指定绘图数据
        #         labels=np.array([r.stage_id.name for r in scheme.result_ids]),#为饼图添加标签说明
        #         autopct='%.2f%%'
        #     )
        # plt.set_title(f"图1 各单元碳排放占比", loc="center", fontsize=16, fontproperties=chinese)

        # result.res_all = sum(v) * A_Area / 1000
        # result.res_area = sum(v)
        # result.res_year = sum(v) * A_Area / (A_Year * 1000)
        # result.res_area_year = sum(v)/A_Year
        result_data = []
        for r in scheme.result_ids:
            category_data = []
            for i in json.loads(r.category_result):
                res_all = i.get('res_all')
                res_area = i.get('res_all') * 1000 / A_Area
                res_year = i.get('res_all') / A_Year
                res_area_year = i.get('res_all') * 1000 / (A_Area * A_Year)
                category_data.append({
                    'category': i.get('category'),
                    'res_all': '%.5f'% float(res_all),
                    'res_area': '%.5f'% float(res_area),
                    'res_year': '%.5f'% float(res_year),
                    'res_area_year': '%.5f'% float(res_area_year),
                })
            result_data.append({
                'name':r.stage_id.name,
                'res_all':'%.5f'% float(r.res_all),
                'res_area':'%.5f'% float(r.res_area),
                'res_year':'%.5f'% float(r.res_year),
                'res_area_year':'%.5f'% float(r.res_area_year),
                'category_data': category_data,
            })

        
        # _logger.info(result_data)
        
        # result_data = [{
        #     'name':r.stage_id.name,
        #     'res_all':'%.5f'% float(r.res_all),
        #     'res_area':'%.5f'% float(r.res_area),
        #     'res_year':'%.5f'% float(r.res_year),
        #     'res_area_year':'%.5f'% float(r.res_area_year),
        # } for r in scheme.result_ids]
        host = request.httprequest.host
        return {
            'doc_ids': docids,
            'doc_model': 'carbon.project',
            'docs': docs,
            'project': project,
            'current_time': (datetime.now()+timedelta(hours=8)).strftime('%Y-%m-%d'),
            'scheme_data': scheme_data,
            'life': json.loads(scheme.data).get('A-Year'),
            'result_data': result_data,
            'images': images,
            # 'res_area_image': f'http://**************:8069/carbon/static{save_path_res_area}',
            # 'res_area_year_image': f'http://**************:8069/carbon/static{save_path_res_area_year}',
        }

class ProjectReportRoughCompare(models.AbstractModel):
    _name = 'report.carbon.project_report_rough_compare'

    @api.model
    def _get_report_values(self, docids, data=None):
        path = os.path.dirname(os.path.abspath(__file__)).replace('models','static')
        timestamp = time.time()

        base_url = self.env["ir.config_parameter"].sudo().get_param("server.base.url")
        CarbonProjectScheme = self.env['carbon.project.scheme'].sudo()
        docs = CarbonProjectScheme.browse(docids)
        scheme = docs[0]
        project = docs[0].project_id
        A_Area = float(project.area)
        schemes_data = []

        def get_constructs(data):
            constructs = []

            
            LayerData = data.get('LayerData')
            # _logger.info(data)
            # _logger.info(LayerData)
            for l in LayerData:
                composition_value = l.get('composition_value')
                layer_name = l.get('layer').split('&')[1]
                # _logger.info(layer_name)
                k = layer_name
                v = []
                for c in composition_value:
                    _id = c.get('id')
                    value = c.get('value')
                    composition = self.env['structural.layer.composition'].sudo().browse(_id)
                
            
                    # _logger.info(composition.type)
                    if composition.type == 'fill':
                        unit = composition.unit
                        # _logger.info(f'{composition.name}')
                        # _logger.info(f'{value}')
                        v.append(f'{composition.name}({value}{unit})')
                    if composition.type == 'radio':
                        # _logger.info(f'{composition.name}')
                        # _logger.info(f'{value}')
                        if composition.name == '混合料':
                            pass
                        else:
                            value = self.env['material.life.cycle.inventory'].browse(value).name
                        
                        v.append(f'{composition.name}({value})')

                    if composition.type == 'checkbox':
                        # _logger.info(f'{composition.name}')
                        # _logger.info(f'{value}')
                        if composition.name == '矿料':
                            _l = []
                            for i in value:
                                category = i.get('category')
                                quality_proportion = i.get('quality_proportion')
                                n = self.env['material.life.cycle.inventory'].browse(category).name
                                _l.append(f'{n}{quality_proportion}%')
                            s = '、'.join(_l)
                            v.append(f'矿料({s})')
                        else:
                            if len(value):
                                category = value[0].get('category')
                                number = value[0].get('number')
                                unit = value[0].get('unit')
                                n = self.env['material.life.cycle.inventory'].browse(category).name
                                v.append(f'{n}({number}{unit})')

                        # if composition.name == '混合料':
                        #     pass
                        # else:
                        #     value = self.env['material.life.cycle.inventory'].browse(value).name
                        
                        # v.append(f'{composition.name}({value})')
                
                constructs.append(k + '：  ' + '、'.join(v))
            _logger.info(constructs)
            return constructs
        table_idx = 2
        for sc in docs:
            A_Year = float(json.loads(sc.data).get('A-Year'))
            result_data = []
            for r in sc.result_ids:
                category_data = []
                for i in json.loads(r.category_result):
                    res_all = i.get('res_all')
                    res_area = i.get('res_all') * 1000 / A_Area
                    res_year = i.get('res_all') / A_Year
                    res_area_year = i.get('res_all') * 1000 / (A_Area * A_Year)
                    category_data.append({
                        'category': i.get('category'),
                        'res_all': '%.5f'% float(res_all),
                        'res_area': '%.5f'% float(res_area),
                        'res_year': '%.5f'% float(res_year),
                        'res_area_year': '%.5f'% float(res_area_year),
                    })
                result_data.append({
                    'name':r.stage_id.name,
                    'table_idx':table_idx,
                    'res_all':'%.5f'% float(r.res_all),
                    'res_area':'%.5f'% float(r.res_area),
                    'res_year':'%.5f'% float(r.res_year),
                    'res_area_year':'%.5f'% float(r.res_area_year),
                    'category_data':category_data,
                })
                table_idx += 1
            schemes_data.append({
                'name': sc.name,
                'life': json.loads(sc.data).get('A-Year'),
                'constructs': get_constructs(json.loads(sc.data)),
                'result_data': result_data,
            })

        images = [] 
        for idx, scheme in enumerate(docs):
            _logger.info(scheme.name)
            has_f = False # 是否有负值
            # if '碳汇' in [r.stage_id.name for r in scheme.result_ids]:
            #     has_f = True
            if has_f:
                save_path_image = '/src/images/' + f'rough_two_pie_scheme_{scheme.id}_{timestamp}.png'
                create_two_pie(np.array([float(r.res_all) * 10000 for r in scheme.result_ids if float(r.res_all)>0]),np.array([]),np.array([r.stage_id.name for r in scheme.result_ids if float(r.res_all)>0]),[0 for r in scheme.result_ids if float(r.res_all)>0],f'图{idx+1} 方案{idx+1}各单元碳排放占比', save_path_image)
                images.append(f'{base_url}/carbon/static{save_path_image}')
            else:
                result_ids = scheme.result_ids.filtered(lambda x:x.stage_id.name != '碳汇')
                save_path_image = '/src/images/' + f'rough_pie_scheme_{scheme.id}_{timestamp}.png'
                create_pie(np.array([float(r.res_all) * 10000 for r in result_ids]),np.array([r.stage_id.name for r in result_ids]),[0 for r in result_ids],f'图{idx+1} 方案{idx+1}各单元碳排放占比', save_path_image)
                images.append(f'{base_url}/carbon/static{save_path_image}')


        save_path_image = '/src/images/' + f'rough_compare_bar_all_scheme_{scheme.id}_{timestamp}.png'
        create_bar(np.array([sc.name for sc in docs]), np.array([float(sc.res_all) for sc in docs]), f'图{len(docs)+1} 总碳排放', save_path_image)
        images.append(f'{base_url}/carbon/static{save_path_image}')

        save_path_image = '/src/images/' + f'rough_compare_bar_year_scheme_{scheme.id}_{timestamp}.png'
        create_bar(np.array([sc.name for sc in docs]), np.array([float(sc.res_year) for sc in docs]), f'图{len(docs)+2} 平均每年碳排放强度', save_path_image)
        images.append(f'{base_url}/carbon/static{save_path_image}')

        save_path_image = '/src/images/' + f'rough_compare_bar_area_scheme_{scheme.id}_{timestamp}.png'
        create_bar(np.array([sc.name for sc in docs]), np.array([float(sc.res_area) for sc in docs]), f'图{len(docs)+3} 单位面积碳排放强度', save_path_image)
        images.append(f'{base_url}/carbon/static{save_path_image}')

        save_path_image = '/src/images/' + f'rough_compare_bar_area_year_scheme_{scheme.id}_{timestamp}.png'
        create_bar(np.array([sc.name for sc in docs]), np.array([float(sc.res_area_year) for sc in docs]), f'图{len(docs)+4} 平均每年单位面积碳排放强度', save_path_image)
        images.append(f'{base_url}/carbon/static{save_path_image}')



        _logger.info(images)

        




        

        # chinese = matplotlib.font_manager.FontProperties(fname=path + '/fonts/simhei.ttf')
        # # plt.legend(prop=chinese)
        # from pylab import mpl
        # # 设置显示中文字体
        # # mpl.rcParams["font.family"] = path + '/fonts/simhei.ttf'
        # # mpl.rcParams['font.sans-serif']=[chinese]
        # mpl.rcParams['axes.unicode_minus'] = False
        
        # # 创建一个2x2的网格
        # fig, axs = plt.subplots(4, 1, figsize=(8, 32))
        
        # # 在每个子图中绘制一个图形
        # n = 1
        # for ax in axs.flat:
        #     if n == 1:
        #         x = np.array([float(r.res_all) for r in scheme.result_ids])
        #         labels = np.array([r.stage_id.name for r in scheme.result_ids])
        #         explode = [0 for r in scheme.result_ids]

        #         _, l_text, autotexts = ax.pie(x = x, labels=labels, shadow=1,autopct='%.2f%%', explode=explode)

        #         # 将饼图中的字体改为白色
        #         for autotext in autotexts:
        #             autotext.set_color('white')
        #         for t in l_text: 
	    #             t.set_fontproperties(chinese)
        #         ax.set_title(f"图1 各单元碳排放占比", loc="center", fontsize=16, fontproperties=chinese)
        #     if n == 2:
        #         x = np.array([r.stage_id.name for r in scheme.result_ids])
        #         y = np.array([float(r.res_year) for r in scheme.result_ids])
        #         ax.set_title(f"图2 平均每年碳排放强度", loc="center", fontsize=16, fontproperties=chinese)
        #         ax.bar(x, y, color='#547ccb')
        #         for label in ax.get_xticklabels():
        #             label.set_fontproperties(chinese)
        #     if n == 3:
        #         x = np.array([r.stage_id.name for r in scheme.result_ids])
        #         y = np.array([float(r.res_area) for r in scheme.result_ids])
        #         ax.set_title(f"图3 单位面积碳排放强度", loc="center", fontsize=16, fontproperties=chinese)
        #         ax.bar(x, y, color='#547ccb')
        #         for label in ax.get_xticklabels():
        #             label.set_fontproperties(chinese)

        #     if n == 4:
        #         x = np.array([r.stage_id.name for r in scheme.result_ids])
        #         y = np.array([float(r.res_area_year) for r in scheme.result_ids])
        #         ax.set_title(f"图4 平均每年单位面积碳排放强度", loc="center", fontsize=16, fontproperties=chinese)
        #         ax.bar(x, y, color='#547ccb')
        #         for label in ax.get_xticklabels():
        #             label.set_fontproperties(chinese)

        #     n += 1
        
        

        
        # save_path_image = '/src/images/' + f'scheme_{scheme.id}.png'
        # plt.savefig(path + save_path_image)
        
        result_data = [{
            'name':r.stage_id.name,
            'res_all':'%.5f'% float(r.res_all),
            'res_area':'%.5f'% float(r.res_area),
            'res_year':'%.5f'% float(r.res_year),
            'res_area_year':'%.5f'% float(r.res_area_year),
        } for r in scheme.result_ids]
        host = request.httprequest.host
        return {
            'doc_ids': docids,
            'doc_model': 'carbon.project',
            'docs': docs,
            'schemes_data': schemes_data,
            'project': project,
            'current_time': (datetime.now()+timedelta(hours=8)).strftime('%Y-%m-%d'),
            'life': json.loads(scheme.data).get('A-Year'),
            'result_data': result_data,
            # 'image': f'{base_url}/carbon/static{save_path_image}',
            'images': images,
            # 'res_area_image': f'http://**************:8069/carbon/static{save_path_res_area}',
            # 'res_area_year_image': f'http://**************:8069/carbon/static{save_path_res_area_year}',
        }



class ProjectReportFine(models.AbstractModel):
    _name = 'report.carbon.project_report_fine'

    @api.model
    def _get_report_values(self, docids, data=None):
        path = os.path.dirname(os.path.abspath(__file__)).replace('models','static')
        timestamp = time.time()

        base_url = self.env["ir.config_parameter"].sudo().get_param("server.base.url")
        CarbonProjectScheme = self.env['carbon.project.scheme'].sudo()
        docs = CarbonProjectScheme.browse(docids)
        scheme = docs[0]
        project = docs[0].project_id
        A_Area = float(project.area)
        A_Year = float(project.life)

        images = []

        has_f = False # 是否有负值
        # if '碳汇' in [r.stage_id.name for r in scheme.result_ids]:
        #     has_f = True
        if has_f:
            save_path_image = '/src/images/' + f'rough_two_pie_scheme_{scheme.id}_{timestamp}.png'
            create_two_pie(np.array([float(r.res_all) * 10000 for r in scheme.result_ids if float(r.res_all)>0]),np.array([]),np.array([r.stage_id.name for r in scheme.result_ids if float(r.res_all)>0]),[0 for r in scheme.result_ids if float(r.res_all)>0],'图1 各单元碳排放占比', save_path_image)
            images.append(f'{base_url}/carbon/static{save_path_image}')
        else:
            result_ids = scheme.result_ids.filtered(lambda x:x.stage_id.name != '碳汇')
            save_path_image = '/src/images/' + f'rough_pie_scheme_{scheme.id}_{timestamp}.png'
            create_pie(np.array([float(r.res_all) * 10000 for r in result_ids]),np.array([r.stage_id.name for r in result_ids]),[0 for r in result_ids],'图1 各单元碳排放占比', save_path_image)
            images.append(f'{base_url}/carbon/static{save_path_image}')


        save_path_image = '/src/images/' + f'fine_bar_year_scheme_{scheme.id}_{timestamp}.png'
        create_bar(np.array([r.stage_id.name for r in scheme.result_ids]), np.array([float(r.res_year) for r in scheme.result_ids]), '图2 平均每年碳排放强度', save_path_image)
        images.append(f'{base_url}/carbon/static{save_path_image}')

        save_path_image = '/src/images/' + f'fine_bar_area_scheme_{scheme.id}_{timestamp}.png'
        create_bar(np.array([r.stage_id.name for r in scheme.result_ids]), np.array([float(r.res_area) for r in scheme.result_ids]), '图3 单位面积碳排放强度', save_path_image)
        images.append(f'{base_url}/carbon/static{save_path_image}')
        
        save_path_image = '/src/images/' + f'fine_bar_area_year_scheme_{scheme.id}_{timestamp}.png'
        create_bar(np.array([r.stage_id.name for r in scheme.result_ids]), np.array([float(r.res_area_year) for r in scheme.result_ids]), '图4 平均每年单位面积碳排放强度', save_path_image)
        images.append(f'{base_url}/carbon/static{save_path_image}')

        _logger.info(images)

        detail = json.loads(scheme.data)
        _logger.info(detail)
        # detail_data = {}
        # CarbonStage = self.env['carbon.stage'].sudo()
        # for k,v in detail.items():
        #     stage_name = CarbonStage.browse(int(k)).name
        #     detail_data[stage_name] = v
        # _logger.info(detail_data)

        result_data = []
        for r in scheme.result_ids:
            category_data = []
            for i in json.loads(r.category_result):
                res_all = i.get('res_all')
                res_area = i.get('res_all') * 1000 / A_Area
                res_year = i.get('res_all') / A_Year
                res_area_year = i.get('res_all') * 1000 / (A_Area * A_Year)
                category_data.append({
                    'category': i.get('category'),
                    'res_all': '%.5f'% float(res_all),
                    'res_area': '%.5f'% float(res_area),
                    'res_year': '%.5f'% float(res_year),
                    'res_area_year': '%.5f'% float(res_area_year),
                })
            result_data.append({
                'name':r.stage_id.name,
                'res_all':'%.5f'% float(r.res_all),
                'res_area':'%.5f'% float(r.res_area),
                'res_year':'%.5f'% float(r.res_year),
                'res_area_year':'%.5f'% float(r.res_area_year),
                'detail_data':detail.get(str(r.stage_id.id)),
                'category_data':category_data
            })

        
        # result_data = [{
        #     'name':r.stage_id.name,
        #     'res_all':'%.5f'% float(r.res_all),
        #     'res_area':'%.5f'% float(r.res_area),
        #     'res_year':'%.5f'% float(r.res_year),
        #     'res_area_year':'%.5f'% float(r.res_area_year),
        #     'detail_data':detail.get(str(r.stage_id.id)),
        # } for r in scheme.result_ids]
        _logger.info(result_data)

        host = request.httprequest.host
        return {
            'doc_ids': docids,
            'doc_model': 'carbon.project',
            'docs': docs,
            'project': project,
            'current_time': (datetime.now()+timedelta(hours=8)).strftime('%Y-%m-%d'),
            'result_data': result_data,
            # 'image': f'{base_url}/carbon/static{save_path_image}',
            'images': images,
            # 'res_area_image': f'http://**************:8069/carbon/static{save_path_res_area}',
            # 'res_area_year_image': f'http://**************:8069/carbon/static{save_path_res_area_year}',
        }

   
    
class ProjectReportFineCompare(models.AbstractModel):
    _name = 'report.carbon.project_report_fine_compare'

    @api.model
    def _get_report_values(self, docids, data=None):
        path = os.path.dirname(os.path.abspath(__file__)).replace('models','static')
        timestamp = time.time()

        base_url = self.env["ir.config_parameter"].sudo().get_param("server.base.url")
        CarbonProjectScheme = self.env['carbon.project.scheme'].sudo()
        docs = CarbonProjectScheme.browse(docids)
        scheme = docs[0]
        rough_scheme = docs[1]
        project = docs[0].project_id
        A_Area = float(project.area)
        A_Year = float(project.life)

        images = []

        has_f = False # 是否有负值
        # if '碳汇' in [r.stage_id.name for r in scheme.result_ids]:
        #     has_f = True
        if has_f:
            save_path_image = '/src/images/' + f'rough_two_pie_scheme_{scheme.id}_{timestamp}.png'
            create_two_pie(np.array([float(r.res_all) * 10000 for r in scheme.result_ids if float(r.res_all)>0]),np.array([]),np.array([r.stage_id.name for r in scheme.result_ids if float(r.res_all)>0]),[0 for r in scheme.result_ids if float(r.res_all)>0],'图1 各单元碳排放占比', save_path_image)
            images.append(f'{base_url}/carbon/static{save_path_image}')
        else:
            result_ids = scheme.result_ids.filtered(lambda x:x.stage_id.name != '碳汇')
            save_path_image = '/src/images/' + f'rough_pie_scheme_{scheme.id}_{timestamp}.png'
            create_pie(np.array([float(r.res_all) * 10000 for r in result_ids]),np.array([r.stage_id.name for r in result_ids]),[0 for r in result_ids],'图1 各单元碳排放占比', save_path_image)
            images.append(f'{base_url}/carbon/static{save_path_image}')


        save_path_image = '/src/images/' + f'fine_bar_year_scheme_{scheme.id}_{timestamp}.png'
        create_bar(np.array([r.stage_id.name for r in scheme.result_ids]), np.array([float(r.res_year) for r in scheme.result_ids]), '图2 平均每年碳排放强度', save_path_image)
        images.append(f'{base_url}/carbon/static{save_path_image}')

        save_path_image = '/src/images/' + f'fine_bar_area_scheme_{scheme.id}_{timestamp}.png'
        create_bar(np.array([r.stage_id.name for r in scheme.result_ids]), np.array([float(r.res_area) for r in scheme.result_ids]), '图3 单位面积碳排放强度', save_path_image)
        images.append(f'{base_url}/carbon/static{save_path_image}')
        
        save_path_image = '/src/images/' + f'fine_bar_area_year_scheme_{scheme.id}_{timestamp}.png'
        create_bar(np.array([r.stage_id.name for r in scheme.result_ids]), np.array([float(r.res_area_year) for r in scheme.result_ids]), '图4 平均每年单位面积碳排放强度', save_path_image)
        images.append(f'{base_url}/carbon/static{save_path_image}')

        _logger.info(images)

        detail = json.loads(scheme.data)
        _logger.info(detail)
        # detail_data = {}
        # CarbonStage = self.env['carbon.stage'].sudo()
        # for k,v in detail.items():
        #     stage_name = CarbonStage.browse(int(k)).name
        #     detail_data[stage_name] = v
        # _logger.info(detail_data)

        result_data = []
        for r in scheme.result_ids:
            category_data = []
            for i in json.loads(r.category_result):
                res_all = i.get('res_all')
                res_area = i.get('res_all') * 1000 / A_Area
                res_year = i.get('res_all') / A_Year
                res_area_year = i.get('res_all') * 1000 / (A_Area * A_Year)
                category_data.append({
                    'category': i.get('category'),
                    'res_all': '%.5f'% float(res_all),
                    'res_area': '%.5f'% float(res_area),
                    'res_year': '%.5f'% float(res_year),
                    'res_area_year': '%.5f'% float(res_area_year),
                })
            result_data.append({
                'name':r.stage_id.name,
                'res_all':'%.5f'% float(r.res_all),
                'res_area':'%.5f'% float(r.res_area),
                'res_year':'%.5f'% float(r.res_year),
                'res_area_year':'%.5f'% float(r.res_area_year),
                'detail_data':detail.get(str(r.stage_id.id)),
                'category_data':category_data
            })

        
        # result_data = [{
        #     'name':r.stage_id.name,
        #     'res_all':'%.5f'% float(r.res_all),
        #     'res_area':'%.5f'% float(r.res_area),
        #     'res_year':'%.5f'% float(r.res_year),
        #     'res_area_year':'%.5f'% float(r.res_area_year),
        #     'detail_data':detail.get(str(r.stage_id.id)),
        # } for r in scheme.result_ids]
        _logger.info(result_data)

        

        compare_data = []

        for _type in ['res_all','res_area','res_year','res_area_year']:
            _t_dic = {
                'res_all': '总碳排放',
                'res_area': '单位面积碳排放强度',
                'res_year': '平均每年碳排放强度',
                'res_area_year': '单位面积年均碳排放强度',
            }
            c_data = {
                'type': _t_dic[_type],
                'data': [],
            }
            # 此处需要改stage_ids
            for stage in project.fine_stage_ids:
                scheme_result = scheme.result_ids.filtered(lambda x:x.stage_id.id == stage.id)
                fine_value = '%.2f' % float(scheme_result[_type])
                if stage.name == '养护':
                    value = '/'
                    compare_rate = '/'
                else:
                    rough_scheme_result = rough_scheme.result_ids.filtered(lambda x:x.stage_id.id == stage.id)
                    _logger.info(rough_scheme_result)
                    value = '%.2f' % float(rough_scheme_result[_type])
                    _logger.info('wwwwwwwwwwwwwwwwwwwwwwww')
                    _logger.info(value)
                    if float(value) > 0:
                        compare_rate = '+' +  '%.2f'% float((float(fine_value) - float(value)) / float(value) * 100) if float(fine_value) > float(value) else '%.2f'% float((float(fine_value) - float(value)) / float(value) * 100)
                    else:
                        compare_rate = '/'
                c_data['data'].append({
                    'stage_name': stage.name,
                    'value': value,
                    'fine_value': fine_value,
                    'compare_rate': compare_rate,
                })
            compare_data.append(c_data)

        _logger.info(compare_data)

        

        host = request.httprequest.host
        return {
            'doc_ids': docids,
            'doc_model': 'carbon.project',
            'docs': docs,
            'project': project,
            'current_time': (datetime.now()+timedelta(hours=8)).strftime('%Y-%m-%d'),
            'result_data': result_data,
            'compare_data': compare_data,
            # 'image': f'{base_url}/carbon/static{save_path_image}',
            'images': images,
            # 'res_area_image': f'http://**************:8069/carbon/static{save_path_res_area}',
            # 'res_area_year_image': f'http://**************:8069/carbon/static{save_path_res_area_year}',
        }