12.0.2.0.1
~~~~~~~~~~

* _validator_...() methods can now return a cerberus ``Validator`` object
  instead of a schema dictionnary, for additional flexibility (e.g. allowing
  validator options such as ``allow_unknown``).

12.0.2.0.0
~~~~~~~~~~

* Licence changed from AGPL-3 to LGPL-3

12.0.1.0.1
~~~~~~~~~~

* Fix issue when rendering the jsonapi documentation if no documentation is
  provided on a method part of the REST api.

12.0.1.0.0
~~~~~~~~~~

First official version. The addon has been incubated into the
`Shopinvader repository <https://github.com/akretion/odoo-shopinvader>`_ from
Akretion. For more information you need to look at the git log.
