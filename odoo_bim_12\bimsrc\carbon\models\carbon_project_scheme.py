# -*- coding: utf-8 -*-
from odoo import fields, api, models
from odoo.exceptions import UserError, ValidationError
import os
import logging
_logger = logging.getLogger(__name__)
class CarbonProjectScheme(models.Model):
    """
    项目方案
    """
    _name = 'carbon.project.scheme'
    _description = "项目方案"
    _rec_name = 'name'

  

    project_id = fields.Many2one('carbon.project',string='所属项目')
    select = fields.Boolean(string='是否选择')
    name = fields.Char(string='方案名称')
    mode = fields.Selection(
            selection=[
                ('rough', '方案比选'),
                ('fine', '碳排放核算')
            ],
            default='rough', required=True)
    data = fields.Text(string='填报数据')
    is_completed = fields.Bo<PERSON>an(string='是否完成计算', compute='com_is_completed')
    res_all = fields.Char(string='总碳排放', compute='com_res')
    res_area = fields.Char(string='单位面积碳排放强度', compute='com_res')
    res_year = fields.Char(string='平均每年碳排放强度', compute='com_res')
    res_area_year = fields.Char(string='单位面积年均碳排放强度', compute='com_res')
    result_ids = fields.One2many('carbon.project.result','scheme_id', '计算结果')


    def del_report_images(self):

        def delete_files_in_folder(folder_path):
            for filename in os.listdir(folder_path):
                file_path = os.path.join(folder_path, filename)
                if os.path.isfile(file_path):
                    os.remove(file_path)

        path = os.path.dirname(os.path.abspath(__file__)).replace('models','static') + os.sep + 'src' + os.sep + 'images'
        _logger.info(path)

        delete_files_in_folder(path)

    def com_res(self):
        for record in self:
            record.res_all = sum([float(r.res_all) for r in record.result_ids])
            record.res_area = sum([float(r.res_area) for r in record.result_ids])
            record.res_year = sum([float(r.res_year) for r in record.result_ids])
            record.res_area_year = sum([float(r.res_area_year) for r in record.result_ids])


    def com_is_completed(self):
        # 此处需要改stage_ids
        for record in self:
            _stage_ids = record.project_id.stage_ids if record.mode == 'rough' else record.project_id.fine_stage_ids
            if sorted(_stage_ids.ids, key=lambda x:x) == sorted([r.stage_id.id for r in record.result_ids], key=lambda x:x):
                record.is_completed = True
            else:
                record.is_completed = False


    def calc_rough(self):
        import json
        import logging
        _logger = logging.getLogger(__name__)

        StructuralLayerComposition = self.env['structural.layer.composition'].sudo()
        MaterialLifeCycleInventory = self.env['material.life.cycle.inventory'].sudo()
        CarbonLifeCycleInventory = self.env['carbon.life.cycle.inventory'].sudo()
        CarbonStage = self.env['carbon.stage'].sudo()
        CarbonProjectResult = self.env['carbon.project.result'].sudo()


        data = json.loads(self.data)
        A_YJ = float(data.get('A-YJ'))
        # _logger.info(A_YJ)

        # A_Year = float(self.project_id.life)
        A_Year = float(data.get('A-Year'))
        A_Area = float(self.project_id.area)

        stage_names = self.project_id.stage_ids.mapped('name')

        stage_data = {}
        for s in self.project_id.stage_ids:
            stage_data[s.name] = []
        

        YCL_LIST = []
        YS_LIST = []
        SG_LIST = []



        A_ZHD_LIST = []

        LayerData = data.get('LayerData')
        F_RES_PERUNIT_YCL = 0 #原材料负值
        for l in LayerData:
            layerid = l.get('layer').split('&')[0]
            layername = l.get('layer').split('&')[1]
            # _logger.info(layername)
            if layername == '沥青混凝土面层':
                composition_value = l.get('composition_value')
                for c in composition_value:
                    composition_id = c.get('id')
                    composition_value = c.get('value')
                    #查询成分编码
                    composition_code = StructuralLayerComposition.browse(composition_id).code
                    _logger.info(composition_code)
                    if composition_code == 'P-YSB':
                        P_YSB = float(composition_value)
                    if composition_code == 'A-LQMH':
                        A_LQMH = float(composition_value)
                    if composition_code == 'A-LQHD':
                        A_LQHD = float(composition_value)
                        A_ZHD_LIST.append(A_LQHD)
                    if composition_code == 'P-KF':
                        P_KF = float(composition_value)

                    if composition_code == 'F-JHL':
                        F_JHL = float(MaterialLifeCycleInventory.browse(composition_value).carbon_factor)
                        JHL_HS = CarbonLifeCycleInventory.search([('material_id','=',composition_value)] ,limit=1)
                        if JHL_HS:
                            F_JHL_HS = float(JHL_HS.carbon_factor)
                    if composition_code == 'F-KL':
                        KL = composition_value
                        _logger.info(KL)
                    if composition_code == 'T-HHL':
                        T_HHL = composition_value


                # 计算原材料单元
                if '原材料' in stage_names:
                    # s1 = P_YSB/(1+P_YSB) * A_LQHD/100 * A_LQMH * F_JHL / 1000

                    # s2 = 1/(1+P_YSB) * A_LQHD/100 * A_LQMH/ 1000

                    KL_LIST = []
                    for kl in KL:
                        F_KL = float(MaterialLifeCycleInventory.browse(kl.get('category')).carbon_factor)
                        P_KL = float(kl.get('quality_proportion'))
                        KL_LIST.append(P_KL/100 * F_KL)

                    # s3 = (1 - P_KF) * sum(KL_LIST) + 7.355 * P_KF
                   
                    # RES_PERUNIT_YCL_AC = s1 + s2 * s3
                    UNIT_AREA_AC_YCL_LQ = (P_YSB/100 * A_LQHD/100000 * A_LQMH * F_JHL) / (1 + P_YSB/100)
                    UNIT_AREA_AC_YCL_KL = (A_LQHD/100) * A_LQMH/1000 * (1 - P_KF/100) * sum(KL_LIST) / (1 + P_YSB/100)
                    UNIT_AREA_AC_YCL_KF = (A_LQHD/100) * A_LQMH/1000 * P_KF/100 * 7.355 / (1 + P_YSB/100)

                    _logger.info(f'沥青混凝土面层-原材料单元-沥青类别:{UNIT_AREA_AC_YCL_LQ}')
                    _logger.info(f'沥青混凝土面层-原材料单元-矿料类别:{UNIT_AREA_AC_YCL_KL}')
                    _logger.info(f'沥青混凝土面层-原材料单元-矿粉类别:{UNIT_AREA_AC_YCL_KF}')

                    stage_data['原材料'].append({
                        'category': '沥青',
                        'res_area': UNIT_AREA_AC_YCL_LQ,
                    })
                    stage_data['原材料'].append({
                        'category': '矿料',
                        'res_area': UNIT_AREA_AC_YCL_KL,
                    })
                    stage_data['原材料'].append({
                        'category': '矿粉',
                        'res_area': UNIT_AREA_AC_YCL_KF,
                    })

        

                # 计算施工单元
                if '施工' in stage_names:
                    # 计算拌合环节
                    if T_HHL in ['AC','SMA']:
                        UNIT_AREA_AC_SG_BH = 0.6782475 * A_LQHD
                    elif T_HHL == 'EA-30': 
                        UNIT_AREA_AC_SG_BH = 0.6790058 * A_LQHD
                    elif T_HHL == 'EA-40': 
                        UNIT_AREA_AC_SG_BH = 0.6708970 * A_LQHD
                    else: #GA
                        UNIT_AREA_AC_SG_BH = 0.7482419 * A_LQHD
                    
                    _logger.info(f'沥青混凝土面层-施工单元-拌合类别:{UNIT_AREA_AC_SG_BH}')
                    
                    stage_data['施工'].append({
                        'category': '拌合',
                        'res_area': UNIT_AREA_AC_SG_BH,
                    })
            

                    # 计算摊铺环节
                    if T_HHL in ['AC','SMA']:
                        UNIT_AREA_AC_SG_TP = 0.0089219 * A_LQHD
                    elif 'EA' in T_HHL: 
                        UNIT_AREA_AC_SG_TP = 0.0114383 * A_LQHD
                    else: #GA
                        UNIT_AREA_AC_SG_TP = 0.0162081 * A_LQHD
                    
                    _logger.info(f'沥青混凝土面层-施工单元-摊铺类别:{UNIT_AREA_AC_SG_TP}')
                    stage_data['施工'].append({
                        'category': '摊铺',
                        'res_area': UNIT_AREA_AC_SG_TP,
                    })
            

                    # 计算碾压环节
                    if T_HHL in ['AC','SMA']:
                        UNIT_AREA_AC_SG_NY = 0.0344924 * A_LQHD
                    elif 'EA' in T_HHL: 
                        UNIT_AREA_AC_SG_NY = 0.0263297 * A_LQHD
                    else: #GA
                        UNIT_AREA_AC_SG_NY = 0
                    
                    _logger.info(f'沥青混凝土面层-施工单元-碾压类别:{UNIT_AREA_AC_SG_NY}')
                    stage_data['施工'].append({
                        'category': '碾压',
                        'res_area': UNIT_AREA_AC_SG_NY,
                    })


                    # 计算现场运输
                    if T_HHL in ['AC','SMA']:
                        UNIT_AREA_AC_SG_XCYS = 214.83 * (6.91 + (A_YJ - 1) * 0.29) * A_LQHD / 100000
                    elif 'EA' in T_HHL: 
                        UNIT_AREA_AC_SG_XCYS = 214.83 * (6.91 + (A_YJ - 1) * 0.29) * A_LQHD / 100000
                    else: #GA
                        UNIT_AREA_AC_SG_XCYS = 9 * A_LQHD * A_LQMH * A_YJ / 10000000
                    _logger.info(f'沥青混凝土面层-施工单元-现场运输类别:{UNIT_AREA_AC_SG_XCYS}')

                    stage_data['施工'].append({
                        'category': '现场运输',
                        'res_area': UNIT_AREA_AC_SG_XCYS,
                    })
                 

                if '拆除' in stage_names:
                    # 计算路面拆除类别 pass
                    # 计算废料外运类别，同现场运输
                    if T_HHL in ['AC','SMA']:
                        UNIT_AREA_AC_CC_FLWY = 214.83 * (6.91 + (A_YJ - 1) * 0.29) * A_LQHD / 100000
                    elif 'EA' in T_HHL: 
                        UNIT_AREA_AC_CC_FLWY = 214.83 * (6.91 + (A_YJ - 1) * 0.29) * A_LQHD / 100000
                    else: #GA
                        UNIT_AREA_AC_CC_FLWY = 9 * A_LQHD * A_LQMH * A_YJ / 10000000
                    _logger.info(f'沥青混凝土面层-拆除单元-废料外运类别:{UNIT_AREA_AC_CC_FLWY}')
                    
                    stage_data['拆除'].append({
                        'category': '废料外运',
                        'res_area': UNIT_AREA_AC_CC_FLWY,
                    })

                if '碳汇' in stage_names:
                    #计算沥青，若有对应结合料的回收材料，则计算
                    UNIT_AREA_AC_TH_LQ = 0
                    if JHL_HS:
                        UNIT_AREA_AC_TH_LQ = (P_YSB/100 * A_LQHD/100000 * A_LQMH * F_JHL_HS) / (1 + P_YSB/100)
                    _logger.info(f'沥青混凝土面层-碳汇单元-沥青类别:{UNIT_AREA_AC_TH_LQ}')

                    stage_data['碳汇'].append({
                        'category': '沥青',
                        'res_area': UNIT_AREA_AC_TH_LQ,
                    })

                    #计算矿料，循环遍历
                    UNIT_AREA_AC_TH_KL = 0
                    KL_HS_LIST = []
                    for kl in KL:
                        KL_HS = CarbonLifeCycleInventory.search([('material_id','=',kl.get('category'))] ,limit=1)
                        if KL_HS:
                            F_KL_HS = float(KL_HS.carbon_factor)
                            P_KL = float(kl.get('quality_proportion'))
                            KL_HS_LIST.append(P_KL/100 * F_KL_HS)
                    if len(KL_HS_LIST):
                        UNIT_AREA_AC_TH_KL = (A_LQHD/100) * A_LQMH/1000 * (1 - P_KF/100) * sum(KL_HS_LIST) / (1 + P_YSB/100)

                    _logger.info(f'沥青混凝土面层-碳汇单元-矿料类别:{UNIT_AREA_AC_TH_KL}')
                    
                    stage_data['碳汇'].append({
                        'category': '矿料',
                        'res_area': UNIT_AREA_AC_TH_KL,
                    })



                    


            
            if layername == '水泥混凝土面层':
                composition_value = l.get('composition_value')
                for c in composition_value:
                    composition_id = c.get('id')
                    composition_value = c.get('value')
                    #查询成分编码
                    composition_code = StructuralLayerComposition.browse(composition_id).code
                    _logger.info(composition_code)
                    if composition_code == 'F-KL':
                        KL = composition_value
                        _logger.info(KL)
                    if composition_code == 'F-SN':
                        F_SN = float(MaterialLifeCycleInventory.browse(composition_value).carbon_factor)
                        SN_HS = CarbonLifeCycleInventory.search([('material_id','=',composition_value)] ,limit=1)
                        if SN_HS:
                            F_SN_HS = float(SN_HS.carbon_factor)
                    if composition_code == 'P-SN':
                        P_SN = float(composition_value)/100
                    if composition_code == 'A-SNHM':
                        A_SNHM = float(composition_value)
                    if composition_code == 'A-SNHD':
                        A_SNHD = float(composition_value)
                        A_ZHD_LIST.append(A_SNHD)

                # 计算原材料单元
                if '原材料' in stage_names:
                    KL_LIST = []
                    _logger.info(KL)
                    for kl in KL:
                        F_KL = float(MaterialLifeCycleInventory.browse(kl.get('category')).carbon_factor)
                        P_KL = float(kl.get('quality_proportion'))
                        KL_LIST.append(P_KL/100 * F_KL)

                    UNIT_AREA_CC_YCL_SN = A_SNHD/100 * A_SNHM * P_SN/100 * F_SN /1000
                    UNIT_AREA_CC_YCL_KL = (A_SNHD/100) * A_SNHM/1000 * (1 - P_SN/100) * sum(KL_LIST)

                    _logger.info(f'水泥混凝土面层-原材料单元-水泥类别:{UNIT_AREA_CC_YCL_SN}')
                    _logger.info(f'水泥混凝土面层-原材料单元-矿料类别:{UNIT_AREA_CC_YCL_KL}')
                    
                    stage_data['原材料'].append({
                        'category': '水泥',
                        'res_area': UNIT_AREA_CC_YCL_SN,
                    })
                    stage_data['原材料'].append({
                        'category': '矿料',
                        'res_area': UNIT_AREA_CC_YCL_KL,
                    })

                

                # 计算施工单元
                if '施工' in stage_names:
                     # 计算现场运输
                    UNIT_AREA_CC_SG_XCYS = 214.83 * (5.41 + (A_YJ - 1) * 0.34) * A_SNHD / 100000
                    _logger.info(f'水泥混凝土面层-施工单元-现场运输类别:{UNIT_AREA_CC_SG_XCYS}')

                    stage_data['施工'].append({
                        'category': '现场运输',
                        'res_area': UNIT_AREA_CC_SG_XCYS,
                    })

                if '拆除' in stage_names:
                    # 计算路面拆除类别 pass
                    # 计算废料外运类别，同现场运输
                    UNIT_AREA_CC_CC_FLWY = 214.83 * (5.41 + (A_YJ - 1) * 0.34) * A_SNHD / 100000
                    _logger.info(f'水泥混凝土面层-拆除单元-废料外运类别:{UNIT_AREA_CC_CC_FLWY}')

                    stage_data['拆除'].append({
                        'category': '废料外运',
                        'res_area': UNIT_AREA_CC_CC_FLWY,
                    })
                
                if '碳汇' in stage_names:
                    #计算水泥，若有对应水泥的回收材料，则计算
                    UNIT_AREA_CC_TH_SN = 0
                    if SN_HS:
                        UNIT_AREA_CC_TH_SN = A_SNHD/100 * A_SNHM * P_SN/100 * FF_SN_HS/1000
                    _logger.info(f'水泥混凝土面层-碳汇单元-水泥类别:{UNIT_AREA_CC_TH_SN}')

                    stage_data['碳汇'].append({
                        'category': '水泥',
                        'res_area': UNIT_AREA_CC_TH_SN,
                    })

                    #计算矿料，循环遍历
                    UNIT_AREA_CC_TH_KL = 0
                    KL_HS_LIST = []
                    for kl in KL:
                        KL_HS = CarbonLifeCycleInventory.search([('material_id','=',kl.get('category'))] ,limit=1)
                        if KL_HS:
                            F_KL_HS = float(KL_HS.carbon_factor)
                            P_KL = float(kl.get('quality_proportion'))
                            KL_HS_LIST.append(P_KL/100 * F_KL_HS)
                    if len(KL_HS_LIST):
                        UNIT_AREA_CC_TH_KL = (A_SNHD/100) * A_SNHM/1000 * (1 - P_SN/100) * sum(KL_HS_LIST)

                    _logger.info(f'水泥混凝土面层-碳汇单元-矿料类别:{UNIT_AREA_CC_TH_KL}')

                    stage_data['碳汇'].append({
                        'category': '矿料',
                        'res_area': UNIT_AREA_CC_TH_KL,
                    })

            if layername == '功能层':
                composition_value = l.get('composition_value')
                for c in composition_value:
                    composition_id = c.get('id')
                    composition_value = c.get('value')
                    #查询成分编码
                    composition_code = StructuralLayerComposition.browse(composition_id).code
                    _logger.info(composition_code)
                    if composition_code == 'F-GNC':
                        _logger.info(MaterialLifeCycleInventory.browse(composition_value))
                        if composition_value:
                            F_GNC = float(MaterialLifeCycleInventory.browse(composition_value[0].get('category')).carbon_factor)
                            GNC_HS = CarbonLifeCycleInventory.search([('material_id','=',composition_value[0].get('category'))] ,limit=1)
                            if GNC_HS:
                                F_GNC_HS = float(GNC_HS.carbon_factor)
                                F_GNC_NAME_HS = GNC_HS.name
                            F_GNC_NAME = MaterialLifeCycleInventory.browse(composition_value[0].get('category')).name
                    # if composition_code == 'A-GNC':
                            A_GNC = float(composition_value[0].get('number'))
                            Unit_GNC = composition_value[0].get('unit')
                        else:
                            F_GNC = A_GNC = 0

                # 计算原材料单元
                if '原材料' in stage_names:
                    UNIT_AREA_GNC_YCL_CATEGORY = F_GNC * A_GNC
                    if Unit_GNC == 'cm':
                        UNIT_AREA_GNC_YCL_CATEGORY = F_GNC * A_GNC / 100

                    _logger.info(f'功能层-原材料单元-{F_GNC_NAME}类别:{UNIT_AREA_GNC_YCL_CATEGORY}')

                    stage_data['原材料'].append({
                        'category': F_GNC_NAME,
                        'res_area': UNIT_AREA_GNC_YCL_CATEGORY,
                    })

                 
                if '施工' in stage_names:
                    pass

                if '拆除' in stage_names:
                    pass

                if '碳汇' in stage_names:
                    UNIT_AREA_GNC_TH_CATEGORY = 0
                    if GNC_HS:
                        UNIT_AREA_GNC_TH_CATEGORY = F_GNC_HS * A_GNC
                        if Unit_GNC == 'cm':
                            UNIT_AREA_GNC_TH_CATEGORY = F_GNC_HS * A_GNC / 100
                    _logger.info(f'功能层-碳汇单元-{F_GNC_NAME}类别:{UNIT_AREA_GNC_TH_CATEGORY}')

                    stage_data['碳汇'].append({
                        'category': F_GNC_NAME,
                        'res_area': UNIT_AREA_GNC_TH_CATEGORY,
                    })

            if layername == '基层/底基层':
                composition_value = l.get('composition_value')
                for c in composition_value:
                    composition_id = c.get('id')
                    composition_value = c.get('value')
                    #查询成分编码
                    composition_code = StructuralLayerComposition.browse(composition_id).code
                    _logger.info(composition_code)
                    if composition_code == 'F-JC':
                        if composition_value:
                            F_JC = float(MaterialLifeCycleInventory.browse(composition_value[0].get('category')).carbon_factor)
                            F_JC_NAME = MaterialLifeCycleInventory.browse(composition_value[0].get('category')).name
                            JC_HS = CarbonLifeCycleInventory.search([('material_id','=',composition_value[0].get('category'))] ,limit=1)
                            if JC_HS:
                                F_JC_HS = float(JCC_HS.carbon_factor)
                                F_JC_NAME_HS = JC_HS.name
                        # if composition_code == 'A-JC':
                            A_JC = float(composition_value[0].get('number'))
                        else:
                            F_JC_NAME = ''
                            F_JC = A_JC = 0

    
                # 计算原材料单元
                if '原材料' in stage_names:
                    UNIT_AREA_BL_YCL_CATEGORY = F_JC * A_JC / 100
                    _logger.info(f'基层/底基层-原材料单元-{F_JC_NAME}类别:{UNIT_AREA_BL_YCL_CATEGORY}')

                    stage_data['原材料'].append({
                        'category': F_JC_NAME,
                        'res_area': UNIT_AREA_BL_YCL_CATEGORY,
                    })

                if '施工' in stage_names:
                    # 计算现场运输
                    UNIT_AREA_BL_SG_XCYS = 214.83 * (4.54 + (A_YJ - 1) * 0.23) * A_JC / 100000
                    _logger.info(f'基层/底基层-施工单元-现场运输类别:{UNIT_AREA_BL_SG_XCYS}')

                    stage_data['施工'].append({
                        'category': '现场运输',
                        'res_area': UNIT_AREA_BL_SG_XCYS,
                    })

                if '拆除' in stage_names:
                    UNIT_AREA_BL_CC_FLWY = 214.83 * (4.54 + (A_YJ - 1) * 0.23) * A_JC / 100000
                    _logger.info(f'基层/底基层-拆除单元-废料外运类别:{UNIT_AREA_BL_CC_FLWY}')

                    stage_data['拆除'].append({
                        'category': '废料外运',
                        'res_area': UNIT_AREA_BL_CC_FLWY,
                    })

                if '碳汇' in stage_names:
                    UNIT_AREA_BL_TH_CATEGORY = 0
                    if JC_HS:
                        UNIT_AREA_BL_TH_CATEGORY = F_JC_HS * A_JC / 100
                    _logger.info(f'基层/底基层-碳汇单元-{F_JC_NAME}类别:{UNIT_AREA_BL_TH_CATEGORY}')

                    stage_data['碳汇'].append({
                        'category': F_JC_NAME,
                        'res_area': UNIT_AREA_BL_TH_CATEGORY,
                    })


        A_ZHD = sum(A_ZHD_LIST)
        # 计算拆除单元的路面拆除类别
        if '拆除' in stage_names:
            UNIT_AREA_ALL_CC_LMCC = 0.38398 if A_ZHD <= 5 else (383.98 + (A_ZHD - 5) * 77.98) / 1000
            _logger.info(f'所有层-拆除单元-路面拆除类别:{UNIT_AREA_ALL_CC_LMCC}')

            stage_data['拆除'].append({
                'category': '路面拆除',
                'res_area': UNIT_AREA_ALL_CC_LMCC,
            })



        # _logger.info(f'原材料单元:{sum(YCL_LIST)}')
        # # _logger.info(f'运输单元:{sum(YS_LIST)}')
        # _logger.info(f'施工单元:{sum(SG_LIST)}')

        _logger.info(stage_data)
        for k,v in stage_data.items():
            s_id = CarbonStage.search([('name','=',k)], limit=1).id
            _logger.info(s_id)
            _logger.info(v)

            result = CarbonProjectResult.search([('scheme_id','=',self.id),('stage_id','=',s_id)])
            if not result:
                result = CarbonProjectResult.create({
                    'scheme_id':self.id,
                    'stage_id':s_id,
                })
            
            sumv= sum([i.get('res_area') for i in v])
            
            result.res_all = sumv * A_Area / 1000
            result.res_area = sumv
            result.res_year = sumv * A_Area / (A_Year * 1000)
            result.res_area_year = sumv/A_Year

            category_dic = {}
            for i in v:
                if i.get('category') not in category_dic.keys():
                    category_dic[i.get('category')] = i.get('res_area')  * A_Area / 1000
                else:
                    category_dic[i.get('category')] += i.get('res_area')  * A_Area / 1000
            
            category_result = [{
                'category': k,
                'res_all': v,
            } for k,v in category_dic.items() if v != 0]
            
            result.category_result = json.dumps(category_result, ensure_ascii=False)
            
                


                









