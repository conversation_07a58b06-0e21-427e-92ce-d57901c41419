# -*- coding: utf-8 -*-
from odoo import fields, api, models
from odoo.exceptions import UserError, ValidationError

class LifeCycleInventory(models.Model):
    """
    生命周期清单
    """
    _name = 'life.cycle.inventory'
    _description = "生命周期清单"
    _rec_name = 'name'

  

    is_active = fields.Boolean(string='是否生效')
    name = fields.Char(string='清单名称', required=True)
    remark = fields.Char(string='备注')
    user_id = fields.Many2one('res.users', string='所属账号', required=True)


    def bind_user_default_data(self, user_id, user_name):
        # user_id = 2
        import os
        path = os.path.dirname(os.path.abspath(__file__)).replace('models', 'data')
        # print(path)
        import xlrd
        bok = xlrd.open_workbook(path + '/default_inventory_data.xlsx')
        sheets = bok.sheets()
        # print(len(sheets))

        LifeCycleInventory = self.env['life.cycle.inventory'].sudo()
        LifeCycleInventoryType = self.env['life.cycle.inventory.type'].sudo()
        MaterialLifeCycleInventory = self.env['material.life.cycle.inventory'].sudo()
        MechanicalLifeCycleInventory = self.env['mechanical.life.cycle.inventory'].sudo()
        MaintenanceLifeCycleInventory = self.env['maintenance.life.cycle.inventory'].sudo()
        CarbonLifeCycleInventory = self.env['carbon.life.cycle.inventory'].sudo()

        inventory_id = LifeCycleInventory.create({
            'user_id': user_id,
            'name': f'{user_name}-生命周期清单(默认)',
            'remark': '系统默认配置',
            'is_active': True,
        }).id
        # print(inventory_id)
        category_dic = {
            '材料': 'material',
            '运输机械': 'ys_mechanical',
            '施工机械': 'sg_mechanical',
            '拆除机械': 'cc_mechanical',
            '养护': 'maintenance',
            '碳汇': 'carbon',
        }
        type_name_to_id = {}
        material_name_to_id = {}
        for i,sheet in enumerate(sheets):
            if i == 0:
                for j in range(0,sheet.nrows):
                    if j>0:
                        row = sheet.row_values(j)
                        type_id = LifeCycleInventoryType.create({
                            'inventory_id': inventory_id,
                            'name': row[0],
                            'category': category_dic[row[1]],
                            'unit_ids': [(6,0,[int(i) for i in row[2].split(',')])],
                        }).id
                        type_name_to_id[row[0]] = type_id
            if i == 1:
                # print(type_name_to_id)
                for j in range(0,sheet.nrows):
                    if j>0:
                        row = sheet.row_values(j)
                        material_id = MaterialLifeCycleInventory.create({
                            'inventory_id': inventory_id,
                            'composition_id': row[1],
                            'type_id': type_name_to_id[row[0]],
                            'name': row[2],
                            'carbon_factor': row[3],
                            'unit_id': row[4],
                        }).id
                        material_name_to_id[row[2]] = material_id
            if i == 2:
                for j in range(0,sheet.nrows):
                    if j>0:
                        row = sheet.row_values(j)
                        MechanicalLifeCycleInventory.create({
                            'inventory_id': inventory_id,
                            'type_id': type_name_to_id[row[0]],
                            'name': row[1],
                            'carbon_factor': row[2],
                            'unit_id': row[3],
                        })
            if i == 3:
                for j in range(0,sheet.nrows):
                    if j>0:
                        row = sheet.row_values(j)
                        MaintenanceLifeCycleInventory.create({
                            'inventory_id': inventory_id,
                            'type_id': type_name_to_id[row[0]],
                            'name': row[1],
                            'remark': row[2],
                            'carbon_factor': row[3],
                            'unit_id': row[4],
                        })
            if i == 4:
                for j in range(0,sheet.nrows):
                    if j>0:
                        row = sheet.row_values(j)
                        CarbonLifeCycleInventory.create({
                            'inventory_id': inventory_id,
                            'type_id': type_name_to_id[row[0]],
                            'material_id': material_name_to_id[row[1]] if row[1] else False,
                            'name': row[2],
                            'carbon_factor': row[3],
                            'remark': row[4],
                            'unit_id': row[5],
                        })
    
